var Q=function(D){return Q=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(U){return typeof U}:function(U){return U&&typeof Symbol=="function"&&U.constructor===Symbol&&U!==Symbol.prototype?"symbol":typeof U},Q(D)},W=function(D,U){var J=Object.keys(D);if(Object.getOwnPropertySymbols){var X=Object.getOwnPropertySymbols(D);U&&(X=X.filter(function(x){return Object.getOwnPropertyDescriptor(D,x).enumerable})),J.push.apply(J,X)}return J},K=function(D){for(var U=1;U<arguments.length;U++){var J=arguments[U]!=null?arguments[U]:{};U%2?W(Object(J),!0).forEach(function(X){A0(D,X,J[X])}):Object.getOwnPropertyDescriptors?Object.defineProperties(D,Object.getOwnPropertyDescriptors(J)):W(Object(J)).forEach(function(X){Object.defineProperty(D,X,Object.getOwnPropertyDescriptor(J,X))})}return D},A0=function(D,U,J){if(U=B0(U),U in D)Object.defineProperty(D,U,{value:J,enumerable:!0,configurable:!0,writable:!0});else D[U]=J;return D},B0=function(D){var U=E0(D,"string");return Q(U)=="symbol"?U:String(U)},E0=function(D,U){if(Q(D)!="object"||!D)return D;var J=D[Symbol.toPrimitive];if(J!==void 0){var X=J.call(D,U||"default");if(Q(X)!="object")return X;throw new TypeError("@@toPrimitive must return a primitive value.")}return(U==="string"?String:Number)(D)};(function(D){var U=Object.defineProperty,J=function A(E,C){for(var B in C)U(E,B,{get:C[B],enumerable:!0,configurable:!0,set:function H(G){return C[B]=function(){return G}}})},X={lessThanXSeconds:{one:{standalone:"\u043C\u0430\u045A\u0435 \u043E\u0434 1 \u0441\u0435\u043A\u0443\u043D\u0434\u0435",withPrepositionAgo:"\u043C\u0430\u045A\u0435 \u043E\u0434 1 \u0441\u0435\u043A\u0443\u043D\u0434\u0435",withPrepositionIn:"\u043C\u0430\u045A\u0435 \u043E\u0434 1 \u0441\u0435\u043A\u0443\u043D\u0434\u0443"},dual:"\u043C\u0430\u045A\u0435 \u043E\u0434 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0435",other:"\u043C\u0430\u045A\u0435 \u043E\u0434 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0438"},xSeconds:{one:{standalone:"1 \u0441\u0435\u043A\u0443\u043D\u0434\u0430",withPrepositionAgo:"1 \u0441\u0435\u043A\u0443\u043D\u0434\u0435",withPrepositionIn:"1 \u0441\u0435\u043A\u0443\u043D\u0434\u0443"},dual:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0435",other:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0438"},halfAMinute:"\u043F\u043E\u043B\u0430 \u043C\u0438\u043D\u0443\u0442\u0435",lessThanXMinutes:{one:{standalone:"\u043C\u0430\u045A\u0435 \u043E\u0434 1 \u043C\u0438\u043D\u0443\u0442\u0435",withPrepositionAgo:"\u043C\u0430\u045A\u0435 \u043E\u0434 1 \u043C\u0438\u043D\u0443\u0442\u0435",withPrepositionIn:"\u043C\u0430\u045A\u0435 \u043E\u0434 1 \u043C\u0438\u043D\u0443\u0442\u0443"},dual:"\u043C\u0430\u045A\u0435 \u043E\u0434 {{count}} \u043C\u0438\u043D\u0443\u0442\u0435",other:"\u043C\u0430\u045A\u0435 \u043E\u0434 {{count}} \u043C\u0438\u043D\u0443\u0442\u0430"},xMinutes:{one:{standalone:"1 \u043C\u0438\u043D\u0443\u0442\u0430",withPrepositionAgo:"1 \u043C\u0438\u043D\u0443\u0442\u0435",withPrepositionIn:"1 \u043C\u0438\u043D\u0443\u0442\u0443"},dual:"{{count}} \u043C\u0438\u043D\u0443\u0442\u0435",other:"{{count}} \u043C\u0438\u043D\u0443\u0442\u0430"},aboutXHours:{one:{standalone:"\u043E\u043A\u043E 1 \u0441\u0430\u0442",withPrepositionAgo:"\u043E\u043A\u043E 1 \u0441\u0430\u0442",withPrepositionIn:"\u043E\u043A\u043E 1 \u0441\u0430\u0442"},dual:"\u043E\u043A\u043E {{count}} \u0441\u0430\u0442\u0430",other:"\u043E\u043A\u043E {{count}} \u0441\u0430\u0442\u0438"},xHours:{one:{standalone:"1 \u0441\u0430\u0442",withPrepositionAgo:"1 \u0441\u0430\u0442",withPrepositionIn:"1 \u0441\u0430\u0442"},dual:"{{count}} \u0441\u0430\u0442\u0430",other:"{{count}} \u0441\u0430\u0442\u0438"},xDays:{one:{standalone:"1 \u0434\u0430\u043D",withPrepositionAgo:"1 \u0434\u0430\u043D",withPrepositionIn:"1 \u0434\u0430\u043D"},dual:"{{count}} \u0434\u0430\u043D\u0430",other:"{{count}} \u0434\u0430\u043D\u0430"},aboutXWeeks:{one:{standalone:"\u043E\u043A\u043E 1 \u043D\u0435\u0434\u0435\u0459\u0443",withPrepositionAgo:"\u043E\u043A\u043E 1 \u043D\u0435\u0434\u0435\u0459\u0443",withPrepositionIn:"\u043E\u043A\u043E 1 \u043D\u0435\u0434\u0435\u0459\u0443"},dual:"\u043E\u043A\u043E {{count}} \u043D\u0435\u0434\u0435\u0459\u0435",other:"\u043E\u043A\u043E {{count}} \u043D\u0435\u0434\u0435\u0459\u0435"},xWeeks:{one:{standalone:"1 \u043D\u0435\u0434\u0435\u0459\u0443",withPrepositionAgo:"1 \u043D\u0435\u0434\u0435\u0459\u0443",withPrepositionIn:"1 \u043D\u0435\u0434\u0435\u0459\u0443"},dual:"{{count}} \u043D\u0435\u0434\u0435\u0459\u0435",other:"{{count}} \u043D\u0435\u0434\u0435\u0459\u0435"},aboutXMonths:{one:{standalone:"\u043E\u043A\u043E 1 \u043C\u0435\u0441\u0435\u0446",withPrepositionAgo:"\u043E\u043A\u043E 1 \u043C\u0435\u0441\u0435\u0446",withPrepositionIn:"\u043E\u043A\u043E 1 \u043C\u0435\u0441\u0435\u0446"},dual:"\u043E\u043A\u043E {{count}} \u043C\u0435\u0441\u0435\u0446\u0430",other:"\u043E\u043A\u043E {{count}} \u043C\u0435\u0441\u0435\u0446\u0438"},xMonths:{one:{standalone:"1 \u043C\u0435\u0441\u0435\u0446",withPrepositionAgo:"1 \u043C\u0435\u0441\u0435\u0446",withPrepositionIn:"1 \u043C\u0435\u0441\u0435\u0446"},dual:"{{count}} \u043C\u0435\u0441\u0435\u0446\u0430",other:"{{count}} \u043C\u0435\u0441\u0435\u0446\u0438"},aboutXYears:{one:{standalone:"\u043E\u043A\u043E 1 \u0433\u043E\u0434\u0438\u043D\u0443",withPrepositionAgo:"\u043E\u043A\u043E 1 \u0433\u043E\u0434\u0438\u043D\u0443",withPrepositionIn:"\u043E\u043A\u043E 1 \u0433\u043E\u0434\u0438\u043D\u0443"},dual:"\u043E\u043A\u043E {{count}} \u0433\u043E\u0434\u0438\u043D\u0435",other:"\u043E\u043A\u043E {{count}} \u0433\u043E\u0434\u0438\u043D\u0430"},xYears:{one:{standalone:"1 \u0433\u043E\u0434\u0438\u043D\u0430",withPrepositionAgo:"1 \u0433\u043E\u0434\u0438\u043D\u0435",withPrepositionIn:"1 \u0433\u043E\u0434\u0438\u043D\u0443"},dual:"{{count}} \u0433\u043E\u0434\u0438\u043D\u0435",other:"{{count}} \u0433\u043E\u0434\u0438\u043D\u0430"},overXYears:{one:{standalone:"\u043F\u0440\u0435\u043A\u043E 1 \u0433\u043E\u0434\u0438\u043D\u0443",withPrepositionAgo:"\u043F\u0440\u0435\u043A\u043E 1 \u0433\u043E\u0434\u0438\u043D\u0443",withPrepositionIn:"\u043F\u0440\u0435\u043A\u043E 1 \u0433\u043E\u0434\u0438\u043D\u0443"},dual:"\u043F\u0440\u0435\u043A\u043E {{count}} \u0433\u043E\u0434\u0438\u043D\u0435",other:"\u043F\u0440\u0435\u043A\u043E {{count}} \u0433\u043E\u0434\u0438\u043D\u0430"},almostXYears:{one:{standalone:"\u0433\u043E\u0442\u043E\u0432\u043E 1 \u0433\u043E\u0434\u0438\u043D\u0443",withPrepositionAgo:"\u0433\u043E\u0442\u043E\u0432\u043E 1 \u0433\u043E\u0434\u0438\u043D\u0443",withPrepositionIn:"\u0433\u043E\u0442\u043E\u0432\u043E 1 \u0433\u043E\u0434\u0438\u043D\u0443"},dual:"\u0433\u043E\u0442\u043E\u0432\u043E {{count}} \u0433\u043E\u0434\u0438\u043D\u0435",other:"\u0433\u043E\u0442\u043E\u0432\u043E {{count}} \u0433\u043E\u0434\u0438\u043D\u0430"}},x=function A(E,C,B){var H,G=X[E];if(typeof G==="string")H=G;else if(C===1)if(B!==null&&B!==void 0&&B.addSuffix)if(B.comparison&&B.comparison>0)H=G.one.withPrepositionIn;else H=G.one.withPrepositionAgo;else H=G.one.standalone;else if(C%10>1&&C%10<5&&String(C).substr(-2,1)!=="1")H=G.dual.replace("{{count}}",String(C));else H=G.other.replace("{{count}}",String(C));if(B!==null&&B!==void 0&&B.addSuffix)if(B.comparison&&B.comparison>0)return"\u0437\u0430 "+H;else return"\u043F\u0440\u0435 "+H;return H};function z(A){return function(){var E=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},C=E.width?String(E.width):A.defaultWidth,B=A.formats[C]||A.formats[A.defaultWidth];return B}}var $={full:"EEEE, d. MMMM yyyy.",long:"d. MMMM yyyy.",medium:"d. MMM yy.",short:"dd. MM. yy."},M={full:"HH:mm:ss (zzzz)",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},S={full:"{{date}} '\u0443' {{time}}",long:"{{date}} '\u0443' {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},R={date:z({formats:$,defaultWidth:"full"}),time:z({formats:M,defaultWidth:"full"}),dateTime:z({formats:S,defaultWidth:"full"})},L={lastWeek:function A(E){var C=E.getDay();switch(C){case 0:return"'\u043F\u0440\u043E\u0448\u043B\u0435 \u043D\u0435\u0434\u0435\u0459\u0435 \u0443' p";case 3:return"'\u043F\u0440\u043E\u0448\u043B\u0435 \u0441\u0440\u0435\u0434\u0435 \u0443' p";case 6:return"'\u043F\u0440\u043E\u0448\u043B\u0435 \u0441\u0443\u0431\u043E\u0442\u0435 \u0443' p";default:return"'\u043F\u0440\u043E\u0448\u043B\u0438' EEEE '\u0443' p"}},yesterday:"'\u0458\u0443\u0447\u0435 \u0443' p",today:"'\u0434\u0430\u043D\u0430\u0441 \u0443' p",tomorrow:"'\u0441\u0443\u0442\u0440\u0430 \u0443' p",nextWeek:function A(E){var C=E.getDay();switch(C){case 0:return"'\u0441\u043B\u0435\u0434\u0435\u045B\u0435 \u043D\u0435\u0434\u0435\u0459\u0435 \u0443' p";case 3:return"'\u0441\u043B\u0435\u0434\u0435\u045B\u0443 \u0441\u0440\u0435\u0434\u0443 \u0443' p";case 6:return"'\u0441\u043B\u0435\u0434\u0435\u045B\u0443 \u0441\u0443\u0431\u043E\u0442\u0443 \u0443' p";default:return"'\u0441\u043B\u0435\u0434\u0435\u045B\u0438' EEEE '\u0443' p"}},other:"P"},j=function A(E,C,B,H){var G=L[E];if(typeof G==="function")return G(C);return G};function T(A){return function(E,C){var B=C!==null&&C!==void 0&&C.context?String(C.context):"standalone",H;if(B==="formatting"&&A.formattingValues){var G=A.defaultFormattingWidth||A.defaultWidth,Y=C!==null&&C!==void 0&&C.width?String(C.width):G;H=A.formattingValues[Y]||A.formattingValues[G]}else{var Z=A.defaultWidth,q=C!==null&&C!==void 0&&C.width?String(C.width):A.defaultWidth;H=A.values[q]||A.values[Z]}var I=A.argumentCallback?A.argumentCallback(E):E;return H[I]}}var V={narrow:["\u043F\u0440.\u043D.\u0435.","\u0410\u0414"],abbreviated:["\u043F\u0440. \u0425\u0440.","\u043F\u043E. \u0425\u0440."],wide:["\u041F\u0440\u0435 \u0425\u0440\u0438\u0441\u0442\u0430","\u041F\u043E\u0441\u043B\u0435 \u0425\u0440\u0438\u0441\u0442\u0430"]},f={narrow:["1.","2.","3.","4."],abbreviated:["1. \u043A\u0432.","2. \u043A\u0432.","3. \u043A\u0432.","4. \u043A\u0432."],wide:["1. \u043A\u0432\u0430\u0440\u0442\u0430\u043B","2. \u043A\u0432\u0430\u0440\u0442\u0430\u043B","3. \u043A\u0432\u0430\u0440\u0442\u0430\u043B","4. \u043A\u0432\u0430\u0440\u0442\u0430\u043B"]},v={narrow:["1.","2.","3.","4.","5.","6.","7.","8.","9.","10.","11.","12."],abbreviated:["\u0458\u0430\u043D","\u0444\u0435\u0431","\u043C\u0430\u0440","\u0430\u043F\u0440","\u043C\u0430\u0458","\u0458\u0443\u043D","\u0458\u0443\u043B","\u0430\u0432\u0433","\u0441\u0435\u043F","\u043E\u043A\u0442","\u043D\u043E\u0432","\u0434\u0435\u0446"],wide:["\u0458\u0430\u043D\u0443\u0430\u0440","\u0444\u0435\u0431\u0440\u0443\u0430\u0440","\u043C\u0430\u0440\u0442","\u0430\u043F\u0440\u0438\u043B","\u043C\u0430\u0458","\u0458\u0443\u043D","\u0458\u0443\u043B","\u0430\u0432\u0433\u0443\u0441\u0442","\u0441\u0435\u043F\u0442\u0435\u043C\u0431\u0430\u0440","\u043E\u043A\u0442\u043E\u0431\u0430\u0440","\u043D\u043E\u0432\u0435\u043C\u0431\u0430\u0440","\u0434\u0435\u0446\u0435\u043C\u0431\u0430\u0440"]},w={narrow:["1.","2.","3.","4.","5.","6.","7.","8.","9.","10.","11.","12."],abbreviated:["\u0458\u0430\u043D","\u0444\u0435\u0431","\u043C\u0430\u0440","\u0430\u043F\u0440","\u043C\u0430\u0458","\u0458\u0443\u043D","\u0458\u0443\u043B","\u0430\u0432\u0433","\u0441\u0435\u043F","\u043E\u043A\u0442","\u043D\u043E\u0432","\u0434\u0435\u0446"],wide:["\u0458\u0430\u043D\u0443\u0430\u0440","\u0444\u0435\u0431\u0440\u0443\u0430\u0440","\u043C\u0430\u0440\u0442","\u0430\u043F\u0440\u0438\u043B","\u043C\u0430\u0458","\u0458\u0443\u043D","\u0458\u0443\u043B","\u0430\u0432\u0433\u0443\u0441\u0442","\u0441\u0435\u043F\u0442\u0435\u043C\u0431\u0430\u0440","\u043E\u043A\u0442\u043E\u0431\u0430\u0440","\u043D\u043E\u0432\u0435\u043C\u0431\u0430\u0440","\u0434\u0435\u0446\u0435\u043C\u0431\u0430\u0440"]},_={narrow:["\u041D","\u041F","\u0423","\u0421","\u0427","\u041F","\u0421"],short:["\u043D\u0435\u0434","\u043F\u043E\u043D","\u0443\u0442\u043E","\u0441\u0440\u0435","\u0447\u0435\u0442","\u043F\u0435\u0442","\u0441\u0443\u0431"],abbreviated:["\u043D\u0435\u0434","\u043F\u043E\u043D","\u0443\u0442\u043E","\u0441\u0440\u0435","\u0447\u0435\u0442","\u043F\u0435\u0442","\u0441\u0443\u0431"],wide:["\u043D\u0435\u0434\u0435\u0459\u0430","\u043F\u043E\u043D\u0435\u0434\u0435\u0459\u0430\u043A","\u0443\u0442\u043E\u0440\u0430\u043A","\u0441\u0440\u0435\u0434\u0430","\u0447\u0435\u0442\u0432\u0440\u0442\u0430\u043A","\u043F\u0435\u0442\u0430\u043A","\u0441\u0443\u0431\u043E\u0442\u0430"]},F={narrow:{am:"\u0410\u041C",pm:"\u041F\u041C",midnight:"\u043F\u043E\u043D\u043E\u045B",noon:"\u043F\u043E\u0434\u043D\u0435",morning:"\u0443\u0458\u0443\u0442\u0440\u0443",afternoon:"\u043F\u043E\u043F\u043E\u0434\u043D\u0435",evening:"\u0443\u0432\u0435\u0447\u0435",night:"\u043D\u043E\u045B\u0443"},abbreviated:{am:"\u0410\u041C",pm:"\u041F\u041C",midnight:"\u043F\u043E\u043D\u043E\u045B",noon:"\u043F\u043E\u0434\u043D\u0435",morning:"\u0443\u0458\u0443\u0442\u0440\u0443",afternoon:"\u043F\u043E\u043F\u043E\u0434\u043D\u0435",evening:"\u0443\u0432\u0435\u0447\u0435",night:"\u043D\u043E\u045B\u0443"},wide:{am:"AM",pm:"PM",midnight:"\u043F\u043E\u043D\u043E\u045B",noon:"\u043F\u043E\u0434\u043D\u0435",morning:"\u0443\u0458\u0443\u0442\u0440\u0443",afternoon:"\u043F\u043E\u0441\u043B\u0435 \u043F\u043E\u0434\u043D\u0435",evening:"\u0443\u0432\u0435\u0447\u0435",night:"\u043D\u043E\u045B\u0443"}},P={narrow:{am:"AM",pm:"PM",midnight:"\u043F\u043E\u043D\u043E\u045B",noon:"\u043F\u043E\u0434\u043D\u0435",morning:"\u0443\u0458\u0443\u0442\u0440\u0443",afternoon:"\u043F\u043E\u043F\u043E\u0434\u043D\u0435",evening:"\u0443\u0432\u0435\u0447\u0435",night:"\u043D\u043E\u045B\u0443"},abbreviated:{am:"AM",pm:"PM",midnight:"\u043F\u043E\u043D\u043E\u045B",noon:"\u043F\u043E\u0434\u043D\u0435",morning:"\u0443\u0458\u0443\u0442\u0440\u0443",afternoon:"\u043F\u043E\u043F\u043E\u0434\u043D\u0435",evening:"\u0443\u0432\u0435\u0447\u0435",night:"\u043D\u043E\u045B\u0443"},wide:{am:"AM",pm:"PM",midnight:"\u043F\u043E\u043D\u043E\u045B",noon:"\u043F\u043E\u0434\u043D\u0435",morning:"\u0443\u0458\u0443\u0442\u0440\u0443",afternoon:"\u043F\u043E\u0441\u043B\u0435 \u043F\u043E\u0434\u043D\u0435",evening:"\u0443\u0432\u0435\u0447\u0435",night:"\u043D\u043E\u045B\u0443"}},b=function A(E,C){var B=Number(E);return B+"."},k={ordinalNumber:b,era:T({values:V,defaultWidth:"wide"}),quarter:T({values:f,defaultWidth:"wide",argumentCallback:function A(E){return E-1}}),month:T({values:v,defaultWidth:"wide",formattingValues:w,defaultFormattingWidth:"wide"}),day:T({values:_,defaultWidth:"wide"}),dayPeriod:T({values:P,defaultWidth:"wide",formattingValues:F,defaultFormattingWidth:"wide"})};function O(A){return function(E){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},B=C.width,H=B&&A.matchPatterns[B]||A.matchPatterns[A.defaultMatchWidth],G=E.match(H);if(!G)return null;var Y=G[0],Z=B&&A.parsePatterns[B]||A.parsePatterns[A.defaultParseWidth],q=Array.isArray(Z)?m(Z,function(N){return N.test(Y)}):h(Z,function(N){return N.test(Y)}),I;I=A.valueCallback?A.valueCallback(q):q,I=C.valueCallback?C.valueCallback(I):I;var C0=E.slice(Y.length);return{value:I,rest:C0}}}var h=function A(E,C){for(var B in E)if(Object.prototype.hasOwnProperty.call(E,B)&&C(E[B]))return B;return},m=function A(E,C){for(var B=0;B<E.length;B++)if(C(E[B]))return B;return};function u(A){return function(E){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},B=E.match(A.matchPattern);if(!B)return null;var H=B[0],G=E.match(A.parsePattern);if(!G)return null;var Y=A.valueCallback?A.valueCallback(G[0]):G[0];Y=C.valueCallback?C.valueCallback(Y):Y;var Z=E.slice(H.length);return{value:Y,rest:Z}}}var y=/^(\d+)\./i,c=/\d+/i,g={narrow:/^(пр\.н\.е\.|АД)/i,abbreviated:/^(пр\.\s?Хр\.|по\.\s?Хр\.)/i,wide:/^(Пре Христа|пре нове ере|После Христа|нова ера)/i},p={any:[/^пр/i,/^(по|нова)/i]},d={narrow:/^[1234]/i,abbreviated:/^[1234]\.\s?кв\.?/i,wide:/^[1234]\. квартал/i},l={any:[/1/i,/2/i,/3/i,/4/i]},i={narrow:/^(10|11|12|[123456789])\./i,abbreviated:/^(јан|феб|мар|апр|мај|јун|јул|авг|сеп|окт|нов|дец)/i,wide:/^((јануар|јануара)|(фебруар|фебруара)|(март|марта)|(април|априла)|(мја|маја)|(јун|јуна)|(јул|јула)|(август|августа)|(септембар|септембра)|(октобар|октобра)|(новембар|новембра)|(децембар|децембра))/i},n={narrow:[/^1/i,/^2/i,/^3/i,/^4/i,/^5/i,/^6/i,/^7/i,/^8/i,/^9/i,/^10/i,/^11/i,/^12/i],any:[/^ја/i,/^ф/i,/^мар/i,/^ап/i,/^мај/i,/^јун/i,/^јул/i,/^авг/i,/^с/i,/^о/i,/^н/i,/^д/i]},s={narrow:/^[пусчн]/i,short:/^(нед|пон|уто|сре|чет|пет|суб)/i,abbreviated:/^(нед|пон|уто|сре|чет|пет|суб)/i,wide:/^(недеља|понедељак|уторак|среда|четвртак|петак|субота)/i},o={narrow:[/^п/i,/^у/i,/^с/i,/^ч/i,/^п/i,/^с/i,/^н/i],any:[/^нед/i,/^пон/i,/^уто/i,/^сре/i,/^чет/i,/^пет/i,/^суб/i]},r={any:/^(ам|пм|поноћ|(по)?подне|увече|ноћу|после подне|ујутру)/i},e={any:{am:/^a/i,pm:/^p/i,midnight:/^поно/i,noon:/^под/i,morning:/ујутру/i,afternoon:/(после\s|по)+подне/i,evening:/(увече)/i,night:/(ноћу)/i}},a={ordinalNumber:u({matchPattern:y,parsePattern:c,valueCallback:function A(E){return parseInt(E,10)}}),era:O({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:p,defaultParseWidth:"any"}),quarter:O({matchPatterns:d,defaultMatchWidth:"wide",parsePatterns:l,defaultParseWidth:"any",valueCallback:function A(E){return E+1}}),month:O({matchPatterns:i,defaultMatchWidth:"wide",parsePatterns:n,defaultParseWidth:"any"}),day:O({matchPatterns:s,defaultMatchWidth:"wide",parsePatterns:o,defaultParseWidth:"any"}),dayPeriod:O({matchPatterns:r,defaultMatchWidth:"any",parsePatterns:e,defaultParseWidth:"any"})},t={code:"sr",formatDistance:x,formatLong:R,formatRelative:j,localize:k,match:a,options:{weekStartsOn:1,firstWeekContainsDate:1}};window.dateFns=K(K({},window.dateFns),{},{locale:K(K({},(D=window.dateFns)===null||D===void 0?void 0:D.locale),{},{sr:t})})})();

//# debugId=DEFA10015F5F428764756e2164756e21
