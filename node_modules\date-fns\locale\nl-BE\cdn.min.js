var E=function(H){return E=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(G){return typeof G}:function(G){return G&&typeof Symbol=="function"&&G.constructor===Symbol&&G!==Symbol.prototype?"symbol":typeof G},E(H)},M=function(H,G){var J=Object.keys(H);if(Object.getOwnPropertySymbols){var I=Object.getOwnPropertySymbols(H);G&&(I=I.filter(function(x){return Object.getOwnPropertyDescriptor(H,x).enumerable})),J.push.apply(J,I)}return J},N=function(H){for(var G=1;G<arguments.length;G++){var J=arguments[G]!=null?arguments[G]:{};G%2?M(Object(J),!0).forEach(function(I){t(H,I,J[I])}):Object.getOwnPropertyDescriptors?Object.defineProperties(H,Object.getOwnPropertyDescriptors(J)):M(Object(J)).forEach(function(I){Object.defineProperty(H,I,Object.getOwnPropertyDescriptor(J,I))})}return H},t=function(H,G,J){if(G=XX(G),G in H)Object.defineProperty(H,G,{value:J,enumerable:!0,configurable:!0,writable:!0});else H[G]=J;return H},XX=function(H){var G=CX(H,"string");return E(G)=="symbol"?G:String(G)},CX=function(H,G){if(E(H)!="object"||!H)return H;var J=H[Symbol.toPrimitive];if(J!==void 0){var I=J.call(H,G||"default");if(E(I)!="object")return I;throw new TypeError("@@toPrimitive must return a primitive value.")}return(G==="string"?String:Number)(H)};(function(H){var G=Object.defineProperty,J=function X(U,C){for(var B in C)G(U,B,{get:C[B],enumerable:!0,configurable:!0,set:function Y(Z){return C[B]=function(){return Z}}})},I={lessThanXSeconds:{one:"minder dan een seconde",other:"minder dan {{count}} seconden"},xSeconds:{one:"1 seconde",other:"{{count}} seconden"},halfAMinute:"een halve minuut",lessThanXMinutes:{one:"minder dan een minuut",other:"minder dan {{count}} minuten"},xMinutes:{one:"een minuut",other:"{{count}} minuten"},aboutXHours:{one:"ongeveer 1 uur",other:"ongeveer {{count}} uur"},xHours:{one:"1 uur",other:"{{count}} uur"},xDays:{one:"1 dag",other:"{{count}} dagen"},aboutXWeeks:{one:"ongeveer 1 week",other:"ongeveer {{count}} weken"},xWeeks:{one:"1 week",other:"{{count}} weken"},aboutXMonths:{one:"ongeveer 1 maand",other:"ongeveer {{count}} maanden"},xMonths:{one:"1 maand",other:"{{count}} maanden"},aboutXYears:{one:"ongeveer 1 jaar",other:"ongeveer {{count}} jaar"},xYears:{one:"1 jaar",other:"{{count}} jaar"},overXYears:{one:"meer dan 1 jaar",other:"meer dan {{count}} jaar"},almostXYears:{one:"bijna 1 jaar",other:"bijna {{count}} jaar"}},x=function X(U,C,B){var Y,Z=I[U];if(typeof Z==="string")Y=Z;else if(C===1)Y=Z.one;else Y=Z.other.replace("{{count}}",String(C));if(B!==null&&B!==void 0&&B.addSuffix)if(B.comparison&&B.comparison>0)return"over "+Y;else return Y+" geleden";return Y};function z(X){return function(){var U=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},C=U.width?String(U.width):X.defaultWidth,B=X.formats[C]||X.formats[X.defaultWidth];return B}}var S={full:"EEEE d MMMM y",long:"d MMMM y",medium:"d MMM y",short:"dd.MM.y"},W={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},$={full:"{{date}} 'om' {{time}}",long:"{{date}} 'om' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},R={date:z({formats:S,defaultWidth:"full"}),time:z({formats:W,defaultWidth:"full"}),dateTime:z({formats:$,defaultWidth:"full"})},L={lastWeek:"'vorige' eeee 'om' p",yesterday:"'gisteren om' p",today:"'vandaag om' p",tomorrow:"'morgen om' p",nextWeek:"eeee 'om' p",other:"P"},V=function X(U,C,B,Y){return L[U]};function q(X){return function(U,C){var B=C!==null&&C!==void 0&&C.context?String(C.context):"standalone",Y;if(B==="formatting"&&X.formattingValues){var Z=X.defaultFormattingWidth||X.defaultWidth,T=C!==null&&C!==void 0&&C.width?String(C.width):Z;Y=X.formattingValues[T]||X.formattingValues[Z]}else{var O=X.defaultWidth,K=C!==null&&C!==void 0&&C.width?String(C.width):X.defaultWidth;Y=X.values[K]||X.values[O]}var Q=X.argumentCallback?X.argumentCallback(U):U;return Y[Q]}}var j={narrow:["v.C.","n.C."],abbreviated:["v.Chr.","n.Chr."],wide:["voor Christus","na Christus"]},f={narrow:["1","2","3","4"],abbreviated:["K1","K2","K3","K4"],wide:["1e kwartaal","2e kwartaal","3e kwartaal","4e kwartaal"]},v={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["jan.","feb.","mrt.","apr.","mei","jun.","jul.","aug.","sep.","okt.","nov.","dec."],wide:["januari","februari","maart","april","mei","juni","juli","augustus","september","oktober","november","december"]},P={narrow:["Z","M","D","W","D","V","Z"],short:["zo","ma","di","wo","do","vr","za"],abbreviated:["zon","maa","din","woe","don","vri","zat"],wide:["zondag","maandag","dinsdag","woensdag","donderdag","vrijdag","zaterdag"]},_={narrow:{am:"AM",pm:"PM",midnight:"middernacht",noon:"het middag",morning:"'s ochtends",afternoon:"'s namiddags",evening:"'s avonds",night:"'s nachts"},abbreviated:{am:"AM",pm:"PM",midnight:"middernacht",noon:"het middag",morning:"'s ochtends",afternoon:"'s namiddags",evening:"'s avonds",night:"'s nachts"},wide:{am:"AM",pm:"PM",midnight:"middernacht",noon:"het middag",morning:"'s ochtends",afternoon:"'s namiddags",evening:"'s avonds",night:"'s nachts"}},w=function X(U,C){var B=Number(U);return B+"e"},F={ordinalNumber:w,era:q({values:j,defaultWidth:"wide"}),quarter:q({values:f,defaultWidth:"wide",argumentCallback:function X(U){return U-1}}),month:q({values:v,defaultWidth:"wide"}),day:q({values:P,defaultWidth:"wide"}),dayPeriod:q({values:_,defaultWidth:"wide"})};function A(X){return function(U){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},B=C.width,Y=B&&X.matchPatterns[B]||X.matchPatterns[X.defaultMatchWidth],Z=U.match(Y);if(!Z)return null;var T=Z[0],O=B&&X.parsePatterns[B]||X.parsePatterns[X.defaultParseWidth],K=Array.isArray(O)?b(O,function(D){return D.test(T)}):h(O,function(D){return D.test(T)}),Q;Q=X.valueCallback?X.valueCallback(K):K,Q=C.valueCallback?C.valueCallback(Q):Q;var e=U.slice(T.length);return{value:Q,rest:e}}}var h=function X(U,C){for(var B in U)if(Object.prototype.hasOwnProperty.call(U,B)&&C(U[B]))return B;return},b=function X(U,C){for(var B=0;B<U.length;B++)if(C(U[B]))return B;return};function k(X){return function(U){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},B=U.match(X.matchPattern);if(!B)return null;var Y=B[0],Z=U.match(X.parsePattern);if(!Z)return null;var T=X.valueCallback?X.valueCallback(Z[0]):Z[0];T=C.valueCallback?C.valueCallback(T):T;var O=U.slice(Y.length);return{value:T,rest:O}}}var m=/^(\d+)e?/i,c=/\d+/i,y={narrow:/^([vn]\.? ?C\.?)/,abbreviated:/^([vn]\. ?Chr\.?)/,wide:/^((voor|na) Christus)/},p={any:[/^v/,/^n/]},d={narrow:/^[1234]/i,abbreviated:/^K[1234]/i,wide:/^[1234]e kwartaal/i},g={any:[/1/i,/2/i,/3/i,/4/i]},u={narrow:/^[jfmasond]/i,abbreviated:/^(jan.|feb.|mrt.|apr.|mei|jun.|jul.|aug.|sep.|okt.|nov.|dec.)/i,wide:/^(januari|februari|maart|april|mei|juni|juli|augustus|september|oktober|november|december)/i},l={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^jan/i,/^feb/i,/^m(r|a)/i,/^apr/i,/^mei/i,/^jun/i,/^jul/i,/^aug/i,/^sep/i,/^okt/i,/^nov/i,/^dec/i]},i={narrow:/^[zmdwv]/i,short:/^(zo|ma|di|wo|do|vr|za)/i,abbreviated:/^(zon|maa|din|woe|don|vri|zat)/i,wide:/^(zondag|maandag|dinsdag|woensdag|donderdag|vrijdag|zaterdag)/i},n={narrow:[/^z/i,/^m/i,/^d/i,/^w/i,/^d/i,/^v/i,/^z/i],any:[/^zo/i,/^ma/i,/^di/i,/^wo/i,/^do/i,/^vr/i,/^za/i]},s={any:/^(am|pm|middernacht|het middaguur|'s (ochtends|middags|avonds|nachts))/i},o={any:{am:/^am/i,pm:/^pm/i,midnight:/^middernacht/i,noon:/^het middaguur/i,morning:/ochtend/i,afternoon:/middag/i,evening:/avond/i,night:/nacht/i}},r={ordinalNumber:k({matchPattern:m,parsePattern:c,valueCallback:function X(U){return parseInt(U,10)}}),era:A({matchPatterns:y,defaultMatchWidth:"wide",parsePatterns:p,defaultParseWidth:"any"}),quarter:A({matchPatterns:d,defaultMatchWidth:"wide",parsePatterns:g,defaultParseWidth:"any",valueCallback:function X(U){return U+1}}),month:A({matchPatterns:u,defaultMatchWidth:"wide",parsePatterns:l,defaultParseWidth:"any"}),day:A({matchPatterns:i,defaultMatchWidth:"wide",parsePatterns:n,defaultParseWidth:"any"}),dayPeriod:A({matchPatterns:s,defaultMatchWidth:"any",parsePatterns:o,defaultParseWidth:"any"})},a={code:"nl-BE",formatDistance:x,formatLong:R,formatRelative:V,localize:F,match:r,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=N(N({},window.dateFns),{},{locale:N(N({},(H=window.dateFns)===null||H===void 0?void 0:H.locale),{},{nlBE:a})})})();

//# debugId=F87A33AD9F8CB00564756e2164756e21
