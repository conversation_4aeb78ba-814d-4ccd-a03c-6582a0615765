var z=function(J){return z=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(H){return typeof H}:function(H){return H&&typeof Symbol=="function"&&H.constructor===Symbol&&H!==Symbol.prototype?"symbol":typeof H},z(J)},$=function(J,H){var X=Object.keys(J);if(Object.getOwnPropertySymbols){var I=Object.getOwnPropertySymbols(J);H&&(I=I.filter(function(N){return Object.getOwnPropertyDescriptor(J,N).enumerable})),X.push.apply(X,I)}return X},K=function(J){for(var H=1;H<arguments.length;H++){var X=arguments[H]!=null?arguments[H]:{};H%2?$(Object(X),!0).forEach(function(I){B0(J,I,X[I])}):Object.getOwnPropertyDescriptors?Object.defineProperties(J,Object.getOwnPropertyDescriptors(X)):$(Object(X)).forEach(function(I){Object.defineProperty(J,I,Object.getOwnPropertyDescriptor(X,I))})}return J},B0=function(J,H,X){if(H=C0(H),H in J)Object.defineProperty(J,H,{value:X,enumerable:!0,configurable:!0,writable:!0});else J[H]=X;return J},C0=function(J){var H=G0(J,"string");return z(H)=="symbol"?H:String(H)},G0=function(J,H){if(z(J)!="object"||!J)return J;var X=J[Symbol.toPrimitive];if(X!==void 0){var I=X.call(J,H||"default");if(z(I)!="object")return I;throw new TypeError("@@toPrimitive must return a primitive value.")}return(H==="string"?String:Number)(J)};(function(J){var H=Object.defineProperty,X=function U(C,B){for(var G in B)H(C,G,{get:B[G],enumerable:!0,configurable:!0,set:function Y(Z){return B[G]=function(){return Z}}})},I={lessThanXSeconds:{standalone:{one:"weniger als 1 Sekunde",other:"weniger als {{count}} Sekunden"},withPreposition:{one:"weniger als 1 Sekunde",other:"weniger als {{count}} Sekunden"}},xSeconds:{standalone:{one:"1 Sekunde",other:"{{count}} Sekunden"},withPreposition:{one:"1 Sekunde",other:"{{count}} Sekunden"}},halfAMinute:{standalone:"eine halbe Minute",withPreposition:"einer halben Minute"},lessThanXMinutes:{standalone:{one:"weniger als 1 Minute",other:"weniger als {{count}} Minuten"},withPreposition:{one:"weniger als 1 Minute",other:"weniger als {{count}} Minuten"}},xMinutes:{standalone:{one:"1 Minute",other:"{{count}} Minuten"},withPreposition:{one:"1 Minute",other:"{{count}} Minuten"}},aboutXHours:{standalone:{one:"etwa 1 Stunde",other:"etwa {{count}} Stunden"},withPreposition:{one:"etwa 1 Stunde",other:"etwa {{count}} Stunden"}},xHours:{standalone:{one:"1 Stunde",other:"{{count}} Stunden"},withPreposition:{one:"1 Stunde",other:"{{count}} Stunden"}},xDays:{standalone:{one:"1 Tag",other:"{{count}} Tage"},withPreposition:{one:"1 Tag",other:"{{count}} Tagen"}},aboutXWeeks:{standalone:{one:"etwa 1 Woche",other:"etwa {{count}} Wochen"},withPreposition:{one:"etwa 1 Woche",other:"etwa {{count}} Wochen"}},xWeeks:{standalone:{one:"1 Woche",other:"{{count}} Wochen"},withPreposition:{one:"1 Woche",other:"{{count}} Wochen"}},aboutXMonths:{standalone:{one:"etwa 1 Monat",other:"etwa {{count}} Monate"},withPreposition:{one:"etwa 1 Monat",other:"etwa {{count}} Monaten"}},xMonths:{standalone:{one:"1 Monat",other:"{{count}} Monate"},withPreposition:{one:"1 Monat",other:"{{count}} Monaten"}},aboutXYears:{standalone:{one:"etwa 1 Jahr",other:"etwa {{count}} Jahre"},withPreposition:{one:"etwa 1 Jahr",other:"etwa {{count}} Jahren"}},xYears:{standalone:{one:"1 Jahr",other:"{{count}} Jahre"},withPreposition:{one:"1 Jahr",other:"{{count}} Jahren"}},overXYears:{standalone:{one:"mehr als 1 Jahr",other:"mehr als {{count}} Jahre"},withPreposition:{one:"mehr als 1 Jahr",other:"mehr als {{count}} Jahren"}},almostXYears:{standalone:{one:"fast 1 Jahr",other:"fast {{count}} Jahre"},withPreposition:{one:"fast 1 Jahr",other:"fast {{count}} Jahren"}}},N=function U(C,B,G){var Y,Z=G!==null&&G!==void 0&&G.addSuffix?I[C].withPreposition:I[C].standalone;if(typeof Z==="string")Y=Z;else if(B===1)Y=Z.one;else Y=Z.other.replace("{{count}}",String(B));if(G!==null&&G!==void 0&&G.addSuffix)if(G.comparison&&G.comparison>0)return"in "+Y;else return"vor "+Y;return Y};function S(U){return function(){var C=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},B=C.width?String(C.width):U.defaultWidth,G=U.formats[B]||U.formats[U.defaultWidth];return G}}var D={full:"EEEE, do MMMM y",long:"do MMMM y",medium:"do MMM y",short:"dd.MM.y"},M={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},R={full:"{{date}} 'um' {{time}}",long:"{{date}} 'um' {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},L={date:S({formats:D,defaultWidth:"full"}),time:S({formats:M,defaultWidth:"full"}),dateTime:S({formats:R,defaultWidth:"full"})},V={lastWeek:"'letzten' eeee 'um' p",yesterday:"'gestern um' p",today:"'heute um' p",tomorrow:"'morgen um' p",nextWeek:"eeee 'um' p",other:"P"},f=function U(C,B,G,Y){return V[C]};function Q(U){return function(C,B){var G=B!==null&&B!==void 0&&B.context?String(B.context):"standalone",Y;if(G==="formatting"&&U.formattingValues){var Z=U.defaultFormattingWidth||U.defaultWidth,T=B!==null&&B!==void 0&&B.width?String(B.width):Z;Y=U.formattingValues[T]||U.formattingValues[Z]}else{var E=U.defaultWidth,A=B!==null&&B!==void 0&&B.width?String(B.width):U.defaultWidth;Y=U.values[A]||U.values[E]}var O=U.argumentCallback?U.argumentCallback(C):C;return Y[O]}}var j={narrow:["v.Chr.","n.Chr."],abbreviated:["v.Chr.","n.Chr."],wide:["vor Christus","nach Christus"]},v={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1. Quartal","2. Quartal","3. Quartal","4. Quartal"]},x={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","M\xE4r","Apr","Mai","Jun","Jul","Aug","Sep","Okt","Nov","Dez"],wide:["Januar","Februar","M\xE4rz","April","Mai","Juni","Juli","August","September","Oktober","November","Dezember"]},w={narrow:x.narrow,abbreviated:["Jan.","Feb.","M\xE4rz","Apr.","Mai","Juni","Juli","Aug.","Sep.","Okt.","Nov.","Dez."],wide:x.wide},P={narrow:["S","M","D","M","D","F","S"],short:["So","Mo","Di","Mi","Do","Fr","Sa"],abbreviated:["So.","Mo.","Di.","Mi.","Do.","Fr.","Sa."],wide:["Sonntag","Montag","Dienstag","Mittwoch","Donnerstag","Freitag","Samstag"]},_={narrow:{am:"vm.",pm:"nm.",midnight:"Mitternacht",noon:"Mittag",morning:"Morgen",afternoon:"Nachm.",evening:"Abend",night:"Nacht"},abbreviated:{am:"vorm.",pm:"nachm.",midnight:"Mitternacht",noon:"Mittag",morning:"Morgen",afternoon:"Nachmittag",evening:"Abend",night:"Nacht"},wide:{am:"vormittags",pm:"nachmittags",midnight:"Mitternacht",noon:"Mittag",morning:"Morgen",afternoon:"Nachmittag",evening:"Abend",night:"Nacht"}},F={narrow:{am:"vm.",pm:"nm.",midnight:"Mitternacht",noon:"Mittag",morning:"morgens",afternoon:"nachm.",evening:"abends",night:"nachts"},abbreviated:{am:"vorm.",pm:"nachm.",midnight:"Mitternacht",noon:"Mittag",morning:"morgens",afternoon:"nachmittags",evening:"abends",night:"nachts"},wide:{am:"vormittags",pm:"nachmittags",midnight:"Mitternacht",noon:"Mittag",morning:"morgens",afternoon:"nachmittags",evening:"abends",night:"nachts"}},h=function U(C){var B=Number(C);return B+"."},b={ordinalNumber:h,era:Q({values:j,defaultWidth:"wide"}),quarter:Q({values:v,defaultWidth:"wide",argumentCallback:function U(C){return C-1}}),month:Q({values:x,formattingValues:w,defaultWidth:"wide"}),day:Q({values:P,defaultWidth:"wide"}),dayPeriod:Q({values:_,defaultWidth:"wide",formattingValues:F,defaultFormattingWidth:"wide"})};function q(U){return function(C){var B=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},G=B.width,Y=G&&U.matchPatterns[G]||U.matchPatterns[U.defaultMatchWidth],Z=C.match(Y);if(!Z)return null;var T=Z[0],E=G&&U.parsePatterns[G]||U.parsePatterns[U.defaultParseWidth],A=Array.isArray(E)?m(E,function(W){return W.test(T)}):k(E,function(W){return W.test(T)}),O;O=U.valueCallback?U.valueCallback(A):A,O=B.valueCallback?B.valueCallback(O):O;var U0=C.slice(T.length);return{value:O,rest:U0}}}var k=function U(C,B){for(var G in C)if(Object.prototype.hasOwnProperty.call(C,G)&&B(C[G]))return G;return},m=function U(C,B){for(var G=0;G<C.length;G++)if(B(C[G]))return G;return};function c(U){return function(C){var B=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},G=C.match(U.matchPattern);if(!G)return null;var Y=G[0],Z=C.match(U.parsePattern);if(!Z)return null;var T=U.valueCallback?U.valueCallback(Z[0]):Z[0];T=B.valueCallback?B.valueCallback(T):T;var E=C.slice(Y.length);return{value:T,rest:E}}}var y=/^(\d+)(\.)?/i,g=/\d+/i,p={narrow:/^(v\.? ?Chr\.?|n\.? ?Chr\.?)/i,abbreviated:/^(v\.? ?Chr\.?|n\.? ?Chr\.?)/i,wide:/^(vor Christus|vor unserer Zeitrechnung|nach Christus|unserer Zeitrechnung)/i},d={any:[/^v/i,/^n/i]},u={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](\.)? Quartal/i},l={any:[/1/i,/2/i,/3/i,/4/i]},i={narrow:/^[jfmasond]/i,abbreviated:/^(j[aä]n|feb|mär[z]?|apr|mai|jun[i]?|jul[i]?|aug|sep|okt|nov|dez)\.?/i,wide:/^(januar|februar|märz|april|mai|juni|juli|august|september|oktober|november|dezember)/i},n={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^j[aä]/i,/^f/i,/^mär/i,/^ap/i,/^mai/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},s={narrow:/^[smdmf]/i,short:/^(so|mo|di|mi|do|fr|sa)/i,abbreviated:/^(son?|mon?|die?|mit?|don?|fre?|sam?)\.?/i,wide:/^(sonntag|montag|dienstag|mittwoch|donnerstag|freitag|samstag)/i},o={any:[/^so/i,/^mo/i,/^di/i,/^mi/i,/^do/i,/^f/i,/^sa/i]},r={narrow:/^(vm\.?|nm\.?|Mitternacht|Mittag|morgens|nachm\.?|abends|nachts)/i,abbreviated:/^(vorm\.?|nachm\.?|Mitternacht|Mittag|morgens|nachm\.?|abends|nachts)/i,wide:/^(vormittags|nachmittags|Mitternacht|Mittag|morgens|nachmittags|abends|nachts)/i},e={any:{am:/^v/i,pm:/^n/i,midnight:/^Mitte/i,noon:/^Mitta/i,morning:/morgens/i,afternoon:/nachmittags/i,evening:/abends/i,night:/nachts/i}},a={ordinalNumber:c({matchPattern:y,parsePattern:g,valueCallback:function U(C){return parseInt(C)}}),era:q({matchPatterns:p,defaultMatchWidth:"wide",parsePatterns:d,defaultParseWidth:"any"}),quarter:q({matchPatterns:u,defaultMatchWidth:"wide",parsePatterns:l,defaultParseWidth:"any",valueCallback:function U(C){return C+1}}),month:q({matchPatterns:i,defaultMatchWidth:"wide",parsePatterns:n,defaultParseWidth:"any"}),day:q({matchPatterns:s,defaultMatchWidth:"wide",parsePatterns:o,defaultParseWidth:"any"}),dayPeriod:q({matchPatterns:r,defaultMatchWidth:"wide",parsePatterns:e,defaultParseWidth:"any"})},t={code:"de",formatDistance:N,formatLong:L,formatRelative:f,localize:b,match:a,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=K(K({},window.dateFns),{},{locale:K(K({},(J=window.dateFns)===null||J===void 0?void 0:J.locale),{},{de:t})})})();

//# debugId=92CCB5A282C12B6464756e2164756e21
