import { useLanguage } from '@/hooks/use-language';
import { Link } from 'wouter';

interface LegalLink {
  href: string;
  label: string;
}

export function FooterBottomBar() {
  const { t } = useLanguage();

  const legalLinks: LegalLink[] = [
    { href: '/privacy', label: t('footer.privacyPolicy') },
    { href: '/terms', label: t('footer.termsOfService') },
    { href: '/cookies', label: t('footer.cookiePolicy') },
  ];

  return (
    <div className="border-t border-gray-700 pt-2">
      <div className="flex flex-col md:flex-row justify-between items-center">
        <p className="text-gray-400 text-sm">{t('footer.copyright')}</p>
        <div className="flex items-center space-x-6 mt-4 md:mt-0">
          {legalLinks.map(link => (
            <Link
              key={link.href}
              href={link.href}
              className="text-gray-400 hover:text-gold text-sm transition-colors duration-300">
              {link.label}
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
}
