{"name": "@types/passport-local", "version": "1.0.38", "description": "TypeScript definitions for passport-local", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/passport-local", "license": "MIT", "contributors": [{"name": "Maxime LUCE", "githubUsername": "SomaticIT", "url": "https://github.com/SomaticIT"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/passport-local"}, "scripts": {}, "dependencies": {"@types/express": "*", "@types/passport": "*", "@types/passport-strategy": "*"}, "typesPublisherContentHash": "a78489fa7611c74a38c8634dd40513c82c0103efd669381f07dcf715294ff3ef", "typeScriptVersion": "4.5"}