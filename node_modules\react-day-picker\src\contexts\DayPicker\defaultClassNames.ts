import { ClassNames } from 'types/Styles';

/**
 * The name of the default CSS classes.
 */
export const defaultClassNames: Required<ClassNames> = {
  root: 'rdp',
  multiple_months: 'rdp-multiple_months',
  with_weeknumber: 'rdp-with_weeknumber',
  vhidden: 'rdp-vhidden',
  button_reset: 'rdp-button_reset',
  button: 'rdp-button',

  caption: 'rdp-caption',

  caption_start: 'rdp-caption_start',
  caption_end: 'rdp-caption_end',
  caption_between: 'rdp-caption_between',
  caption_label: 'rdp-caption_label',

  caption_dropdowns: 'rdp-caption_dropdowns',

  dropdown: 'rdp-dropdown',
  dropdown_month: 'rdp-dropdown_month',
  dropdown_year: 'rdp-dropdown_year',
  dropdown_icon: 'rdp-dropdown_icon',

  months: 'rdp-months',
  month: 'rdp-month',
  table: 'rdp-table',
  tbody: 'rdp-tbody',
  tfoot: 'rdp-tfoot',

  head: 'rdp-head',
  head_row: 'rdp-head_row',
  head_cell: 'rdp-head_cell',

  nav: 'rdp-nav',
  nav_button: 'rdp-nav_button',
  nav_button_previous: 'rdp-nav_button_previous',
  nav_button_next: 'rdp-nav_button_next',

  nav_icon: 'rdp-nav_icon',

  row: 'rdp-row',
  weeknumber: 'rdp-weeknumber',
  cell: 'rdp-cell',

  day: 'rdp-day',
  day_today: 'rdp-day_today',
  day_outside: 'rdp-day_outside',
  day_selected: 'rdp-day_selected',
  day_disabled: 'rdp-day_disabled',
  day_hidden: 'rdp-day_hidden',
  day_range_start: 'rdp-day_range_start',
  day_range_end: 'rdp-day_range_end',
  day_range_middle: 'rdp-day_range_middle'
};
