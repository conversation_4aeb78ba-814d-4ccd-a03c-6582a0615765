var O=function(Y){return O=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(X){return typeof X}:function(X){return X&&typeof Symbol=="function"&&X.constructor===Symbol&&X!==Symbol.prototype?"symbol":typeof X},O(Y)},N=function(Y,X){var H=Object.keys(Y);if(Object.getOwnPropertySymbols){var I=Object.getOwnPropertySymbols(Y);X&&(I=I.filter(function(K){return Object.getOwnPropertyDescriptor(Y,K).enumerable})),H.push.apply(H,I)}return H},D=function(Y){for(var X=1;X<arguments.length;X++){var H=arguments[X]!=null?arguments[X]:{};X%2?N(Object(H),!0).forEach(function(I){CC(Y,I,H[I])}):Object.getOwnPropertyDescriptors?Object.defineProperties(Y,Object.getOwnPropertyDescriptors(H)):N(Object(H)).forEach(function(I){Object.defineProperty(Y,I,Object.getOwnPropertyDescriptor(H,I))})}return Y},CC=function(Y,X,H){if(X=UC(X),X in Y)Object.defineProperty(Y,X,{value:H,enumerable:!0,configurable:!0,writable:!0});else Y[X]=H;return Y},UC=function(Y){var X=BC(Y,"string");return O(X)=="symbol"?X:String(X)},BC=function(Y,X){if(O(Y)!="object"||!Y)return Y;var H=Y[Symbol.toPrimitive];if(H!==void 0){var I=H.call(Y,X||"default");if(O(I)!="object")return I;throw new TypeError("@@toPrimitive must return a primitive value.")}return(X==="string"?String:Number)(Y)};(function(Y){var X=Object.defineProperty,H=function U(J,C){for(var B in C)X(J,B,{get:C[B],enumerable:!0,configurable:!0,set:function Z(G){return C[B]=function(){return G}}})},I={lessThanXSeconds:{one:"moins d\u2019une seconde",other:"moins de {{count}} secondes"},xSeconds:{one:"1 seconde",other:"{{count}} secondes"},halfAMinute:"30 secondes",lessThanXMinutes:{one:"moins d\u2019une minute",other:"moins de {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"environ 1 heure",other:"environ {{count}} heures"},xHours:{one:"1 heure",other:"{{count}} heures"},xDays:{one:"1 jour",other:"{{count}} jours"},aboutXWeeks:{one:"environ 1 semaine",other:"environ {{count}} semaines"},xWeeks:{one:"1 semaine",other:"{{count}} semaines"},aboutXMonths:{one:"environ 1 mois",other:"environ {{count}} mois"},xMonths:{one:"1 mois",other:"{{count}} mois"},aboutXYears:{one:"environ 1 an",other:"environ {{count}} ans"},xYears:{one:"1 an",other:"{{count}} ans"},overXYears:{one:"plus d\u2019un an",other:"plus de {{count}} ans"},almostXYears:{one:"presqu\u2019un an",other:"presque {{count}} ans"}},K=function U(J,C,B){var Z,G=I[J];if(typeof G==="string")Z=G;else if(C===1)Z=G.one;else Z=G.other.replace("{{count}}",String(C));if(B!==null&&B!==void 0&&B.addSuffix)if(B.comparison&&B.comparison>0)return"dans "+Z;else return"il y a "+Z;return Z};function x(U){return function(){var J=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},C=J.width?String(J.width):U.defaultWidth,B=U.formats[C]||U.formats[U.defaultWidth];return B}}var V={full:"EEEE d MMMM y",long:"d MMMM y",medium:"d MMM y",short:"dd/MM/y"},$={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},R={full:"{{date}} '\xE0' {{time}}",long:"{{date}} '\xE0' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},M={date:x({formats:V,defaultWidth:"full"}),time:x({formats:$,defaultWidth:"full"}),dateTime:x({formats:R,defaultWidth:"full"})},S={lastWeek:"eeee 'dernier \xE0' p",yesterday:"'hier \xE0' p",today:"'aujourd\u2019hui \xE0' p",tomorrow:"'demain \xE0' p'",nextWeek:"eeee 'prochain \xE0' p",other:"P"},L=function U(J,C,B,Z){return S[J]};function E(U){return function(J,C){var B=C!==null&&C!==void 0&&C.context?String(C.context):"standalone",Z;if(B==="formatting"&&U.formattingValues){var G=U.defaultFormattingWidth||U.defaultWidth,Q=C!==null&&C!==void 0&&C.width?String(C.width):G;Z=U.formattingValues[Q]||U.formattingValues[G]}else{var q=U.defaultWidth,T=C!==null&&C!==void 0&&C.width?String(C.width):U.defaultWidth;Z=U.values[T]||U.values[q]}var A=U.argumentCallback?U.argumentCallback(J):J;return Z[A]}}var j={narrow:["av. J.-C","ap. J.-C"],abbreviated:["av. J.-C","ap. J.-C"],wide:["avant J\xE9sus-Christ","apr\xE8s J\xE9sus-Christ"]},v={narrow:["T1","T2","T3","T4"],abbreviated:["1er trim.","2\xE8me trim.","3\xE8me trim.","4\xE8me trim."],wide:["1er trimestre","2\xE8me trimestre","3\xE8me trimestre","4\xE8me trimestre"]},P={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["janv.","f\xE9vr.","mars","avr.","mai","juin","juil.","ao\xFBt","sept.","oct.","nov.","d\xE9c."],wide:["janvier","f\xE9vrier","mars","avril","mai","juin","juillet","ao\xFBt","septembre","octobre","novembre","d\xE9cembre"]},w={narrow:["D","L","M","M","J","V","S"],short:["di","lu","ma","me","je","ve","sa"],abbreviated:["dim.","lun.","mar.","mer.","jeu.","ven.","sam."],wide:["dimanche","lundi","mardi","mercredi","jeudi","vendredi","samedi"]},F={narrow:{am:"AM",pm:"PM",midnight:"minuit",noon:"midi",morning:"mat.",afternoon:"ap.m.",evening:"soir",night:"mat."},abbreviated:{am:"AM",pm:"PM",midnight:"minuit",noon:"midi",morning:"matin",afternoon:"apr\xE8s-midi",evening:"soir",night:"matin"},wide:{am:"AM",pm:"PM",midnight:"minuit",noon:"midi",morning:"du matin",afternoon:"de l\u2019apr\xE8s-midi",evening:"du soir",night:"du matin"}},_=function U(J,C){var B=Number(J),Z=C===null||C===void 0?void 0:C.unit;if(B===0)return"0";var G=["year","week","hour","minute","second"],Q;if(B===1)Q=Z&&G.includes(Z)?"\xE8re":"er";else Q="\xE8me";return B+Q},f=["MMM","MMMM"],k={preprocessor:function U(J,C){if(J.getDate()===1)return C;var B=C.some(function(Z){return Z.isToken&&f.includes(Z.value)});if(!B)return C;return C.map(function(Z){return Z.isToken&&Z.value==="do"?{isToken:!0,value:"d"}:Z})},ordinalNumber:_,era:E({values:j,defaultWidth:"wide"}),quarter:E({values:v,defaultWidth:"wide",argumentCallback:function U(J){return J-1}}),month:E({values:P,defaultWidth:"wide"}),day:E({values:w,defaultWidth:"wide"}),dayPeriod:E({values:F,defaultWidth:"wide"})};function W(U){return function(J){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},B=C.width,Z=B&&U.matchPatterns[B]||U.matchPatterns[U.defaultMatchWidth],G=J.match(Z);if(!G)return null;var Q=G[0],q=B&&U.parsePatterns[B]||U.parsePatterns[U.defaultParseWidth],T=Array.isArray(q)?h(q,function(z){return z.test(Q)}):b(q,function(z){return z.test(Q)}),A;A=U.valueCallback?U.valueCallback(T):T,A=C.valueCallback?C.valueCallback(A):A;var t=J.slice(Q.length);return{value:A,rest:t}}}var b=function U(J,C){for(var B in J)if(Object.prototype.hasOwnProperty.call(J,B)&&C(J[B]))return B;return},h=function U(J,C){for(var B=0;B<J.length;B++)if(C(J[B]))return B;return};function m(U){return function(J){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},B=J.match(U.matchPattern);if(!B)return null;var Z=B[0],G=J.match(U.parsePattern);if(!G)return null;var Q=U.valueCallback?U.valueCallback(G[0]):G[0];Q=C.valueCallback?C.valueCallback(Q):Q;var q=J.slice(Z.length);return{value:Q,rest:q}}}var c=/^(\d+)(ième|ère|ème|er|e)?/i,y=/\d+/i,u={narrow:/^(av\.J\.C|ap\.J\.C|ap\.J\.-C)/i,abbreviated:/^(av\.J\.-C|av\.J-C|apr\.J\.-C|apr\.J-C|ap\.J-C)/i,wide:/^(avant Jésus-Christ|après Jésus-Christ)/i},g={any:[/^av/i,/^ap/i]},d={narrow:/^T?[1234]/i,abbreviated:/^[1234](er|ème|e)? trim\.?/i,wide:/^[1234](er|ème|e)? trimestre/i},l={any:[/1/i,/2/i,/3/i,/4/i]},p={narrow:/^[jfmasond]/i,abbreviated:/^(janv|févr|mars|avr|mai|juin|juill|juil|août|sept|oct|nov|déc)\.?/i,wide:/^(janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)/i},i={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^av/i,/^ma/i,/^juin/i,/^juil/i,/^ao/i,/^s/i,/^o/i,/^n/i,/^d/i]},n={narrow:/^[lmjvsd]/i,short:/^(di|lu|ma|me|je|ve|sa)/i,abbreviated:/^(dim|lun|mar|mer|jeu|ven|sam)\.?/i,wide:/^(dimanche|lundi|mardi|mercredi|jeudi|vendredi|samedi)/i},s={narrow:[/^d/i,/^l/i,/^m/i,/^m/i,/^j/i,/^v/i,/^s/i],any:[/^di/i,/^lu/i,/^ma/i,/^me/i,/^je/i,/^ve/i,/^sa/i]},o={narrow:/^(a|p|minuit|midi|mat\.?|ap\.?m\.?|soir|nuit)/i,any:/^([ap]\.?\s?m\.?|du matin|de l'après[-\s]midi|du soir|de la nuit)/i},e={any:{am:/^a/i,pm:/^p/i,midnight:/^min/i,noon:/^mid/i,morning:/mat/i,afternoon:/ap/i,evening:/soir/i,night:/nuit/i}},r={ordinalNumber:m({matchPattern:c,parsePattern:y,valueCallback:function U(J){return parseInt(J)}}),era:W({matchPatterns:u,defaultMatchWidth:"wide",parsePatterns:g,defaultParseWidth:"any"}),quarter:W({matchPatterns:d,defaultMatchWidth:"wide",parsePatterns:l,defaultParseWidth:"any",valueCallback:function U(J){return J+1}}),month:W({matchPatterns:p,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any"}),day:W({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),dayPeriod:W({matchPatterns:o,defaultMatchWidth:"any",parsePatterns:e,defaultParseWidth:"any"})},a={code:"fr",formatDistance:K,formatLong:M,formatRelative:L,localize:k,match:r,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=D(D({},window.dateFns),{},{locale:D(D({},(Y=window.dateFns)===null||Y===void 0?void 0:Y.locale),{},{fr:a})})})();

//# debugId=15DEBA649CA1E4C364756e2164756e21
