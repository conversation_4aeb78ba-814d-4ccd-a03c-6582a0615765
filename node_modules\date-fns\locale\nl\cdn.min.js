var x=function(J){return x=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(H){return typeof H}:function(H){return H&&typeof Symbol=="function"&&H.constructor===Symbol&&H!==Symbol.prototype?"symbol":typeof H},x(J)},N=function(J,H){var T=Object.keys(J);if(Object.getOwnPropertySymbols){var Z=Object.getOwnPropertySymbols(J);H&&(Z=Z.filter(function(D){return Object.getOwnPropertyDescriptor(J,D).enumerable})),T.push.apply(T,Z)}return T},z=function(J){for(var H=1;H<arguments.length;H++){var T=arguments[H]!=null?arguments[H]:{};H%2?N(Object(T),!0).forEach(function(Z){t(J,Z,T[Z])}):Object.getOwnPropertyDescriptors?Object.defineProperties(J,Object.getOwnPropertyDescriptors(T)):N(Object(T)).forEach(function(Z){Object.defineProperty(J,Z,Object.getOwnPropertyDescriptor(T,Z))})}return J},t=function(J,H,T){if(H=CC(H),H in J)Object.defineProperty(J,H,{value:T,enumerable:!0,configurable:!0,writable:!0});else J[H]=T;return J},CC=function(J){var H=UC(J,"string");return x(H)=="symbol"?H:String(H)},UC=function(J,H){if(x(J)!="object"||!J)return J;var T=J[Symbol.toPrimitive];if(T!==void 0){var Z=T.call(J,H||"default");if(x(Z)!="object")return Z;throw new TypeError("@@toPrimitive must return a primitive value.")}return(H==="string"?String:Number)(J)};(function(J){var H=Object.defineProperty,T=function C(B,U){for(var G in U)H(B,G,{get:U[G],enumerable:!0,configurable:!0,set:function X(Y){return U[G]=function(){return Y}}})},Z={lessThanXSeconds:{one:"minder dan een seconde",other:"minder dan {{count}} seconden"},xSeconds:{one:"1 seconde",other:"{{count}} seconden"},halfAMinute:"een halve minuut",lessThanXMinutes:{one:"minder dan een minuut",other:"minder dan {{count}} minuten"},xMinutes:{one:"een minuut",other:"{{count}} minuten"},aboutXHours:{one:"ongeveer 1 uur",other:"ongeveer {{count}} uur"},xHours:{one:"1 uur",other:"{{count}} uur"},xDays:{one:"1 dag",other:"{{count}} dagen"},aboutXWeeks:{one:"ongeveer 1 week",other:"ongeveer {{count}} weken"},xWeeks:{one:"1 week",other:"{{count}} weken"},aboutXMonths:{one:"ongeveer 1 maand",other:"ongeveer {{count}} maanden"},xMonths:{one:"1 maand",other:"{{count}} maanden"},aboutXYears:{one:"ongeveer 1 jaar",other:"ongeveer {{count}} jaar"},xYears:{one:"1 jaar",other:"{{count}} jaar"},overXYears:{one:"meer dan 1 jaar",other:"meer dan {{count}} jaar"},almostXYears:{one:"bijna 1 jaar",other:"bijna {{count}} jaar"}},D=function C(B,U,G){var X,Y=Z[B];if(typeof Y==="string")X=Y;else if(U===1)X=Y.one;else X=Y.other.replace("{{count}}",String(U));if(G!==null&&G!==void 0&&G.addSuffix)if(G.comparison&&G.comparison>0)return"over "+X;else return X+" geleden";return X};function K(C){return function(){var B=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},U=B.width?String(B.width):C.defaultWidth,G=C.formats[U]||C.formats[C.defaultWidth];return G}}var W={full:"EEEE d MMMM y",long:"d MMMM y",medium:"d MMM y",short:"dd-MM-y"},$={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},M={full:"{{date}} 'om' {{time}}",long:"{{date}} 'om' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},S={date:K({formats:W,defaultWidth:"full"}),time:K({formats:$,defaultWidth:"full"}),dateTime:K({formats:M,defaultWidth:"full"})},L={lastWeek:"'afgelopen' eeee 'om' p",yesterday:"'gisteren om' p",today:"'vandaag om' p",tomorrow:"'morgen om' p",nextWeek:"eeee 'om' p",other:"P"},f=function C(B,U,G,X){return L[B]};function O(C){return function(B,U){var G=U!==null&&U!==void 0&&U.context?String(U.context):"standalone",X;if(G==="formatting"&&C.formattingValues){var Y=C.defaultFormattingWidth||C.defaultWidth,I=U!==null&&U!==void 0&&U.width?String(U.width):Y;X=C.formattingValues[I]||C.formattingValues[Y]}else{var Q=C.defaultWidth,A=U!==null&&U!==void 0&&U.width?String(U.width):C.defaultWidth;X=C.values[A]||C.values[Q]}var E=C.argumentCallback?C.argumentCallback(B):B;return X[E]}}var V={narrow:["v.C.","n.C."],abbreviated:["v.Chr.","n.Chr."],wide:["voor Christus","na Christus"]},j={narrow:["1","2","3","4"],abbreviated:["K1","K2","K3","K4"],wide:["1e kwartaal","2e kwartaal","3e kwartaal","4e kwartaal"]},w={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["jan.","feb.","mrt.","apr.","mei","jun.","jul.","aug.","sep.","okt.","nov.","dec."],wide:["januari","februari","maart","april","mei","juni","juli","augustus","september","oktober","november","december"]},v={narrow:["Z","M","D","W","D","V","Z"],short:["zo","ma","di","wo","do","vr","za"],abbreviated:["zon","maa","din","woe","don","vri","zat"],wide:["zondag","maandag","dinsdag","woensdag","donderdag","vrijdag","zaterdag"]},P={narrow:{am:"AM",pm:"PM",midnight:"middernacht",noon:"het middaguur",morning:"'s ochtends",afternoon:"'s middags",evening:"'s avonds",night:"'s nachts"},abbreviated:{am:"AM",pm:"PM",midnight:"middernacht",noon:"het middaguur",morning:"'s ochtends",afternoon:"'s middags",evening:"'s avonds",night:"'s nachts"},wide:{am:"AM",pm:"PM",midnight:"middernacht",noon:"het middaguur",morning:"'s ochtends",afternoon:"'s middags",evening:"'s avonds",night:"'s nachts"}},_=function C(B,U){var G=Number(B);return G+"e"},F={ordinalNumber:_,era:O({values:V,defaultWidth:"wide"}),quarter:O({values:j,defaultWidth:"wide",argumentCallback:function C(B){return B-1}}),month:O({values:w,defaultWidth:"wide"}),day:O({values:v,defaultWidth:"wide"}),dayPeriod:O({values:P,defaultWidth:"wide"})};function q(C){return function(B){var U=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},G=U.width,X=G&&C.matchPatterns[G]||C.matchPatterns[C.defaultMatchWidth],Y=B.match(X);if(!Y)return null;var I=Y[0],Q=G&&C.parsePatterns[G]||C.parsePatterns[C.defaultParseWidth],A=Array.isArray(Q)?m(Q,function(R){return R.test(I)}):h(Q,function(R){return R.test(I)}),E;E=C.valueCallback?C.valueCallback(A):A,E=U.valueCallback?U.valueCallback(E):E;var e=B.slice(I.length);return{value:E,rest:e}}}var h=function C(B,U){for(var G in B)if(Object.prototype.hasOwnProperty.call(B,G)&&U(B[G]))return G;return},m=function C(B,U){for(var G=0;G<B.length;G++)if(U(B[G]))return G;return};function b(C){return function(B){var U=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},G=B.match(C.matchPattern);if(!G)return null;var X=G[0],Y=B.match(C.parsePattern);if(!Y)return null;var I=C.valueCallback?C.valueCallback(Y[0]):Y[0];I=U.valueCallback?U.valueCallback(I):I;var Q=B.slice(X.length);return{value:I,rest:Q}}}var k=/^(\d+)e?/i,c=/\d+/i,y={narrow:/^([vn]\.? ?C\.?)/,abbreviated:/^([vn]\. ?Chr\.?)/,wide:/^((voor|na) Christus)/},p={any:[/^v/,/^n/]},d={narrow:/^[1234]/i,abbreviated:/^K[1234]/i,wide:/^[1234]e kwartaal/i},g={any:[/1/i,/2/i,/3/i,/4/i]},u={narrow:/^[jfmasond]/i,abbreviated:/^(jan.|feb.|mrt.|apr.|mei|jun.|jul.|aug.|sep.|okt.|nov.|dec.)/i,wide:/^(januari|februari|maart|april|mei|juni|juli|augustus|september|oktober|november|december)/i},l={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^jan/i,/^feb/i,/^m(r|a)/i,/^apr/i,/^mei/i,/^jun/i,/^jul/i,/^aug/i,/^sep/i,/^okt/i,/^nov/i,/^dec/i]},i={narrow:/^[zmdwv]/i,short:/^(zo|ma|di|wo|do|vr|za)/i,abbreviated:/^(zon|maa|din|woe|don|vri|zat)/i,wide:/^(zondag|maandag|dinsdag|woensdag|donderdag|vrijdag|zaterdag)/i},n={narrow:[/^z/i,/^m/i,/^d/i,/^w/i,/^d/i,/^v/i,/^z/i],any:[/^zo/i,/^ma/i,/^di/i,/^wo/i,/^do/i,/^vr/i,/^za/i]},s={any:/^(am|pm|middernacht|het middaguur|'s (ochtends|middags|avonds|nachts))/i},o={any:{am:/^am/i,pm:/^pm/i,midnight:/^middernacht/i,noon:/^het middaguur/i,morning:/ochtend/i,afternoon:/middag/i,evening:/avond/i,night:/nacht/i}},r={ordinalNumber:b({matchPattern:k,parsePattern:c,valueCallback:function C(B){return parseInt(B,10)}}),era:q({matchPatterns:y,defaultMatchWidth:"wide",parsePatterns:p,defaultParseWidth:"any"}),quarter:q({matchPatterns:d,defaultMatchWidth:"wide",parsePatterns:g,defaultParseWidth:"any",valueCallback:function C(B){return B+1}}),month:q({matchPatterns:u,defaultMatchWidth:"wide",parsePatterns:l,defaultParseWidth:"any"}),day:q({matchPatterns:i,defaultMatchWidth:"wide",parsePatterns:n,defaultParseWidth:"any"}),dayPeriod:q({matchPatterns:s,defaultMatchWidth:"any",parsePatterns:o,defaultParseWidth:"any"})},a={code:"nl",formatDistance:D,formatLong:S,formatRelative:f,localize:F,match:r,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=z(z({},window.dateFns),{},{locale:z(z({},(J=window.dateFns)===null||J===void 0?void 0:J.locale),{},{nl:a})})})();

//# debugId=D93973C601BE3FF664756e2164756e21
