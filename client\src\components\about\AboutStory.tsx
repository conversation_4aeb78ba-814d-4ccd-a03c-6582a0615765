import { useLanguage } from '@/hooks/use-language';
import { Card, CardContent } from '@/components/ui/card';

export function AboutStory() {
  const { t, language } = useLanguage();

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          <div className={`${language === 'ar' ? 'lg:order-2' : 'lg:order-1'}`}>
            <h2 className="font-playfair text-4xl lg:text-5xl font-bold text-charcoal mb-6">{t('about.ourStory')}</h2>
            <h3 className="text-2xl text-gold font-semibold mb-6">{t('about.storyTitle')}</h3>
            <p className="text-lg text-medium-gray leading-relaxed mb-6">{t('about.storyDescription')}</p>
            <p className="text-lg text-medium-gray leading-relaxed">{t('about.storyContent')}</p>
          </div>

          <div className={`${language === 'ar' ? 'lg:order-1' : 'lg:order-2'}`}>
            <Card className="elegant-shadow overflow-hidden transform translateZ(0) will-change-transform" style={{
              backfaceVisibility: 'hidden',
              WebkitBackfaceVisibility: 'hidden',
              transformStyle: 'preserve-3d'
            }}>
              <div className="relative w-full h-96">
                <img
                  src="https://images.unsplash.com/photo-1643965304951-07a7ebb783a0?auto=format&fit=crop&w=1200&q=80"
                  alt="Beautiful Oman landscape showing mountains meeting the coast - ionut dobre on Unsplash"
                  className="w-full h-full object-cover"
                  width={600}
                  height={400}
                  loading="lazy"
                  decoding="async"
                  style={{
                    transform: 'translateZ(0)',
                    backfaceVisibility: 'hidden',
                    WebkitBackfaceVisibility: 'hidden',
                    willChange: 'transform',
                    transformStyle: 'preserve-3d',
                    imageRendering: '-webkit-optimize-contrast',
                  }}
                />
              </div>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
}
