var q=function(J){return q=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(G){return typeof G}:function(G){return G&&typeof Symbol=="function"&&G.constructor===Symbol&&G!==Symbol.prototype?"symbol":typeof G},q(J)},W=function(J,G){var X=Object.keys(J);if(Object.getOwnPropertySymbols){var H=Object.getOwnPropertySymbols(J);G&&(H=H.filter(function(D){return Object.getOwnPropertyDescriptor(J,D).enumerable})),X.push.apply(X,H)}return X},x=function(J){for(var G=1;G<arguments.length;G++){var X=arguments[G]!=null?arguments[G]:{};G%2?W(Object(X),!0).forEach(function(H){CC(J,H,X[H])}):Object.getOwnPropertyDescriptors?Object.defineProperties(J,Object.getOwnPropertyDescriptors(X)):W(Object(X)).forEach(function(H){Object.defineProperty(J,H,Object.getOwnPropertyDescriptor(X,H))})}return J},CC=function(J,G,X){if(G=BC(G),G in J)Object.defineProperty(J,G,{value:X,enumerable:!0,configurable:!0,writable:!0});else J[G]=X;return J},BC=function(J){var G=EC(J,"string");return q(G)=="symbol"?G:String(G)},EC=function(J,G){if(q(J)!="object"||!J)return J;var X=J[Symbol.toPrimitive];if(X!==void 0){var H=X.call(J,G||"default");if(q(H)!="object")return H;throw new TypeError("@@toPrimitive must return a primitive value.")}return(G==="string"?String:Number)(J)};(function(J){var G=Object.defineProperty,X=function C(U,B){for(var E in B)G(U,E,{get:B[E],enumerable:!0,configurable:!0,set:function Y(Z){return B[E]=function(){return Z}}})},H={lessThanXSeconds:{one:"\u5C11\u65BC 1 \u79D2",other:"\u5C11\u65BC {{count}} \u79D2"},xSeconds:{one:"1 \u79D2",other:"{{count}} \u79D2"},halfAMinute:"\u534A\u5206\u9418",lessThanXMinutes:{one:"\u5C11\u65BC 1 \u5206\u9418",other:"\u5C11\u65BC {{count}} \u5206\u9418"},xMinutes:{one:"1 \u5206\u9418",other:"{{count}} \u5206\u9418"},xHours:{one:"1 \u5C0F\u6642",other:"{{count}} \u5C0F\u6642"},aboutXHours:{one:"\u5927\u7D04 1 \u5C0F\u6642",other:"\u5927\u7D04 {{count}} \u5C0F\u6642"},xDays:{one:"1 \u5929",other:"{{count}} \u5929"},aboutXWeeks:{one:"\u5927\u7D04 1 \u500B\u661F\u671F",other:"\u5927\u7D04 {{count}} \u500B\u661F\u671F"},xWeeks:{one:"1 \u500B\u661F\u671F",other:"{{count}} \u500B\u661F\u671F"},aboutXMonths:{one:"\u5927\u7D04 1 \u500B\u6708",other:"\u5927\u7D04 {{count}} \u500B\u6708"},xMonths:{one:"1 \u500B\u6708",other:"{{count}} \u500B\u6708"},aboutXYears:{one:"\u5927\u7D04 1 \u5E74",other:"\u5927\u7D04 {{count}} \u5E74"},xYears:{one:"1 \u5E74",other:"{{count}} \u5E74"},overXYears:{one:"\u8D85\u904E 1 \u5E74",other:"\u8D85\u904E {{count}} \u5E74"},almostXYears:{one:"\u5C07\u8FD1 1 \u5E74",other:"\u5C07\u8FD1 {{count}} \u5E74"}},D=function C(U,B,E){var Y,Z=H[U];if(typeof Z==="string")Y=Z;else if(B===1)Y=Z.one;else Y=Z.other.replace("{{count}}",String(B));if(E!==null&&E!==void 0&&E.addSuffix)if(E.comparison&&E.comparison>0)return Y+"\u5167";else return Y+"\u524D";return Y};function N(C){return function(){var U=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},B=U.width?String(U.width):C.defaultWidth,E=C.formats[B]||C.formats[C.defaultWidth];return E}}var $={full:"y'\u5E74'M'\u6708'd'\u65E5' EEEE",long:"y'\u5E74'M'\u6708'd'\u65E5'",medium:"yyyy-MM-dd",short:"yy-MM-dd"},z={full:"zzzz a h:mm:ss",long:"z a h:mm:ss",medium:"a h:mm:ss",short:"a h:mm"},M={full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},R={date:N({formats:$,defaultWidth:"full"}),time:N({formats:z,defaultWidth:"full"}),dateTime:N({formats:M,defaultWidth:"full"})},L={lastWeek:"'\u4E0A\u500B'eeee p",yesterday:"'\u6628\u5929' p",today:"'\u4ECA\u5929' p",tomorrow:"'\u660E\u5929' p",nextWeek:"'\u4E0B\u500B'eeee p",other:"P"},V=function C(U,B,E,Y){return L[U]};function O(C){return function(U,B){var E=B!==null&&B!==void 0&&B.context?String(B.context):"standalone",Y;if(E==="formatting"&&C.formattingValues){var Z=C.defaultFormattingWidth||C.defaultWidth,I=B!==null&&B!==void 0&&B.width?String(B.width):Z;Y=C.formattingValues[I]||C.formattingValues[Z]}else{var T=C.defaultWidth,K=B!==null&&B!==void 0&&B.width?String(B.width):C.defaultWidth;Y=C.values[K]||C.values[T]}var A=C.argumentCallback?C.argumentCallback(U):U;return Y[A]}}var j={narrow:["\u524D","\u516C\u5143"],abbreviated:["\u524D","\u516C\u5143"],wide:["\u516C\u5143\u524D","\u516C\u5143"]},f={narrow:["1","2","3","4"],abbreviated:["\u7B2C\u4E00\u5B63","\u7B2C\u4E8C\u5B63","\u7B2C\u4E09\u5B63","\u7B2C\u56DB\u5B63"],wide:["\u7B2C\u4E00\u5B63\u5EA6","\u7B2C\u4E8C\u5B63\u5EA6","\u7B2C\u4E09\u5B63\u5EA6","\u7B2C\u56DB\u5B63\u5EA6"]},F={narrow:["\u4E00","\u4E8C","\u4E09","\u56DB","\u4E94","\u516D","\u4E03","\u516B","\u4E5D","\u5341","\u5341\u4E00","\u5341\u4E8C"],abbreviated:["1\u6708","2\u6708","3\u6708","4\u6708","5\u6708","6\u6708","7\u6708","8\u6708","9\u6708","10\u6708","11\u6708","12\u6708"],wide:["\u4E00\u6708","\u4E8C\u6708","\u4E09\u6708","\u56DB\u6708","\u4E94\u6708","\u516D\u6708","\u4E03\u6708","\u516B\u6708","\u4E5D\u6708","\u5341\u6708","\u5341\u4E00\u6708","\u5341\u4E8C\u6708"]},P={narrow:["\u65E5","\u4E00","\u4E8C","\u4E09","\u56DB","\u4E94","\u516D"],short:["\u65E5","\u4E00","\u4E8C","\u4E09","\u56DB","\u4E94","\u516D"],abbreviated:["\u9031\u65E5","\u9031\u4E00","\u9031\u4E8C","\u9031\u4E09","\u9031\u56DB","\u9031\u4E94","\u9031\u516D"],wide:["\u661F\u671F\u65E5","\u661F\u671F\u4E00","\u661F\u671F\u4E8C","\u661F\u671F\u4E09","\u661F\u671F\u56DB","\u661F\u671F\u4E94","\u661F\u671F\u516D"]},_={narrow:{am:"\u4E0A",pm:"\u4E0B",midnight:"\u5348\u591C",noon:"\u664C",morning:"\u65E9",afternoon:"\u5348",evening:"\u665A",night:"\u591C"},abbreviated:{am:"\u4E0A\u5348",pm:"\u4E0B\u5348",midnight:"\u5348\u591C",noon:"\u4E2D\u5348",morning:"\u4E0A\u5348",afternoon:"\u4E0B\u5348",evening:"\u665A\u4E0A",night:"\u591C\u665A"},wide:{am:"\u4E0A\u5348",pm:"\u4E0B\u5348",midnight:"\u5348\u591C",noon:"\u4E2D\u5348",morning:"\u4E0A\u5348",afternoon:"\u4E0B\u5348",evening:"\u665A\u4E0A",night:"\u591C\u665A"}},v={narrow:{am:"\u4E0A",pm:"\u4E0B",midnight:"\u5348\u591C",noon:"\u664C",morning:"\u65E9",afternoon:"\u5348",evening:"\u665A",night:"\u591C"},abbreviated:{am:"\u4E0A\u5348",pm:"\u4E0B\u5348",midnight:"\u5348\u591C",noon:"\u4E2D\u5348",morning:"\u4E0A\u5348",afternoon:"\u4E0B\u5348",evening:"\u665A\u4E0A",night:"\u591C\u665A"},wide:{am:"\u4E0A\u5348",pm:"\u4E0B\u5348",midnight:"\u5348\u591C",noon:"\u4E2D\u5348",morning:"\u4E0A\u5348",afternoon:"\u4E0B\u5348",evening:"\u665A\u4E0A",night:"\u591C\u665A"}},w=function C(U,B){var E=Number(U);switch(B===null||B===void 0?void 0:B.unit){case"date":return E+"\u65E5";case"hour":return E+"\u6642";case"minute":return E+"\u5206";case"second":return E+"\u79D2";default:return"\u7B2C "+E}},k={ordinalNumber:w,era:O({values:j,defaultWidth:"wide"}),quarter:O({values:f,defaultWidth:"wide",argumentCallback:function C(U){return U-1}}),month:O({values:F,defaultWidth:"wide"}),day:O({values:P,defaultWidth:"wide"}),dayPeriod:O({values:_,defaultWidth:"wide",formattingValues:v,defaultFormattingWidth:"wide"})};function Q(C){return function(U){var B=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},E=B.width,Y=E&&C.matchPatterns[E]||C.matchPatterns[C.defaultMatchWidth],Z=U.match(Y);if(!Z)return null;var I=Z[0],T=E&&C.parsePatterns[E]||C.parsePatterns[C.defaultParseWidth],K=Array.isArray(T)?h(T,function(S){return S.test(I)}):b(T,function(S){return S.test(I)}),A;A=C.valueCallback?C.valueCallback(K):K,A=B.valueCallback?B.valueCallback(A):A;var t=U.slice(I.length);return{value:A,rest:t}}}var b=function C(U,B){for(var E in U)if(Object.prototype.hasOwnProperty.call(U,E)&&B(U[E]))return E;return},h=function C(U,B){for(var E=0;E<U.length;E++)if(B(U[E]))return E;return};function m(C){return function(U){var B=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},E=U.match(C.matchPattern);if(!E)return null;var Y=E[0],Z=U.match(C.parsePattern);if(!Z)return null;var I=C.valueCallback?C.valueCallback(Z[0]):Z[0];I=B.valueCallback?B.valueCallback(I):I;var T=U.slice(Y.length);return{value:I,rest:T}}}var c=/^(第\s*)?\d+(日|時|分|秒)?/i,y=/\d+/i,d={narrow:/^(前)/i,abbreviated:/^(前)/i,wide:/^(公元前|公元)/i},p={any:[/^(前)/i,/^(公元)/i]},g={narrow:/^[1234]/i,abbreviated:/^第[一二三四]季/i,wide:/^第[一二三四]季度/i},u={any:[/(1|一)/i,/(2|二)/i,/(3|三)/i,/(4|四)/i]},l={narrow:/^(一|二|三|四|五|六|七|八|九|十[二一])/i,abbreviated:/^(一|二|三|四|五|六|七|八|九|十[二一]|\d|1[12])月/i,wide:/^(一|二|三|四|五|六|七|八|九|十[二一])月/i},i={narrow:[/^一/i,/^二/i,/^三/i,/^四/i,/^五/i,/^六/i,/^七/i,/^八/i,/^九/i,/^十(?!(一|二))/i,/^十一/i,/^十二/i],any:[/^一|1/i,/^二|2/i,/^三|3/i,/^四|4/i,/^五|5/i,/^六|6/i,/^七|7/i,/^八|8/i,/^九|9/i,/^十(?!(一|二))|10/i,/^十一|11/i,/^十二|12/i]},n={narrow:/^[一二三四五六日]/i,short:/^[一二三四五六日]/i,abbreviated:/^週[一二三四五六日]/i,wide:/^星期[一二三四五六日]/i},s={any:[/日/i,/一/i,/二/i,/三/i,/四/i,/五/i,/六/i]},o={any:/^(上午?|下午?|午夜|[中正]午|早上?|下午|晚上?|凌晨)/i},r={any:{am:/^上午?/i,pm:/^下午?/i,midnight:/^午夜/i,noon:/^[中正]午/i,morning:/^早上/i,afternoon:/^下午/i,evening:/^晚上?/i,night:/^凌晨/i}},e={ordinalNumber:m({matchPattern:c,parsePattern:y,valueCallback:function C(U){return parseInt(U,10)}}),era:Q({matchPatterns:d,defaultMatchWidth:"wide",parsePatterns:p,defaultParseWidth:"any"}),quarter:Q({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any",valueCallback:function C(U){return U+1}}),month:Q({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any"}),day:Q({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),dayPeriod:Q({matchPatterns:o,defaultMatchWidth:"any",parsePatterns:r,defaultParseWidth:"any"})},a={code:"zh-HK",formatDistance:D,formatLong:R,formatRelative:V,localize:k,match:e,options:{weekStartsOn:0,firstWeekContainsDate:1}};window.dateFns=x(x({},window.dateFns),{},{locale:x(x({},(J=window.dateFns)===null||J===void 0?void 0:J.locale),{},{zhHK:a})})})();

//# debugId=9164AC736F39F97964756e2164756e21
