import { useLanguage } from '@/hooks/use-language';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Award, Heart, Lightbulb, Leaf } from 'lucide-react';

export function AboutValues() {
  const { t } = useLanguage();

  const values = [
    {
      icon: Award,
      title: t('about.qualityTitle'),
      description: t('about.qualityDescription'),
      color: 'text-gold',
    },
    {
      icon: Heart,
      title: t('about.heritageTitle'),
      description: t('about.heritageDescription'),
      color: 'text-bronze',
    },
    {
      icon: Lightbulb,
      title: t('about.innovationTitle'),
      description: t('about.innovationDescription'),
      color: 'text-gold',
    },
    {
      icon: Leaf,
      title: t('about.sustainabilityTitle'),
      description: t('about.sustainabilityDescription'),
      color: 'text-bronze',
    },
  ];

  return (
    <section className="py-20 luxury-gradient">
      <div className="max-w-7xl mx-auto px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="font-playfair text-4xl lg:text-5xl font-bold text-charcoal mb-6">{t('about.ourValues')}</h2>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {values.map((value, index) => {
            const IconComponent = value.icon;
            return (
              <Card key={index} className="product-card group cursor-default bg-white elegant-shadow">
                <CardHeader className="text-center pb-4">
                  <div className="flex justify-center mb-4">
                    <div 
                      className="w-16 h-16 rounded-full bg-warm-white flex items-center justify-center transform transition-transform duration-200 will-change-transform"
                      style={{
                        transform: 'translateZ(0)',
                        backfaceVisibility: 'hidden',
                        WebkitBackfaceVisibility: 'hidden',
                      }}
                      onMouseEnter={(e) => e.currentTarget.style.transform = 'translateZ(0) scale(1.1)'}
                      onMouseLeave={(e) => e.currentTarget.style.transform = 'translateZ(0) scale(1)'}>
                      <IconComponent className={`h-8 w-8 ${value.color}`} />
                    </div>
                  </div>
                  <CardTitle className="font-playfair text-xl text-charcoal">{value.title}</CardTitle>
                </CardHeader>
                <CardContent className="text-center pt-0">
                  <p className="text-medium-gray leading-relaxed">{value.description}</p>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>
    </section>
  );
}
