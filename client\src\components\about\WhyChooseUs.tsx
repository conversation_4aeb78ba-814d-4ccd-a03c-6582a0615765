import { useLanguage } from '@/hooks/use-language';
import { Card, CardContent } from '@/components/ui/card';
import { Clock, Star, Headphones, Handshake } from 'lucide-react';

export function WhyChooseUs() {
  const { t } = useLanguage();

  const reasons = [
    {
      icon: Clock,
      title: t('about.experienceTitle'),
      description: t('about.experienceDescription'),
      gradient: 'from-gold to-bronze',
    },
    {
      icon: Star,
      title: t('about.curatedTitle'),
      description: t('about.curatedDescription'),
      gradient: 'from-bronze to-gold',
    },
    {
      icon: Headphones,
      title: t('about.serviceTitle'),
      description: t('about.serviceDescription'),
      gradient: 'from-gold to-bronze',
    },
    {
      icon: Handshake,
      title: t('about.partnershipTitle'),
      description: t('about.partnershipDescription'),
      gradient: 'from-bronze to-gold',
    },
  ];

  return (
    <section className="py-20 bg-charcoal text-white">
      <div className="max-w-7xl mx-auto px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="font-playfair text-4xl lg:text-5xl font-bold mb-6">{t('about.whyChooseUs')}</h2>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {reasons.map((reason, index) => {
            const IconComponent = reason.icon;
            return (
              <Card
                key={index}
                className="product-card group cursor-default bg-white/5 backdrop-blur-sm border-white/10 hover:bg-white/10 transition-all duration-300 ease-out"
                style={{
                  transitionProperty: 'transform, background-color, box-shadow',
                  transform: 'translateZ(0)',
                  willChange: 'transform, background-color, box-shadow',
                }}
                onMouseEnter={e => {
                  e.currentTarget.style.transform = 'translateZ(0) scale(1.02)';
                  e.currentTarget.style.boxShadow = '0 10px 30px -5px rgba(0, 0, 0, 0.1)';
                }}
                onMouseLeave={e => {
                  e.currentTarget.style.transform = 'translateZ(0) scale(1)';
                  e.currentTarget.style.boxShadow = '';
                }}>
                <CardContent className="p-8 text-center">
                  <div className="flex justify-center mb-6">
                    <div
                      className={`w-16 h-16 rounded-full bg-gradient-to-br ${reason.gradient} flex items-center justify-center transform transition-all duration-300 ease-out will-change-transform`}
                      style={{
                        transform: 'translateZ(0) scale(1)',
                        backfaceVisibility: 'hidden',
                        WebkitBackfaceVisibility: 'hidden',
                        transitionProperty: 'transform, box-shadow',
                        willChange: 'transform',
                      }}
                      onMouseEnter={e => (e.currentTarget.style.transform = 'translateZ(0) scale(1.15)')}
                      onMouseLeave={e => (e.currentTarget.style.transform = 'translateZ(0) scale(1)')}>
                      <IconComponent className="h-8 w-8 text-white transition-transform duration-300 ease-out" />
                    </div>
                  </div>
                  <h3 className="font-playfair text-xl font-semibold mb-4 text-gold">{reason.title}</h3>
                  <p className="text-gray-300 leading-relaxed">{reason.description}</p>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>
    </section>
  );
}
