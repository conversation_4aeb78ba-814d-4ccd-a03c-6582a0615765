"use strict";
exports.mn = void 0;
var _index = require("./mn/_lib/formatDistance.js");
var _index2 = require("./mn/_lib/formatLong.js");
var _index3 = require("./mn/_lib/formatRelative.js");
var _index4 = require("./mn/_lib/localize.js");
var _index5 = require("./mn/_lib/match.js");

/**
 * @category Locales
 * @summary Mongolian locale.
 * @language Mongolian
 * @iso-639-2 mon
 * <AUTHOR> [@bilguun0203](https://github.com/bilguun0203)
 */
const mn = (exports.mn = {
  code: "mn",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 1,
  },
});
