import { Link } from 'wouter';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useLanguage } from '@/hooks/use-language';
import { Product } from '@shared/schema';
import { Sparkles } from 'lucide-react';

interface HeroProps {
  featuredProduct?: Product;
  isLoading?: boolean;
}

export function Hero({ featuredProduct, isLoading = false }: HeroProps) {
  const { t, language } = useLanguage();

  return (
    <section className="relative min-h-screen flex items-center justify-center pt-20">
      <div className="absolute inset-0 z-0">
        <img
          src="https://images.unsplash.com/photo-1618220179428-22790b461013?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1920&h=1080"
          alt="Luxury marble background"
          className="w-full h-full object-cover opacity-30"
        />
        <div className="absolute inset-0 luxury-gradient opacity-90"></div>
      </div>
      {/* Hero content box */}
      <div className="relative z-10 max-w-7xl mx-auto px-6 lg:px-8">
        <div className="grid md:grid-cols-2 gap-16 items-center">
          <div className={`text-center ${language === 'en' ? 'lg:text-left' : 'lg:text-right'} order-2 md:order-1`}>
            <h1 className="font-playfair text-5xl lg:text-7xl font-bold text-charcoal leading-tight mb-6">
              {t('hero.title')}
              <span className="block text-gold">{t('hero.subtitle')}</span>
            </h1>
            <p className="text-xl text-medium-gray leading-relaxed mb-8 max-w-2xl">{t('hero.description')}</p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <Link href="/products">
                <Button
                  size="lg"
                  className="px-8 py-4 gold-gradient text-white font-semibold hover:shadow-lg transform hover:scale-105 transition-all duration-300">
                  <Sparkles className="mr-2 h-5 w-5" />
                  {t('hero.exploreCollection')}
                </Button>
              </Link>
              <Link href="#categories">
                <Button
                  variant="outline"
                  size="lg"
                  className="px-8 py-4 border-2 border-charcoal text-charcoal hover:bg-charcoal hover:text-white transition-all duration-300">
                  {t('hero.downloadCatalog')}
                </Button>
              </Link>
            </div>
          </div>
          <div className="order-1 md:order-2">
            <img
              src="https://images.unsplash.com/photo-1600585154340-be6161a56a0c"
              alt="Modern luxury house"
              className="rounded-xl shadow-lg object-cover w-full h-full"
            />
          </div>
        </div>
      </div>
    </section>
  );
}
