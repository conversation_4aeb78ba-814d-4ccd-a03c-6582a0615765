!function(e,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports):"function"==typeof define&&define.amd?define(["exports"],n):n(e.regexparam={})}(this,(function(e){var n=/(\/|^)([:*][^/]*?)(\?)?(?=[/.]|$)/g;e.inject=function(e,t){return e.replace(n,(e,n,i,r)=>(e=t["*"==i?i:i.substring(1)])?"/"+e:r||"*"==i?"":"/"+i)},e.parse=function(e,n){if(e instanceof RegExp)return{keys:!1,pattern:e};var t,i,r,f,s=[],p="",o=e.split("/");for(o[0]||o.shift();r=o.shift();)"*"===(t=r[0])?(s.push(t),p+="?"===r[1]?"(?:/(.*))?":"/(.*)"):":"===t?(i=r.indexOf("?",1),f=r.indexOf(".",1),s.push(r.substring(1,~i?i:~f?f:r.length)),p+=~i&&!~f?"(?:/([^/]+?))?":"/([^/]+?)",~f&&(p+=(~i?"?":"")+"\\"+r.substring(f))):p+="/"+r;return{keys:s,pattern:new RegExp("^"+p+(n?"(?=$|/)":"/?$"),"i")}}}));