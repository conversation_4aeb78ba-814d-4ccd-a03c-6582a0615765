var Q=function(H){return Q=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(G){return typeof G}:function(G){return G&&typeof Symbol=="function"&&G.constructor===Symbol&&G!==Symbol.prototype?"symbol":typeof G},Q(H)},z=function(H,G){var X=Object.keys(H);if(Object.getOwnPropertySymbols){var Z=Object.getOwnPropertySymbols(H);G&&(Z=Z.filter(function(x){return Object.getOwnPropertyDescriptor(H,x).enumerable})),X.push.apply(X,Z)}return X},K=function(H){for(var G=1;G<arguments.length;G++){var X=arguments[G]!=null?arguments[G]:{};G%2?z(Object(X),!0).forEach(function(Z){B0(H,Z,X[Z])}):Object.getOwnPropertyDescriptors?Object.defineProperties(H,Object.getOwnPropertyDescriptors(X)):z(Object(X)).forEach(function(Z){Object.defineProperty(H,Z,Object.getOwnPropertyDescriptor(X,Z))})}return H},B0=function(H,G,X){if(G=I0(G),G in H)Object.defineProperty(H,G,{value:X,enumerable:!0,configurable:!0,writable:!0});else H[G]=X;return H},I0=function(H){var G=U0(H,"string");return Q(G)=="symbol"?G:String(G)},U0=function(H,G){if(Q(H)!="object"||!H)return H;var X=H[Symbol.toPrimitive];if(X!==void 0){var Z=X.call(H,G||"default");if(Q(Z)!="object")return Z;throw new TypeError("@@toPrimitive must return a primitive value.")}return(G==="string"?String:Number)(H)};(function(H){var G=Object.defineProperty,X=function C(I,B){for(var U in B)G(I,U,{get:B[U],enumerable:!0,configurable:!0,set:function J(Y){return B[U]=function(){return Y}}})},Z={lessThanXSeconds:{one:"\u0441\u0435\u043A\u0443\u043D\u0434 \u0445\u04AF\u0440\u044D\u0445\u0433\u04AF\u0439",other:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434 \u0445\u04AF\u0440\u044D\u0445\u0433\u04AF\u0439"},xSeconds:{one:"1 \u0441\u0435\u043A\u0443\u043D\u0434",other:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434"},halfAMinute:"\u0445\u0430\u0433\u0430\u0441 \u043C\u0438\u043D\u0443\u0442",lessThanXMinutes:{one:"\u043C\u0438\u043D\u0443\u0442 \u0445\u04AF\u0440\u044D\u0445\u0433\u04AF\u0439",other:"{{count}} \u043C\u0438\u043D\u0443\u0442 \u0445\u04AF\u0440\u044D\u0445\u0433\u04AF\u0439"},xMinutes:{one:"1 \u043C\u0438\u043D\u0443\u0442",other:"{{count}} \u043C\u0438\u043D\u0443\u0442"},aboutXHours:{one:"\u043E\u0439\u0440\u043E\u043B\u0446\u043E\u043E\u0433\u043E\u043E\u0440 1 \u0446\u0430\u0433",other:"\u043E\u0439\u0440\u043E\u043B\u0446\u043E\u043E\u0433\u043E\u043E\u0440 {{count}} \u0446\u0430\u0433"},xHours:{one:"1 \u0446\u0430\u0433",other:"{{count}} \u0446\u0430\u0433"},xDays:{one:"1 \u04E9\u0434\u04E9\u0440",other:"{{count}} \u04E9\u0434\u04E9\u0440"},aboutXWeeks:{one:"\u043E\u0439\u0440\u043E\u043B\u0446\u043E\u043E\u0433\u043E\u043E\u0440 1 \u0434\u043E\u043B\u043E\u043E \u0445\u043E\u043D\u043E\u0433",other:"\u043E\u0439\u0440\u043E\u043B\u0446\u043E\u043E\u0433\u043E\u043E\u0440 {{count}} \u0434\u043E\u043B\u043E\u043E \u0445\u043E\u043D\u043E\u0433"},xWeeks:{one:"1 \u0434\u043E\u043B\u043E\u043E \u0445\u043E\u043D\u043E\u0433",other:"{{count}} \u0434\u043E\u043B\u043E\u043E \u0445\u043E\u043D\u043E\u0433"},aboutXMonths:{one:"\u043E\u0439\u0440\u043E\u043B\u0446\u043E\u043E\u0433\u043E\u043E\u0440 1 \u0441\u0430\u0440",other:"\u043E\u0439\u0440\u043E\u043B\u0446\u043E\u043E\u0433\u043E\u043E\u0440 {{count}} \u0441\u0430\u0440"},xMonths:{one:"1 \u0441\u0430\u0440",other:"{{count}} \u0441\u0430\u0440"},aboutXYears:{one:"\u043E\u0439\u0440\u043E\u043B\u0446\u043E\u043E\u0433\u043E\u043E\u0440 1 \u0436\u0438\u043B",other:"\u043E\u0439\u0440\u043E\u043B\u0446\u043E\u043E\u0433\u043E\u043E\u0440 {{count}} \u0436\u0438\u043B"},xYears:{one:"1 \u0436\u0438\u043B",other:"{{count}} \u0436\u0438\u043B"},overXYears:{one:"1 \u0436\u0438\u043B \u0433\u0430\u0440\u0430\u043D",other:"{{count}} \u0436\u0438\u043B \u0433\u0430\u0440\u0430\u043D"},almostXYears:{one:"\u0431\u0430\u0440\u0430\u0433 1 \u0436\u0438\u043B",other:"\u0431\u0430\u0440\u0430\u0433 {{count}} \u0436\u0438\u043B"}},x=function C(I,B,U){var J,Y=Z[I];if(typeof Y==="string")J=Y;else if(B===1)J=Y.one;else J=Y.other.replace("{{count}}",String(B));if(U!==null&&U!==void 0&&U.addSuffix){var A=J.split(" "),E=A.pop();switch(J=A.join(" "),E){case"\u0441\u0435\u043A\u0443\u043D\u0434":J+=" \u0441\u0435\u043A\u0443\u043D\u0434\u0438\u0439\u043D";break;case"\u043C\u0438\u043D\u0443\u0442":J+=" \u043C\u0438\u043D\u0443\u0442\u044B\u043D";break;case"\u0446\u0430\u0433":J+=" \u0446\u0430\u0433\u0438\u0439\u043D";break;case"\u04E9\u0434\u04E9\u0440":J+=" \u04E9\u0434\u0440\u0438\u0439\u043D";break;case"\u0441\u0430\u0440":J+=" \u0441\u0430\u0440\u044B\u043D";break;case"\u0436\u0438\u043B":J+=" \u0436\u0438\u043B\u0438\u0439\u043D";break;case"\u0445\u043E\u043D\u043E\u0433":J+=" \u0445\u043E\u043D\u043E\u0433\u0438\u0439\u043D";break;case"\u0433\u0430\u0440\u0430\u043D":J+=" \u0433\u0430\u0440\u0430\u043D\u044B";break;case"\u0445\u04AF\u0440\u044D\u0445\u0433\u04AF\u0439":J+=" \u0445\u04AF\u0440\u044D\u0445\u0433\u04AF\u0439 \u0445\u0443\u0433\u0430\u0446\u0430\u0430\u043D\u044B";break;default:J+=E+"-\u043D"}if(U.comparison&&U.comparison>0)return J+" \u0434\u0430\u0440\u0430\u0430";else return J+" \u04E9\u043C\u043D\u04E9"}return J};function N(C){return function(){var I=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},B=I.width?String(I.width):C.defaultWidth,U=C.formats[B]||C.formats[C.defaultWidth];return U}}var M={full:"y '\u043E\u043D\u044B' MMMM'\u044B\u043D' d, EEEE '\u0433\u0430\u0440\u0430\u0433'",long:"y '\u043E\u043D\u044B' MMMM'\u044B\u043D' d",medium:"y '\u043E\u043D\u044B' MMM'\u044B\u043D' d",short:"y.MM.dd"},W={full:"H:mm:ss zzzz",long:"H:mm:ss z",medium:"H:mm:ss",short:"H:mm"},S={full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},L={date:N({formats:M,defaultWidth:"full"}),time:N({formats:W,defaultWidth:"full"}),dateTime:N({formats:S,defaultWidth:"full"})},R={lastWeek:"'\u04E9\u043D\u0433\u04E9\u0440\u0441\u04E9\u043D' eeee '\u0433\u0430\u0440\u0430\u0433\u0438\u0439\u043D' p '\u0446\u0430\u0433\u0442'",yesterday:"'\u04E9\u0447\u0438\u0433\u0434\u04E9\u0440' p '\u0446\u0430\u0433\u0442'",today:"'\u04E9\u043D\u04E9\u04E9\u0434\u04E9\u0440' p '\u0446\u0430\u0433\u0442'",tomorrow:"'\u043C\u0430\u0440\u0433\u0430\u0430\u0448' p '\u0446\u0430\u0433\u0442'",nextWeek:"'\u0438\u0440\u044D\u0445' eeee '\u0433\u0430\u0440\u0430\u0433\u0438\u0439\u043D' p '\u0446\u0430\u0433\u0442'",other:"P"},V=function C(I,B,U,J){return R[I]};function q(C){return function(I,B){var U=B!==null&&B!==void 0&&B.context?String(B.context):"standalone",J;if(U==="formatting"&&C.formattingValues){var Y=C.defaultFormattingWidth||C.defaultWidth,A=B!==null&&B!==void 0&&B.width?String(B.width):Y;J=C.formattingValues[A]||C.formattingValues[Y]}else{var E=C.defaultWidth,D=B!==null&&B!==void 0&&B.width?String(B.width):C.defaultWidth;J=C.values[D]||C.values[E]}var T=C.argumentCallback?C.argumentCallback(I):I;return J[T]}}var f={narrow:["\u041D\u0422\u04E8","\u041D\u0422"],abbreviated:["\u041D\u0422\u04E8","\u041D\u0422"],wide:["\u043D\u0438\u0439\u0442\u0438\u0439\u043D \u0442\u043E\u043E\u043B\u043B\u044B\u043D \u04E9\u043C\u043D\u04E9\u0445","\u043D\u0438\u0439\u0442\u0438\u0439\u043D \u0442\u043E\u043E\u043B\u043B\u044B\u043D"]},j={narrow:["I","II","III","IV"],abbreviated:["I \u0443\u043B\u0438\u0440\u0430\u043B","II \u0443\u043B\u0438\u0440\u0430\u043B","III \u0443\u043B\u0438\u0440\u0430\u043B","IV \u0443\u043B\u0438\u0440\u0430\u043B"],wide:["1-\u0440 \u0443\u043B\u0438\u0440\u0430\u043B","2-\u0440 \u0443\u043B\u0438\u0440\u0430\u043B","3-\u0440 \u0443\u043B\u0438\u0440\u0430\u043B","4-\u0440 \u0443\u043B\u0438\u0440\u0430\u043B"]},v={narrow:["I","II","III","IV","V","VI","VII","VIII","IX","X","XI","XII"],abbreviated:["1-\u0440 \u0441\u0430\u0440","2-\u0440 \u0441\u0430\u0440","3-\u0440 \u0441\u0430\u0440","4-\u0440 \u0441\u0430\u0440","5-\u0440 \u0441\u0430\u0440","6-\u0440 \u0441\u0430\u0440","7-\u0440 \u0441\u0430\u0440","8-\u0440 \u0441\u0430\u0440","9-\u0440 \u0441\u0430\u0440","10-\u0440 \u0441\u0430\u0440","11-\u0440 \u0441\u0430\u0440","12-\u0440 \u0441\u0430\u0440"],wide:["\u041D\u044D\u0433\u0434\u04AF\u0433\u044D\u044D\u0440 \u0441\u0430\u0440","\u0425\u043E\u0451\u0440\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440","\u0413\u0443\u0440\u0430\u0432\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440","\u0414\u04E9\u0440\u04E9\u0432\u0434\u04AF\u0433\u044D\u044D\u0440 \u0441\u0430\u0440","\u0422\u0430\u0432\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440","\u0417\u0443\u0440\u0433\u0430\u0430\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440","\u0414\u043E\u043B\u043E\u043E\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440","\u041D\u0430\u0439\u043C\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440","\u0415\u0441\u0434\u04AF\u0433\u044D\u044D\u0440 \u0441\u0430\u0440","\u0410\u0440\u0430\u0432\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440","\u0410\u0440\u0432\u0430\u043D\u043D\u044D\u0433\u0434\u04AF\u0433\u044D\u044D\u0440 \u0441\u0430\u0440","\u0410\u0440\u0432\u0430\u043D \u0445\u043E\u0451\u0440\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440"]},P={narrow:["I","II","III","IV","V","VI","VII","VIII","IX","X","XI","XII"],abbreviated:["1-\u0440 \u0441\u0430\u0440","2-\u0440 \u0441\u0430\u0440","3-\u0440 \u0441\u0430\u0440","4-\u0440 \u0441\u0430\u0440","5-\u0440 \u0441\u0430\u0440","6-\u0440 \u0441\u0430\u0440","7-\u0440 \u0441\u0430\u0440","8-\u0440 \u0441\u0430\u0440","9-\u0440 \u0441\u0430\u0440","10-\u0440 \u0441\u0430\u0440","11-\u0440 \u0441\u0430\u0440","12-\u0440 \u0441\u0430\u0440"],wide:["\u043D\u044D\u0433\u0434\u04AF\u0433\u044D\u044D\u0440 \u0441\u0430\u0440","\u0445\u043E\u0451\u0440\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440","\u0433\u0443\u0440\u0430\u0432\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440","\u0434\u04E9\u0440\u04E9\u0432\u0434\u04AF\u0433\u044D\u044D\u0440 \u0441\u0430\u0440","\u0442\u0430\u0432\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440","\u0437\u0443\u0440\u0433\u0430\u0430\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440","\u0434\u043E\u043B\u043E\u043E\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440","\u043D\u0430\u0439\u043C\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440","\u0435\u0441\u0434\u04AF\u0433\u044D\u044D\u0440 \u0441\u0430\u0440","\u0430\u0440\u0430\u0432\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440","\u0430\u0440\u0432\u0430\u043D\u043D\u044D\u0433\u0434\u04AF\u0433\u044D\u044D\u0440 \u0441\u0430\u0440","\u0430\u0440\u0432\u0430\u043D \u0445\u043E\u0451\u0440\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440"]},_={narrow:["\u041D","\u0414","\u041C","\u041B","\u041F","\u0411","\u0411"],short:["\u041D\u044F","\u0414\u0430","\u041C\u044F","\u041B\u0445","\u041F\u04AF","\u0411\u0430","\u0411\u044F"],abbreviated:["\u041D\u044F\u043C","\u0414\u0430\u0432","\u041C\u044F\u0433","\u041B\u0445\u0430","\u041F\u04AF\u0440","\u0411\u0430\u0430","\u0411\u044F\u043C"],wide:["\u041D\u044F\u043C","\u0414\u0430\u0432\u0430\u0430","\u041C\u044F\u0433\u043C\u0430\u0440","\u041B\u0445\u0430\u0433\u0432\u0430","\u041F\u04AF\u0440\u044D\u0432","\u0411\u0430\u0430\u0441\u0430\u043D","\u0411\u044F\u043C\u0431\u0430"]},F={narrow:["\u041D","\u0414","\u041C","\u041B","\u041F","\u0411","\u0411"],short:["\u041D\u044F","\u0414\u0430","\u041C\u044F","\u041B\u0445","\u041F\u04AF","\u0411\u0430","\u0411\u044F"],abbreviated:["\u041D\u044F\u043C","\u0414\u0430\u0432","\u041C\u044F\u0433","\u041B\u0445\u0430","\u041F\u04AF\u0440","\u0411\u0430\u0430","\u0411\u044F\u043C"],wide:["\u043D\u044F\u043C","\u0434\u0430\u0432\u0430\u0430","\u043C\u044F\u0433\u043C\u0430\u0440","\u043B\u0445\u0430\u0433\u0432\u0430","\u043F\u04AF\u0440\u044D\u0432","\u0431\u0430\u0430\u0441\u0430\u043D","\u0431\u044F\u043C\u0431\u0430"]},w={narrow:{am:"\u04AF.\u04E9.",pm:"\u04AF.\u0445.",midnight:"\u0448\u04E9\u043D\u04E9 \u0434\u0443\u043D\u0434",noon:"\u04AF\u0434 \u0434\u0443\u043D\u0434",morning:"\u04E9\u0433\u043B\u04E9\u04E9",afternoon:"\u04E9\u0434\u04E9\u0440",evening:"\u043E\u0440\u043E\u0439",night:"\u0448\u04E9\u043D\u04E9"},abbreviated:{am:"\u04AF.\u04E9.",pm:"\u04AF.\u0445.",midnight:"\u0448\u04E9\u043D\u04E9 \u0434\u0443\u043D\u0434",noon:"\u04AF\u0434 \u0434\u0443\u043D\u0434",morning:"\u04E9\u0433\u043B\u04E9\u04E9",afternoon:"\u04E9\u0434\u04E9\u0440",evening:"\u043E\u0440\u043E\u0439",night:"\u0448\u04E9\u043D\u04E9"},wide:{am:"\u04AF.\u04E9.",pm:"\u04AF.\u0445.",midnight:"\u0448\u04E9\u043D\u04E9 \u0434\u0443\u043D\u0434",noon:"\u04AF\u0434 \u0434\u0443\u043D\u0434",morning:"\u04E9\u0433\u043B\u04E9\u04E9",afternoon:"\u04E9\u0434\u04E9\u0440",evening:"\u043E\u0440\u043E\u0439",night:"\u0448\u04E9\u043D\u04E9"}},b=function C(I,B){return String(I)},k={ordinalNumber:b,era:q({values:f,defaultWidth:"wide"}),quarter:q({values:j,defaultWidth:"wide",argumentCallback:function C(I){return I-1}}),month:q({values:v,defaultWidth:"wide",formattingValues:P,defaultFormattingWidth:"wide"}),day:q({values:_,defaultWidth:"wide",formattingValues:F,defaultFormattingWidth:"wide"}),dayPeriod:q({values:w,defaultWidth:"wide"})};function O(C){return function(I){var B=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},U=B.width,J=U&&C.matchPatterns[U]||C.matchPatterns[C.defaultMatchWidth],Y=I.match(J);if(!Y)return null;var A=Y[0],E=U&&C.parsePatterns[U]||C.parsePatterns[C.defaultParseWidth],D=Array.isArray(E)?u(E,function($){return $.test(A)}):h(E,function($){return $.test(A)}),T;T=C.valueCallback?C.valueCallback(D):D,T=B.valueCallback?B.valueCallback(T):T;var C0=I.slice(A.length);return{value:T,rest:C0}}}var h=function C(I,B){for(var U in I)if(Object.prototype.hasOwnProperty.call(I,U)&&B(I[U]))return U;return},u=function C(I,B){for(var U=0;U<I.length;U++)if(B(I[U]))return U;return};function m(C){return function(I){var B=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},U=I.match(C.matchPattern);if(!U)return null;var J=U[0],Y=I.match(C.parsePattern);if(!Y)return null;var A=C.valueCallback?C.valueCallback(Y[0]):Y[0];A=B.valueCallback?B.valueCallback(A):A;var E=I.slice(J.length);return{value:A,rest:E}}}var c=/\d+/i,y=/\d+/i,p={narrow:/^(нтө|нт)/i,abbreviated:/^(нтө|нт)/i,wide:/^(нийтийн тооллын өмнө|нийтийн тооллын)/i},g={any:[/^(нтө|нийтийн тооллын өмнө)/i,/^(нт|нийтийн тооллын)/i]},d={narrow:/^(iv|iii|ii|i)/i,abbreviated:/^(iv|iii|ii|i) улирал/i,wide:/^[1-4]-р улирал/i},l={any:[/^(i(\s|$)|1)/i,/^(ii(\s|$)|2)/i,/^(iii(\s|$)|3)/i,/^(iv(\s|$)|4)/i]},i={narrow:/^(xii|xi|x|ix|viii|vii|vi|v|iv|iii|ii|i)/i,abbreviated:/^(1-р сар|2-р сар|3-р сар|4-р сар|5-р сар|6-р сар|7-р сар|8-р сар|9-р сар|10-р сар|11-р сар|12-р сар)/i,wide:/^(нэгдүгээр сар|хоёрдугаар сар|гуравдугаар сар|дөрөвдүгээр сар|тавдугаар сар|зургаадугаар сар|долоодугаар сар|наймдугаар сар|есдүгээр сар|аравдугаар сар|арван нэгдүгээр сар|арван хоёрдугаар сар)/i},n={narrow:[/^i$/i,/^ii$/i,/^iii$/i,/^iv$/i,/^v$/i,/^vi$/i,/^vii$/i,/^viii$/i,/^ix$/i,/^x$/i,/^xi$/i,/^xii$/i],any:[/^(1|нэгдүгээр)/i,/^(2|хоёрдугаар)/i,/^(3|гуравдугаар)/i,/^(4|дөрөвдүгээр)/i,/^(5|тавдугаар)/i,/^(6|зургаадугаар)/i,/^(7|долоодугаар)/i,/^(8|наймдугаар)/i,/^(9|есдүгээр)/i,/^(10|аравдугаар)/i,/^(11|арван нэгдүгээр)/i,/^(12|арван хоёрдугаар)/i]},o={narrow:/^[ндмлпбб]/i,short:/^(ня|да|мя|лх|пү|ба|бя)/i,abbreviated:/^(ням|дав|мяг|лха|пүр|баа|бям)/i,wide:/^(ням|даваа|мягмар|лхагва|пүрэв|баасан|бямба)/i},s={narrow:[/^н/i,/^д/i,/^м/i,/^л/i,/^п/i,/^б/i,/^б/i],any:[/^ня/i,/^да/i,/^мя/i,/^лх/i,/^пү/i,/^ба/i,/^бя/i]},r={narrow:/^(ү\.ө\.|ү\.х\.|шөнө дунд|үд дунд|өглөө|өдөр|орой|шөнө)/i,any:/^(ү\.ө\.|ү\.х\.|шөнө дунд|үд дунд|өглөө|өдөр|орой|шөнө)/i},a={any:{am:/^ү\.ө\./i,pm:/^ү\.х\./i,midnight:/^шөнө дунд/i,noon:/^үд дунд/i,morning:/өглөө/i,afternoon:/өдөр/i,evening:/орой/i,night:/шөнө/i}},e={ordinalNumber:m({matchPattern:c,parsePattern:y,valueCallback:function C(I){return parseInt(I,10)}}),era:O({matchPatterns:p,defaultMatchWidth:"wide",parsePatterns:g,defaultParseWidth:"any"}),quarter:O({matchPatterns:d,defaultMatchWidth:"wide",parsePatterns:l,defaultParseWidth:"any",valueCallback:function C(I){return I+1}}),month:O({matchPatterns:i,defaultMatchWidth:"wide",parsePatterns:n,defaultParseWidth:"any"}),day:O({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),dayPeriod:O({matchPatterns:r,defaultMatchWidth:"any",parsePatterns:a,defaultParseWidth:"any"})},t={code:"mn",formatDistance:x,formatLong:L,formatRelative:V,localize:k,match:e,options:{weekStartsOn:1,firstWeekContainsDate:1}};window.dateFns=K(K({},window.dateFns),{},{locale:K(K({},(H=window.dateFns)===null||H===void 0?void 0:H.locale),{},{mn:t})})})();

//# debugId=A85E6E61577578EC64756e2164756e21
