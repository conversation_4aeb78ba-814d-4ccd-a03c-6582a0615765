var A=function(U){return A=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(J){return typeof J}:function(J){return J&&typeof Symbol=="function"&&J.constructor===Symbol&&J!==Symbol.prototype?"symbol":typeof J},A(U)},z=function(U,J){var X=Object.keys(U);if(Object.getOwnPropertySymbols){var I=Object.getOwnPropertySymbols(U);J&&(I=I.filter(function(D){return Object.getOwnPropertyDescriptor(U,D).enumerable})),X.push.apply(X,I)}return X},x=function(U){for(var J=1;J<arguments.length;J++){var X=arguments[J]!=null?arguments[J]:{};J%2?z(Object(X),!0).forEach(function(I){t(U,I,X[I])}):Object.getOwnPropertyDescriptors?Object.defineProperties(U,Object.getOwnPropertyDescriptors(X)):z(Object(X)).forEach(function(I){Object.defineProperty(U,I,Object.getOwnPropertyDescriptor(X,I))})}return U},t=function(U,J,X){if(J=HH(J),J in U)Object.defineProperty(U,J,{value:X,enumerable:!0,configurable:!0,writable:!0});else U[J]=X;return U},HH=function(U){var J=BH(U,"string");return A(J)=="symbol"?J:String(J)},BH=function(U,J){if(A(U)!="object"||!U)return U;var X=U[Symbol.toPrimitive];if(X!==void 0){var I=X.call(U,J||"default");if(A(I)!="object")return I;throw new TypeError("@@toPrimitive must return a primitive value.")}return(J==="string"?String:Number)(U)};(function(U){var J=Object.defineProperty,X=function H(C,B){for(var G in B)J(C,G,{get:B[G],enumerable:!0,configurable:!0,set:function Y(Z){return B[G]=function(){return Z}}})},I={lessThanXSeconds:{one:"malpli ol sekundo",other:"malpli ol {{count}} sekundoj"},xSeconds:{one:"1 sekundo",other:"{{count}} sekundoj"},halfAMinute:"duonminuto",lessThanXMinutes:{one:"malpli ol minuto",other:"malpli ol {{count}} minutoj"},xMinutes:{one:"1 minuto",other:"{{count}} minutoj"},aboutXHours:{one:"proksimume 1 horo",other:"proksimume {{count}} horoj"},xHours:{one:"1 horo",other:"{{count}} horoj"},xDays:{one:"1 tago",other:"{{count}} tagoj"},aboutXMonths:{one:"proksimume 1 monato",other:"proksimume {{count}} monatoj"},xWeeks:{one:"1 semajno",other:"{{count}} semajnoj"},aboutXWeeks:{one:"proksimume 1 semajno",other:"proksimume {{count}} semajnoj"},xMonths:{one:"1 monato",other:"{{count}} monatoj"},aboutXYears:{one:"proksimume 1 jaro",other:"proksimume {{count}} jaroj"},xYears:{one:"1 jaro",other:"{{count}} jaroj"},overXYears:{one:"pli ol 1 jaro",other:"pli ol {{count}} jaroj"},almostXYears:{one:"preska\u016D 1 jaro",other:"preska\u016D {{count}} jaroj"}},D=function H(C,B,G){var Y,Z=I[C];if(typeof Z==="string")Y=Z;else if(B===1)Y=Z.one;else Y=Z.other.replace("{{count}}",String(B));if(G!==null&&G!==void 0&&G.addSuffix)if(G!==null&&G!==void 0&&G.comparison&&G.comparison>0)return"post "+Y;else return"anta\u016D "+Y;return Y};function N(H){return function(){var C=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},B=C.width?String(C.width):H.defaultWidth,G=H.formats[B]||H.formats[H.defaultWidth];return G}}var $={full:"EEEE, do 'de' MMMM y",long:"y-MMMM-dd",medium:"y-MMM-dd",short:"yyyy-MM-dd"},M={full:"Ho 'horo kaj' m:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},S={any:"{{date}} {{time}}"},R={date:N({formats:$,defaultWidth:"full"}),time:N({formats:M,defaultWidth:"full"}),dateTime:N({formats:S,defaultWidth:"any"})},L={lastWeek:"'pasinta' eeee 'je' p",yesterday:"'hiera\u016D je' p",today:"'hodia\u016D je' p",tomorrow:"'morga\u016D je' p",nextWeek:"eeee 'je' p",other:"P"},V=function H(C,B,G,Y){return L[C]};function Q(H){return function(C,B){var G=B!==null&&B!==void 0&&B.context?String(B.context):"standalone",Y;if(G==="formatting"&&H.formattingValues){var Z=H.defaultFormattingWidth||H.defaultWidth,T=B!==null&&B!==void 0&&B.width?String(B.width):Z;Y=H.formattingValues[T]||H.formattingValues[Z]}else{var E=H.defaultWidth,K=B!==null&&B!==void 0&&B.width?String(B.width):H.defaultWidth;Y=H.values[K]||H.values[E]}var O=H.argumentCallback?H.argumentCallback(C):C;return Y[O]}}var f={narrow:["aK","pK"],abbreviated:["a.K.E.","p.K.E."],wide:["anta\u016D Komuna Erao","Komuna Erao"]},j={narrow:["1","2","3","4"],abbreviated:["K1","K2","K3","K4"],wide:["1-a kvaronjaro","2-a kvaronjaro","3-a kvaronjaro","4-a kvaronjaro"]},v={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["jan","feb","mar","apr","maj","jun","jul","a\u016Dg","sep","okt","nov","dec"],wide:["januaro","februaro","marto","aprilo","majo","junio","julio","a\u016Dgusto","septembro","oktobro","novembro","decembro"]},w={narrow:["D","L","M","M","\u0134","V","S"],short:["di","lu","ma","me","\u0135a","ve","sa"],abbreviated:["dim","lun","mar","mer","\u0135a\u016D","ven","sab"],wide:["diman\u0109o","lundo","mardo","merkredo","\u0135a\u016Ddo","vendredo","sabato"]},P={narrow:{am:"a",pm:"p",midnight:"noktomezo",noon:"tagmezo",morning:"matene",afternoon:"posttagmeze",evening:"vespere",night:"nokte"},abbreviated:{am:"a.t.m.",pm:"p.t.m.",midnight:"noktomezo",noon:"tagmezo",morning:"matene",afternoon:"posttagmeze",evening:"vespere",night:"nokte"},wide:{am:"anta\u016Dtagmeze",pm:"posttagmeze",midnight:"noktomezo",noon:"tagmezo",morning:"matene",afternoon:"posttagmeze",evening:"vespere",night:"nokte"}},_=function H(C){var B=Number(C);return B+"-a"},F={ordinalNumber:_,era:Q({values:f,defaultWidth:"wide"}),quarter:Q({values:j,defaultWidth:"wide",argumentCallback:function H(C){return Number(C)-1}}),month:Q({values:v,defaultWidth:"wide"}),day:Q({values:w,defaultWidth:"wide"}),dayPeriod:Q({values:P,defaultWidth:"wide"})};function b(H){return function(C){var B=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},G=C.match(H.matchPattern);if(!G)return null;var Y=G[0],Z=C.match(H.parsePattern);if(!Z)return null;var T=H.valueCallback?H.valueCallback(Z[0]):Z[0];T=B.valueCallback?B.valueCallback(T):T;var E=C.slice(Y.length);return{value:T,rest:E}}}function q(H){return function(C){var B=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},G=B.width,Y=G&&H.matchPatterns[G]||H.matchPatterns[H.defaultMatchWidth],Z=C.match(Y);if(!Z)return null;var T=Z[0],E=G&&H.parsePatterns[G]||H.parsePatterns[H.defaultParseWidth],K=Array.isArray(E)?m(E,function(W){return W.test(T)}):k(E,function(W){return W.test(T)}),O;O=H.valueCallback?H.valueCallback(K):K,O=B.valueCallback?B.valueCallback(O):O;var e=C.slice(T.length);return{value:O,rest:e}}}var k=function H(C,B){for(var G in C)if(Object.prototype.hasOwnProperty.call(C,G)&&B(C[G]))return G;return},m=function H(C,B){for(var G=0;G<C.length;G++)if(B(C[G]))return G;return},h=/^(\d+)(-?a)?/i,c=/\d+/i,y={narrow:/^([ap]k)/i,abbreviated:/^([ap]\.?\s?k\.?\s?e\.?)/i,wide:/^((antaǔ |post )?komuna erao)/i},g={any:[/^a/i,/^[kp]/i]},u={narrow:/^[1234]/i,abbreviated:/^k[1234]/i,wide:/^[1234](-?a)? kvaronjaro/i},p={any:[/1/i,/2/i,/3/i,/4/i]},d={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|maj|jun|jul|a(ŭ|ux|uh|u)g|sep|okt|nov|dec)/i,wide:/^(januaro|februaro|marto|aprilo|majo|junio|julio|a(ŭ|ux|uh|u)gusto|septembro|oktobro|novembro|decembro)/i},l={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^maj/i,/^jun/i,/^jul/i,/^a(u|ŭ)/i,/^s/i,/^o/i,/^n/i,/^d/i]},i={narrow:/^[dlmĵjvs]/i,short:/^(di|lu|ma|me|(ĵ|jx|jh|j)a|ve|sa)/i,abbreviated:/^(dim|lun|mar|mer|(ĵ|jx|jh|j)a(ŭ|ux|uh|u)|ven|sab)/i,wide:/^(diman(ĉ|cx|ch|c)o|lundo|mardo|merkredo|(ĵ|jx|jh|j)a(ŭ|ux|uh|u)do|vendredo|sabato)/i},n={narrow:[/^d/i,/^l/i,/^m/i,/^m/i,/^(j|ĵ)/i,/^v/i,/^s/i],any:[/^d/i,/^l/i,/^ma/i,/^me/i,/^(j|ĵ)/i,/^v/i,/^s/i]},s={narrow:/^([ap]|(posttagmez|noktomez|tagmez|maten|vesper|nokt)[eo])/i,abbreviated:/^([ap][.\s]?t[.\s]?m[.\s]?|(posttagmez|noktomez|tagmez|maten|vesper|nokt)[eo])/i,wide:/^(anta(ŭ|ux)tagmez|posttagmez|noktomez|tagmez|maten|vesper|nokt)[eo]/i},o={any:{am:/^a/i,pm:/^p/i,midnight:/^noktom/i,noon:/^t/i,morning:/^m/i,afternoon:/^posttagmeze/i,evening:/^v/i,night:/^n/i}},r={ordinalNumber:b({matchPattern:h,parsePattern:c,valueCallback:function H(C){return parseInt(C,10)}}),era:q({matchPatterns:y,defaultMatchWidth:"wide",parsePatterns:g,defaultParseWidth:"any"}),quarter:q({matchPatterns:u,defaultMatchWidth:"wide",parsePatterns:p,defaultParseWidth:"any",valueCallback:function H(C){return C+1}}),month:q({matchPatterns:d,defaultMatchWidth:"wide",parsePatterns:l,defaultParseWidth:"any"}),day:q({matchPatterns:i,defaultMatchWidth:"wide",parsePatterns:n,defaultParseWidth:"any"}),dayPeriod:q({matchPatterns:s,defaultMatchWidth:"wide",parsePatterns:o,defaultParseWidth:"any"})},a={code:"eo",formatDistance:D,formatLong:R,formatRelative:V,localize:F,match:r,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=x(x({},window.dateFns),{},{locale:x(x({},(U=window.dateFns)===null||U===void 0?void 0:U.locale),{},{eo:a})})})();

//# debugId=A7D0C6271731C14664756e2164756e21
