import { useLanguage } from '@/hooks/use-language';
import { But<PERSON> } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Globe, ChevronDown } from 'lucide-react';
import { cn, rtlSpace } from '@/lib/utils';

export function LanguageSwitcher() {
  const { language, setLanguage, t, isRTL } = useLanguage();

  return (
    <DropdownMenu modal={false}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className={cn(
            'flex items-center border-beige hover:border-gold transition-colors duration-300',
            rtlSpace(isRTL, 'space-x-2'),
          )}>
          <Globe className="h-4 w-4 text-medium-gray" />
          <span className="text-sm font-medium">{language === 'en' ? 'EN' : 'العربية'}</span>
          <ChevronDown className="h-3 w-3 text-medium-gray" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align={isRTL ? 'start' : 'end'} className="w-32" onCloseAutoFocus={e => e.preventDefault()}>
        <DropdownMenuItem
          onClick={() => setLanguage('en')}
          className="cursor-pointer hover:bg-beige transition-colors duration-200">
          English
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => setLanguage('ar')}
          className="cursor-pointer hover:bg-beige transition-colors duration-200">
          العربية
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
