var S=function(Y,J){var I=Object.keys(Y);if(Object.getOwnPropertySymbols){var T=Object.getOwnPropertySymbols(Y);J&&(T=T.filter(function(V){return Object.getOwnPropertyDescriptor(Y,V).enumerable})),I.push.apply(I,T)}return I},M=function(Y){for(var J=1;J<arguments.length;J++){var I=arguments[J]!=null?arguments[J]:{};J%2?S(Object(I),!0).forEach(function(T){T0(Y,T,I[T])}):Object.getOwnPropertyDescriptors?Object.defineProperties(Y,Object.getOwnPropertyDescriptors(I)):S(Object(I)).forEach(function(T){Object.defineProperty(Y,T,Object.getOwnPropertyDescriptor(I,T))})}return Y},T0=function(Y,J,I){if(J=A0(J),J in Y)Object.defineProperty(Y,J,{value:I,enumerable:!0,configurable:!0,writable:!0});else Y[J]=I;return Y},A0=function(Y){var J=E0(Y,"string");return K(J)=="symbol"?J:String(J)},E0=function(Y,J){if(K(Y)!="object"||!Y)return Y;var I=Y[Symbol.toPrimitive];if(I!==void 0){var T=I.call(Y,J||"default");if(K(T)!="object")return T;throw new TypeError("@@toPrimitive must return a primitive value.")}return(J==="string"?String:Number)(Y)},K=function(Y){return K=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(J){return typeof J}:function(J){return J&&typeof Symbol=="function"&&J.constructor===Symbol&&J!==Symbol.prototype?"symbol":typeof J},K(Y)};(function(Y){var J=Object.defineProperty,I=function B(U,G){for(var H in G)J(U,H,{get:G[H],enumerable:!0,configurable:!0,set:function X(Z){return G[H]=function(){return Z}}})},T={lessThanXSeconds:{one:"meno di un secondo",other:"meno di {{count}} secondi"},xSeconds:{one:"un secondo",other:"{{count}} secondi"},halfAMinute:"alcuni secondi",lessThanXMinutes:{one:"meno di un minuto",other:"meno di {{count}} minuti"},xMinutes:{one:"un minuto",other:"{{count}} minuti"},aboutXHours:{one:"circa un'ora",other:"circa {{count}} ore"},xHours:{one:"un'ora",other:"{{count}} ore"},xDays:{one:"un giorno",other:"{{count}} giorni"},aboutXWeeks:{one:"circa una settimana",other:"circa {{count}} settimane"},xWeeks:{one:"una settimana",other:"{{count}} settimane"},aboutXMonths:{one:"circa un mese",other:"circa {{count}} mesi"},xMonths:{one:"un mese",other:"{{count}} mesi"},aboutXYears:{one:"circa un anno",other:"circa {{count}} anni"},xYears:{one:"un anno",other:"{{count}} anni"},overXYears:{one:"pi\xF9 di un anno",other:"pi\xF9 di {{count}} anni"},almostXYears:{one:"quasi un anno",other:"quasi {{count}} anni"}},V=function B(U,G,H){var X,Z=T[U];if(typeof Z==="string")X=Z;else if(G===1)X=Z.one;else X=Z.other.replace("{{count}}",G.toString());if(H!==null&&H!==void 0&&H.addSuffix)if(H.comparison&&H.comparison>0)return"tra "+X;else return X+" fa";return X};function $(B){var U=Object.prototype.toString.call(B);if(B instanceof Date||K(B)==="object"&&U==="[object Date]")return new B.constructor(+B);else if(typeof B==="number"||U==="[object Number]"||typeof B==="string"||U==="[object String]")return new Date(B);else return new Date(NaN)}function P(){return D}function Q0(B){D=B}var D={};function j(B,U){var G,H,X,Z,C,A,Q=P(),E=(G=(H=(X=(Z=U===null||U===void 0?void 0:U.weekStartsOn)!==null&&Z!==void 0?Z:U===null||U===void 0||(C=U.locale)===null||C===void 0||(C=C.options)===null||C===void 0?void 0:C.weekStartsOn)!==null&&X!==void 0?X:Q.weekStartsOn)!==null&&H!==void 0?H:(A=Q.locale)===null||A===void 0||(A=A.options)===null||A===void 0?void 0:A.weekStartsOn)!==null&&G!==void 0?G:0,q=$(B),z=q.getDay(),I0=(z<E?7:0)+z-E;return q.setDate(q.getDate()-I0),q.setHours(0,0,0,0),q}function W(B,U,G){var H=j(B,G),X=j(U,G);return+H===+X}var O=function B(U){switch(U){case 0:return"'domenica scorsa alle' p";default:return"'"+L[U]+" scorso alle' p"}},v=function B(U){return"'"+L[U]+" alle' p"},F=function B(U){switch(U){case 0:return"'domenica prossima alle' p";default:return"'"+L[U]+" prossimo alle' p"}},L=["domenica","luned\xEC","marted\xEC","mercoled\xEC","gioved\xEC","venerd\xEC","sabato"],w={lastWeek:function B(U,G,H){var X=U.getDay();if(W(U,G,H))return v(X);else return O(X)},yesterday:"'ieri alle' p",today:"'oggi alle' p",tomorrow:"'domani alle' p",nextWeek:function B(U,G,H){var X=U.getDay();if(W(U,G,H))return v(X);else return F(X)},other:"P"},b=function B(U,G,H,X){var Z=w[U];if(typeof Z==="function")return Z(G,H,X);return Z};function N(B){return function(U,G){var H=G!==null&&G!==void 0&&G.context?String(G.context):"standalone",X;if(H==="formatting"&&B.formattingValues){var Z=B.defaultFormattingWidth||B.defaultWidth,C=G!==null&&G!==void 0&&G.width?String(G.width):Z;X=B.formattingValues[C]||B.formattingValues[Z]}else{var A=B.defaultWidth,Q=G!==null&&G!==void 0&&G.width?String(G.width):B.defaultWidth;X=B.values[Q]||B.values[A]}var E=B.argumentCallback?B.argumentCallback(U):U;return X[E]}}var h={narrow:["aC","dC"],abbreviated:["a.C.","d.C."],wide:["avanti Cristo","dopo Cristo"]},c={narrow:["1","2","3","4"],abbreviated:["T1","T2","T3","T4"],wide:["1\xBA trimestre","2\xBA trimestre","3\xBA trimestre","4\xBA trimestre"]},f={narrow:["G","F","M","A","M","G","L","A","S","O","N","D"],abbreviated:["gen","feb","mar","apr","mag","giu","lug","ago","set","ott","nov","dic"],wide:["gennaio","febbraio","marzo","aprile","maggio","giugno","luglio","agosto","settembre","ottobre","novembre","dicembre"]},k={narrow:["D","L","M","M","G","V","S"],short:["dom","lun","mar","mer","gio","ven","sab"],abbreviated:["dom","lun","mar","mer","gio","ven","sab"],wide:["domenica","luned\xEC","marted\xEC","mercoled\xEC","gioved\xEC","venerd\xEC","sabato"]},m={narrow:{am:"m.",pm:"p.",midnight:"mezzanotte",noon:"mezzogiorno",morning:"mattina",afternoon:"pomeriggio",evening:"sera",night:"notte"},abbreviated:{am:"AM",pm:"PM",midnight:"mezzanotte",noon:"mezzogiorno",morning:"mattina",afternoon:"pomeriggio",evening:"sera",night:"notte"},wide:{am:"AM",pm:"PM",midnight:"mezzanotte",noon:"mezzogiorno",morning:"mattina",afternoon:"pomeriggio",evening:"sera",night:"notte"}},_={narrow:{am:"m.",pm:"p.",midnight:"mezzanotte",noon:"mezzogiorno",morning:"di mattina",afternoon:"del pomeriggio",evening:"di sera",night:"di notte"},abbreviated:{am:"AM",pm:"PM",midnight:"mezzanotte",noon:"mezzogiorno",morning:"di mattina",afternoon:"del pomeriggio",evening:"di sera",night:"di notte"},wide:{am:"AM",pm:"PM",midnight:"mezzanotte",noon:"mezzogiorno",morning:"di mattina",afternoon:"del pomeriggio",evening:"di sera",night:"di notte"}},y=function B(U,G){var H=Number(U);return String(H)},g={ordinalNumber:y,era:N({values:h,defaultWidth:"wide"}),quarter:N({values:c,defaultWidth:"wide",argumentCallback:function B(U){return U-1}}),month:N({values:f,defaultWidth:"wide"}),day:N({values:k,defaultWidth:"wide"}),dayPeriod:N({values:m,defaultWidth:"wide",formattingValues:_,defaultFormattingWidth:"wide"})};function x(B){return function(U){var G=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},H=G.width,X=H&&B.matchPatterns[H]||B.matchPatterns[B.defaultMatchWidth],Z=U.match(X);if(!Z)return null;var C=Z[0],A=H&&B.parsePatterns[H]||B.parsePatterns[B.defaultParseWidth],Q=Array.isArray(A)?p(A,function(z){return z.test(C)}):u(A,function(z){return z.test(C)}),E;E=B.valueCallback?B.valueCallback(Q):Q,E=G.valueCallback?G.valueCallback(E):E;var q=U.slice(C.length);return{value:E,rest:q}}}var u=function B(U,G){for(var H in U)if(Object.prototype.hasOwnProperty.call(U,H)&&G(U[H]))return H;return},p=function B(U,G){for(var H=0;H<U.length;H++)if(G(U[H]))return H;return};function l(B){return function(U){var G=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},H=U.match(B.matchPattern);if(!H)return null;var X=H[0],Z=U.match(B.parsePattern);if(!Z)return null;var C=B.valueCallback?B.valueCallback(Z[0]):Z[0];C=G.valueCallback?G.valueCallback(C):C;var A=U.slice(X.length);return{value:C,rest:A}}}var d=/^(\d+)(º)?/i,i=/\d+/i,n={narrow:/^(aC|dC)/i,abbreviated:/^(a\.?\s?C\.?|a\.?\s?e\.?\s?v\.?|d\.?\s?C\.?|e\.?\s?v\.?)/i,wide:/^(avanti Cristo|avanti Era Volgare|dopo Cristo|Era Volgare)/i},s={any:[/^a/i,/^(d|e)/i]},r={narrow:/^[1234]/i,abbreviated:/^t[1234]/i,wide:/^[1234](º)? trimestre/i},o={any:[/1/i,/2/i,/3/i,/4/i]},a={narrow:/^[gfmalsond]/i,abbreviated:/^(gen|feb|mar|apr|mag|giu|lug|ago|set|ott|nov|dic)/i,wide:/^(gennaio|febbraio|marzo|aprile|maggio|giugno|luglio|agosto|settembre|ottobre|novembre|dicembre)/i},e={narrow:[/^g/i,/^f/i,/^m/i,/^a/i,/^m/i,/^g/i,/^l/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ge/i,/^f/i,/^mar/i,/^ap/i,/^mag/i,/^gi/i,/^l/i,/^ag/i,/^s/i,/^o/i,/^n/i,/^d/i]},t={narrow:/^[dlmgvs]/i,short:/^(do|lu|ma|me|gi|ve|sa)/i,abbreviated:/^(dom|lun|mar|mer|gio|ven|sab)/i,wide:/^(domenica|luned[i|ì]|marted[i|ì]|mercoled[i|ì]|gioved[i|ì]|venerd[i|ì]|sabato)/i},U0={narrow:[/^d/i,/^l/i,/^m/i,/^m/i,/^g/i,/^v/i,/^s/i],any:[/^d/i,/^l/i,/^ma/i,/^me/i,/^g/i,/^v/i,/^s/i]},B0={narrow:/^(a|m\.|p|mezzanotte|mezzogiorno|(di|del) (mattina|pomeriggio|sera|notte))/i,any:/^([ap]\.?\s?m\.?|mezzanotte|mezzogiorno|(di|del) (mattina|pomeriggio|sera|notte))/i},G0={any:{am:/^a/i,pm:/^p/i,midnight:/^mezza/i,noon:/^mezzo/i,morning:/mattina/i,afternoon:/pomeriggio/i,evening:/sera/i,night:/notte/i}},H0={ordinalNumber:l({matchPattern:d,parsePattern:i,valueCallback:function B(U){return parseInt(U,10)}}),era:x({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),quarter:x({matchPatterns:r,defaultMatchWidth:"wide",parsePatterns:o,defaultParseWidth:"any",valueCallback:function B(U){return U+1}}),month:x({matchPatterns:a,defaultMatchWidth:"wide",parsePatterns:e,defaultParseWidth:"any"}),day:x({matchPatterns:t,defaultMatchWidth:"wide",parsePatterns:U0,defaultParseWidth:"any"}),dayPeriod:x({matchPatterns:B0,defaultMatchWidth:"any",parsePatterns:G0,defaultParseWidth:"any"})};function R(B){return function(){var U=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},G=U.width?String(U.width):B.defaultWidth,H=B.formats[G]||B.formats[B.defaultWidth];return H}}var J0={full:"EEEE d MMMM y",long:"d MMMM y",medium:"d MMM y",short:"dd.MM.y"},X0={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},Y0={full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},Z0={date:R({formats:J0,defaultWidth:"full"}),time:R({formats:X0,defaultWidth:"full"}),dateTime:R({formats:Y0,defaultWidth:"full"})},C0={code:"it-CH",formatDistance:V,formatLong:Z0,formatRelative:b,localize:g,match:H0,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=M(M({},window.dateFns),{},{locale:M(M({},(Y=window.dateFns)===null||Y===void 0?void 0:Y.locale),{},{itCH:C0})})})();

//# debugId=7BEC2123BFFCD80964756e2164756e21
