{"version": 3, "file": "index.cjs.js", "sources": ["../src/utils/isCheckBoxInput.ts", "../src/utils/isDateObject.ts", "../src/utils/isNullOrUndefined.ts", "../src/utils/isObject.ts", "../src/logic/getEventValue.ts", "../src/logic/isNameInFieldArray.ts", "../src/logic/getNodeParentName.ts", "../src/utils/isWeb.ts", "../src/utils/cloneObject.ts", "../src/utils/isPlainObject.ts", "../src/utils/isKey.ts", "../src/utils/isUndefined.ts", "../src/utils/compact.ts", "../src/utils/stringToPath.ts", "../src/utils/get.ts", "../src/utils/isBoolean.ts", "../src/utils/set.ts", "../src/constants.ts", "../src/useFormContext.tsx", "../src/logic/getProxyFormState.ts", "../src/useIsomorphicLayoutEffect.ts", "../src/useFormState.ts", "../src/utils/isString.ts", "../src/logic/generateWatchOutput.ts", "../src/utils/isPrimitive.ts", "../src/utils/deepEqual.ts", "../src/useWatch.ts", "../src/useController.ts", "../src/controller.tsx", "../src/utils/flatten.ts", "../src/form.tsx", "../src/logic/appendErrors.ts", "../src/utils/convertToArrayPayload.ts", "../src/utils/createSubject.ts", "../src/utils/isEmptyObject.ts", "../src/utils/isFileInput.ts", "../src/utils/isFunction.ts", "../src/utils/isHTMLElement.ts", "../src/utils/isMultipleSelect.ts", "../src/utils/isRadioInput.ts", "../src/utils/live.ts", "../src/utils/unset.ts", "../src/utils/objectHasFunction.ts", "../src/logic/getDirtyFields.ts", "../src/logic/getCheckboxValue.ts", "../src/logic/getFieldValueAs.ts", "../src/logic/getRadioValue.ts", "../src/logic/getFieldValue.ts", "../src/logic/getResolverOptions.ts", "../src/utils/isRegex.ts", "../src/logic/getRuleValue.ts", "../src/logic/getValidationModes.ts", "../src/logic/hasPromiseValidation.ts", "../src/logic/isWatched.ts", "../src/logic/iterateFieldsByAction.ts", "../src/logic/schemaErrorLookup.ts", "../src/logic/shouldRenderFormState.ts", "../src/logic/updateFieldArrayRootError.ts", "../src/utils/isMessage.ts", "../src/logic/getValidateError.ts", "../src/logic/getValueAndMessage.ts", "../src/logic/validateField.ts", "../src/logic/createFormControl.ts", "../src/logic/hasValidation.ts", "../src/logic/skipValidation.ts", "../src/logic/shouldSubscribeByName.ts", "../src/utils/isRadioOrCheckbox.ts", "../src/logic/unsetEmptyArray.ts", "../src/logic/generateId.ts", "../src/logic/getFocusFieldName.ts", "../src/utils/append.ts", "../src/utils/fillEmptyArray.ts", "../src/utils/insert.ts", "../src/utils/move.ts", "../src/utils/prepend.ts", "../src/utils/remove.ts", "../src/utils/swap.ts", "../src/utils/update.ts", "../src/useFieldArray.ts", "../src/useForm.ts"], "sourcesContent": ["import type { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'checkbox';\n", "export default (value: unknown): value is Date => value instanceof Date;\n", "export default (value: unknown): value is null | undefined => value == null;\n", "import isDateObject from './isDateObject';\nimport isNullOrUndefined from './isNullOrUndefined';\n\nexport const isObjectType = (value: unknown): value is object =>\n  typeof value === 'object';\n\nexport default <T extends object>(value: unknown): value is T =>\n  !isNullOrUndefined(value) &&\n  !Array.isArray(value) &&\n  isObjectType(value) &&\n  !isDateObject(value);\n", "import isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isObject from '../utils/isObject';\n\ntype Event = { target: any };\n\nexport default (event: unknown) =>\n  isObject(event) && (event as Event).target\n    ? isCheckBoxInput((event as Event).target)\n      ? (event as Event).target.checked\n      : (event as Event).target.value\n    : event;\n", "import type { InternalFieldName } from '../types';\n\nimport getNodeParentName from './getNodeParentName';\n\nexport default (names: Set<InternalFieldName>, name: InternalFieldName) =>\n  names.has(getNodeParentName(name));\n", "export default (name: string) =>\n  name.substring(0, name.search(/\\.\\d+(\\.|$)/)) || name;\n", "export default typeof window !== 'undefined' &&\n  typeof window.HTMLElement !== 'undefined' &&\n  typeof document !== 'undefined';\n", "import isObject from './isObject';\nimport isPlainObject from './isPlainObject';\nimport isWeb from './isWeb';\n\nexport default function cloneObject<T>(data: T): T {\n  let copy: any;\n  const isArray = Array.isArray(data);\n  const isFileListInstance =\n    typeof FileList !== 'undefined' ? data instanceof FileList : false;\n\n  if (data instanceof Date) {\n    copy = new Date(data);\n  } else if (\n    !(isWeb && (data instanceof Blob || isFileListInstance)) &&\n    (isArray || isObject(data))\n  ) {\n    copy = isArray ? [] : Object.create(Object.getPrototypeOf(data));\n\n    if (!isArray && !isPlainObject(data)) {\n      copy = data;\n    } else {\n      for (const key in data) {\n        if (data.hasOwnProperty(key)) {\n          copy[key] = cloneObject(data[key]);\n        }\n      }\n    }\n  } else {\n    return data;\n  }\n\n  return copy;\n}\n", "import isObject from './isObject';\n\nexport default (tempObject: object) => {\n  const prototypeCopy =\n    tempObject.constructor && tempObject.constructor.prototype;\n\n  return (\n    isObject(prototypeCopy) && prototypeCopy.hasOwnProperty('isPrototypeOf')\n  );\n};\n", "export default (value: string) => /^\\w*$/.test(value);\n", "export default (val: unknown): val is undefined => val === undefined;\n", "export default <TValue>(value: TValue[]) =>\n  Array.isArray(value) ? value.filter(Boolean) : [];\n", "import compact from './compact';\n\nexport default (input: string): string[] =>\n  compact(input.replace(/[\"|']|\\]/g, '').split(/\\.|\\[/));\n", "import isKey from './isKey';\nimport isNullOrUndefined from './isNullOrUndefined';\nimport isObject from './isObject';\nimport isUndefined from './isUndefined';\nimport stringToPath from './stringToPath';\n\nexport default <T>(\n  object: T,\n  path?: string | null,\n  defaultValue?: unknown,\n): any => {\n  if (!path || !isObject(object)) {\n    return defaultValue;\n  }\n\n  const result = (isKey(path) ? [path] : stringToPath(path)).reduce(\n    (result, key) =>\n      isNullOrUndefined(result) ? result : result[key as keyof {}],\n    object,\n  );\n\n  return isUndefined(result) || result === object\n    ? isUndefined(object[path as keyof T])\n      ? defaultValue\n      : object[path as keyof T]\n    : result;\n};\n", "export default (value: unknown): value is boolean => typeof value === 'boolean';\n", "import type { FieldPath, FieldValues } from '../types';\n\nimport isKey from './isKey';\nimport isObject from './isObject';\nimport stringToPath from './stringToPath';\n\nexport default (\n  object: FieldValues,\n  path: FieldPath<FieldValues>,\n  value?: unknown,\n) => {\n  let index = -1;\n  const tempPath = isKey(path) ? [path] : stringToPath(path);\n  const length = tempPath.length;\n  const lastIndex = length - 1;\n\n  while (++index < length) {\n    const key = tempPath[index];\n    let newValue = value;\n\n    if (index !== lastIndex) {\n      const objValue = object[key];\n      newValue =\n        isObject(objValue) || Array.isArray(objValue)\n          ? objValue\n          : !isNaN(+tempPath[index + 1])\n            ? []\n            : {};\n    }\n\n    if (key === '__proto__' || key === 'constructor' || key === 'prototype') {\n      return;\n    }\n\n    object[key] = newValue;\n    object = object[key];\n  }\n};\n", "export const EVENTS = {\n  BLUR: 'blur',\n  FOCUS_OUT: 'focusout',\n  CHANGE: 'change',\n} as const;\n\nexport const VALIDATION_MODE = {\n  onBlur: 'onBlur',\n  onChange: 'onChange',\n  onSubmit: 'onSubmit',\n  onTouched: 'onTouched',\n  all: 'all',\n} as const;\n\nexport const INPUT_VALIDATION_RULES = {\n  max: 'max',\n  min: 'min',\n  maxLength: 'maxLength',\n  minLength: 'minLength',\n  pattern: 'pattern',\n  required: 'required',\n  validate: 'validate',\n} as const;\n", "import React from 'react';\n\nimport type { FieldValues, FormProviderProps, UseFormReturn } from './types';\n\nconst HookFormContext = React.createContext<UseFormReturn | null>(null);\nHookFormContext.displayName = 'HookFormContext';\n\n/**\n * This custom hook allows you to access the form context. useFormContext is intended to be used in deeply nested structures, where it would become inconvenient to pass the context as a prop. To be used with {@link FormProvider}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @returns return all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nexport const useFormContext = <\n  TFieldValues extends FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(): UseFormReturn<TFieldValues, TContext, TTransformedValues> =>\n  React.useContext(HookFormContext) as UseFormReturn<\n    TFieldValues,\n    TContext,\n    TTransformedValues\n  >;\n\n/**\n * A provider component that propagates the `useForm` methods to all children components via [React Context](https://reactjs.org/docs/context.html) API. To be used with {@link useFormContext}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @param props - all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nexport const FormProvider = <\n  TFieldValues extends FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(\n  props: FormProviderProps<TFieldValues, TContext, TTransformedValues>,\n) => {\n  const { children, ...data } = props;\n  return (\n    <HookFormContext.Provider value={data as unknown as UseFormReturn}>\n      {children}\n    </HookFormContext.Provider>\n  );\n};\n", "import { VALIDATION_MODE } from '../constants';\nimport type { Control, FieldValues, FormState, ReadFormState } from '../types';\n\nexport default <\n  TFieldValues extends FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(\n  formState: FormState<TFieldValues>,\n  control: Control<TFieldValues, TContext, TTransformedValues>,\n  localProxyFormState?: ReadFormState,\n  isRoot = true,\n) => {\n  const result = {\n    defaultValues: control._defaultValues,\n  } as typeof formState;\n\n  for (const key in formState) {\n    Object.defineProperty(result, key, {\n      get: () => {\n        const _key = key as keyof FormState<TFieldValues> & keyof ReadFormState;\n\n        if (control._proxyFormState[_key] !== VALIDATION_MODE.all) {\n          control._proxyFormState[_key] = !isRoot || VALIDATION_MODE.all;\n        }\n\n        localProxyFormState && (localProxyFormState[_key] = true);\n        return formState[_key];\n      },\n    });\n  }\n\n  return result;\n};\n", "import React from 'react';\n\nexport const useIsomorphicLayoutEffect =\n  typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;\n", "import React from 'react';\n\nimport getProxyFormState from './logic/getProxyFormState';\nimport type {\n  FieldValues,\n  UseFormStateProps,\n  UseFormStateReturn,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect';\n\n/**\n * This custom hook allows you to subscribe to each form state, and isolate the re-render at the custom hook level. It has its scope in terms of form state subscription, so it would not affect other useFormState and useForm. Using this hook can reduce the re-render impact on large and complex form application.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformstate) • [Demo](https://codesandbox.io/s/useformstate-75xly)\n *\n * @param props - include options on specify fields to subscribe. {@link UseFormStateReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, control } = useForm({\n *     defaultValues: {\n *     firstName: \"firstName\"\n *   }});\n *   const { dirtyFields } = useFormState({\n *     control\n *   });\n *   const onSubmit = (data) => console.log(data);\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input {...register(\"firstName\")} placeholder=\"First Name\" />\n *       {dirtyFields.firstName && <p>Field is dirty.</p>}\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nexport function useFormState<\n  TFieldValues extends FieldValues = FieldValues,\n  TTransformedValues = TFieldValues,\n>(\n  props?: UseFormStateProps<TFieldValues, TTransformedValues>,\n): UseFormStateReturn<TFieldValues> {\n  const methods = useFormContext<TFieldValues, any, TTransformedValues>();\n  const { control = methods.control, disabled, name, exact } = props || {};\n  const [formState, updateFormState] = React.useState(control._formState);\n  const _localProxyFormState = React.useRef({\n    isDirty: false,\n    isLoading: false,\n    dirtyFields: false,\n    touchedFields: false,\n    validatingFields: false,\n    isValidating: false,\n    isValid: false,\n    errors: false,\n  });\n\n  useIsomorphicLayoutEffect(\n    () =>\n      control._subscribe({\n        name,\n        formState: _localProxyFormState.current,\n        exact,\n        callback: (formState) => {\n          !disabled &&\n            updateFormState({\n              ...control._formState,\n              ...formState,\n            });\n        },\n      }),\n    [name, disabled, exact],\n  );\n\n  React.useEffect(() => {\n    _localProxyFormState.current.isValid && control._setValid(true);\n  }, [control]);\n\n  return React.useMemo(\n    () =>\n      getProxyFormState(\n        formState,\n        control,\n        _localProxyFormState.current,\n        false,\n      ),\n    [formState, control],\n  );\n}\n", "export default (value: unknown): value is string => typeof value === 'string';\n", "import type { DeepPartial, FieldValues, Names } from '../types';\nimport get from '../utils/get';\nimport isString from '../utils/isString';\n\nexport default <T>(\n  names: string | string[] | undefined,\n  _names: Names,\n  formValues?: FieldValues,\n  isGlobal?: boolean,\n  defaultValue?: DeepPartial<T> | unknown,\n) => {\n  if (isString(names)) {\n    isGlobal && _names.watch.add(names);\n    return get(formValues, names, defaultValue);\n  }\n\n  if (Array.isArray(names)) {\n    return names.map(\n      (fieldName) => (\n        isGlobal && _names.watch.add(fieldName),\n        get(formValues, fieldName)\n      ),\n    );\n  }\n\n  isGlobal && (_names.watchAll = true);\n\n  return formValues;\n};\n", "import type { Primitive } from '../types';\n\nimport isNullOrUndefined from './isNullOrUndefined';\nimport { isObjectType } from './isObject';\n\nexport default (value: unknown): value is Primitive =>\n  isNullOrUndefined(value) || !isObjectType(value);\n", "import isObject from '../utils/isObject';\n\nimport isDateObject from './isDateObject';\nimport isPrimitive from './isPrimitive';\n\nexport default function deepEqual(\n  object1: any,\n  object2: any,\n  _internal_visited = new WeakSet(),\n) {\n  if (isPrimitive(object1) || isPrimitive(object2)) {\n    return object1 === object2;\n  }\n\n  if (isDateObject(object1) && isDateObject(object2)) {\n    return object1.getTime() === object2.getTime();\n  }\n\n  const keys1 = Object.keys(object1);\n  const keys2 = Object.keys(object2);\n\n  if (keys1.length !== keys2.length) {\n    return false;\n  }\n\n  if (_internal_visited.has(object1) || _internal_visited.has(object2)) {\n    return true;\n  }\n  _internal_visited.add(object1);\n  _internal_visited.add(object2);\n\n  for (const key of keys1) {\n    const val1 = object1[key];\n\n    if (!keys2.includes(key)) {\n      return false;\n    }\n\n    if (key !== 'ref') {\n      const val2 = object2[key];\n\n      if (\n        (isDateObject(val1) && isDateObject(val2)) ||\n        (isObject(val1) && isObject(val2)) ||\n        (Array.isArray(val1) && Array.isArray(val2))\n          ? !deepEqual(val1, val2, _internal_visited)\n          : val1 !== val2\n      ) {\n        return false;\n      }\n    }\n  }\n\n  return true;\n}\n", "import React from 'react';\n\nimport generateWatchOutput from './logic/generateWatchOutput';\nimport deepEqual from './utils/deepEqual';\nimport type {\n  Control,\n  DeepPartialSkipArrayKey,\n  FieldPath,\n  FieldPathValue,\n  FieldPathValues,\n  FieldValues,\n  InternalFieldName,\n  UseWatchProps,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect';\n\n/**\n * Subscribe to the entire form values change and re-render at the hook level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   defaultValue: {\n *     name: \"data\"\n *   },\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TTransformedValues = TFieldValues,\n>(props: {\n  name?: undefined;\n  defaultValue?: DeepPartialSkipArrayKey<TFieldValues>;\n  control?: Control<TFieldValues, any, TTransformedValues>;\n  disabled?: boolean;\n  exact?: boolean;\n  compute?: undefined;\n}): DeepPartialSkipArrayKey<TFieldValues>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   name: \"fieldA\",\n *   defaultValue: \"default value\",\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n  TTransformedValues = TFieldValues,\n>(props: {\n  name: TFieldName;\n  defaultValue?: FieldPathValue<TFieldValues, TFieldName>;\n  control?: Control<TFieldValues, any, TTransformedValues>;\n  disabled?: boolean;\n  exact?: boolean;\n  compute?: undefined;\n}): FieldPathValue<TFieldValues, TFieldName>;\n/**\n * Custom hook to subscribe to field change and compute function to produce state update\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   compute: (formValues) => formValues.fieldA\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TTransformedValues = TFieldValues,\n  TComputeValue = unknown,\n>(props: {\n  name?: undefined;\n  defaultValue?: DeepPartialSkipArrayKey<TFieldValues>;\n  control?: Control<TFieldValues, any, TTransformedValues>;\n  disabled?: boolean;\n  exact?: boolean;\n  compute: (formValues: TFieldValues) => TComputeValue;\n}): TComputeValue;\n/**\n * Custom hook to subscribe to field change and compute function to produce state update\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   name: \"fieldA\",\n *   defaultValue: \"default value\",\n *   exact: false,\n *   compute: (fieldValue) => fieldValue === \"data\" ? fieldValue : null,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n  TTransformedValues = TFieldValues,\n  TComputeValue = unknown,\n>(props: {\n  name: TFieldName;\n  defaultValue?: FieldPathValue<TFieldValues, TFieldName>;\n  control?: Control<TFieldValues, any, TTransformedValues>;\n  disabled?: boolean;\n  exact?: boolean;\n  compute: (\n    fieldValue: FieldPathValue<TFieldValues, TFieldName>,\n  ) => TComputeValue;\n}): TComputeValue;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   name: [\"fieldA\", \"fieldB\"],\n *   defaultValue: {\n *     fieldA: \"data\",\n *     fieldB: \"data\"\n *   },\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldNames extends\n    readonly FieldPath<TFieldValues>[] = readonly FieldPath<TFieldValues>[],\n  TTransformedValues = TFieldValues,\n>(props: {\n  name: readonly [...TFieldNames];\n  defaultValue?: DeepPartialSkipArrayKey<TFieldValues>;\n  control?: Control<TFieldValues, any, TTransformedValues>;\n  disabled?: boolean;\n  exact?: boolean;\n  compute?: undefined;\n}): FieldPathValues<TFieldValues, TFieldNames>;\n/**\n * Custom hook to subscribe to field change and compute function to produce state update\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   name: [\"fieldA\", \"fieldB\"],\n *   defaultValue: {\n *     fieldA: \"data\",\n *     fieldB: 0\n *   },\n *   compute: ([fieldAValue, fieldBValue]) => fieldB === 2 ? fieldA : null,\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldNames extends\n    readonly FieldPath<TFieldValues>[] = readonly FieldPath<TFieldValues>[],\n  TTransformedValues = TFieldValues,\n  TComputeValue = unknown,\n>(props: {\n  name: readonly [...TFieldNames];\n  defaultValue?: DeepPartialSkipArrayKey<TFieldValues>;\n  control?: Control<TFieldValues, any, TTransformedValues>;\n  disabled?: boolean;\n  exact?: boolean;\n  compute: (\n    fieldValue: FieldPathValues<TFieldValues, TFieldNames>,\n  ) => TComputeValue;\n}): TComputeValue;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * // can skip passing down the control into useWatch if the form is wrapped with the FormProvider\n * const values = useWatch()\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n>(): DeepPartialSkipArrayKey<TFieldValues>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   name: \"fieldName\"\n *   control,\n * })\n * ```\n */\nexport function useWatch<TFieldValues extends FieldValues>(\n  props?: UseWatchProps<TFieldValues>,\n) {\n  const methods = useFormContext<TFieldValues>();\n  const {\n    control = methods.control,\n    name,\n    defaultValue,\n    disabled,\n    exact,\n    compute,\n  } = props || {};\n  const _defaultValue = React.useRef(defaultValue);\n  const _compute = React.useRef(compute);\n  const _computeFormValues = React.useRef(undefined);\n\n  _compute.current = compute;\n\n  const defaultValueMemo = React.useMemo(\n    () =>\n      control._getWatch(\n        name as InternalFieldName,\n        _defaultValue.current as DeepPartialSkipArrayKey<TFieldValues>,\n      ),\n    [control, name],\n  );\n\n  const [value, updateValue] = React.useState(\n    _compute.current ? _compute.current(defaultValueMemo) : defaultValueMemo,\n  );\n\n  useIsomorphicLayoutEffect(\n    () =>\n      control._subscribe({\n        name,\n        formState: {\n          values: true,\n        },\n        exact,\n        callback: (formState) => {\n          if (!disabled) {\n            const formValues = generateWatchOutput(\n              name as InternalFieldName | InternalFieldName[],\n              control._names,\n              formState.values || control._formValues,\n              false,\n              _defaultValue.current,\n            );\n\n            if (_compute.current) {\n              const computedFormValues = _compute.current(formValues);\n\n              if (!deepEqual(computedFormValues, _computeFormValues.current)) {\n                updateValue(computedFormValues);\n                _computeFormValues.current = computedFormValues;\n              }\n            } else {\n              updateValue(formValues);\n            }\n          }\n        },\n      }),\n    [control, disabled, name, exact],\n  );\n\n  React.useEffect(() => control._removeUnmounted());\n\n  return value;\n}\n", "import React from 'react';\n\nimport getEventValue from './logic/getEventValue';\nimport isNameInFieldArray from './logic/isNameInFieldArray';\nimport cloneObject from './utils/cloneObject';\nimport get from './utils/get';\nimport isBoolean from './utils/isBoolean';\nimport isUndefined from './utils/isUndefined';\nimport set from './utils/set';\nimport { EVENTS } from './constants';\nimport type {\n  ControllerFieldState,\n  Field,\n  FieldPath,\n  FieldPathValue,\n  FieldValues,\n  InternalFieldName,\n  UseControllerProps,\n  UseControllerReturn,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useFormState } from './useFormState';\nimport { useWatch } from './useWatch';\n\n/**\n * Custom hook to work with controlled component, this function provide you with both form and field level state. Re-render is isolated at the hook level.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller) • [Demo](https://codesandbox.io/s/usecontroller-0o8px)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns field properties, field and form state. {@link UseControllerReturn}\n *\n * @example\n * ```tsx\n * function Input(props) {\n *   const { field, fieldState, formState } = useController(props);\n *   return (\n *     <div>\n *       <input {...field} placeholder={props.name} />\n *       <p>{fieldState.isTouched && \"Touched\"}</p>\n *       <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *     </div>\n *   );\n * }\n * ```\n */\nexport function useController<\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n  TTransformedValues = TFieldValues,\n>(\n  props: UseControllerProps<TFieldValues, TName, TTransformedValues>,\n): UseControllerReturn<TFieldValues, TName> {\n  const methods = useFormContext<TFieldValues, any, TTransformedValues>();\n  const {\n    name,\n    disabled,\n    control = methods.control,\n    shouldUnregister,\n    defaultValue,\n  } = props;\n  const isArrayField = isNameInFieldArray(control._names.array, name);\n\n  const defaultValueMemo = React.useMemo(\n    () =>\n      get(\n        control._formValues,\n        name,\n        get(control._defaultValues, name, defaultValue),\n      ),\n    [control, name, defaultValue],\n  );\n\n  const value = useWatch({\n    control,\n    name,\n    defaultValue: defaultValueMemo,\n    exact: true,\n  }) as FieldPathValue<TFieldValues, TName>;\n\n  const formState = useFormState({\n    control,\n    name,\n    exact: true,\n  });\n\n  const _props = React.useRef(props);\n\n  const _registerProps = React.useRef(\n    control.register(name, {\n      ...props.rules,\n      value,\n      ...(isBoolean(props.disabled) ? { disabled: props.disabled } : {}),\n    }),\n  );\n\n  _props.current = props;\n\n  const fieldState = React.useMemo(\n    () =>\n      Object.defineProperties(\n        {},\n        {\n          invalid: {\n            enumerable: true,\n            get: () => !!get(formState.errors, name),\n          },\n          isDirty: {\n            enumerable: true,\n            get: () => !!get(formState.dirtyFields, name),\n          },\n          isTouched: {\n            enumerable: true,\n            get: () => !!get(formState.touchedFields, name),\n          },\n          isValidating: {\n            enumerable: true,\n            get: () => !!get(formState.validatingFields, name),\n          },\n          error: {\n            enumerable: true,\n            get: () => get(formState.errors, name),\n          },\n        },\n      ) as ControllerFieldState,\n    [formState, name],\n  );\n\n  const onChange = React.useCallback(\n    (event: any) =>\n      _registerProps.current.onChange({\n        target: {\n          value: getEventValue(event),\n          name: name as InternalFieldName,\n        },\n        type: EVENTS.CHANGE,\n      }),\n    [name],\n  );\n\n  const onBlur = React.useCallback(\n    () =>\n      _registerProps.current.onBlur({\n        target: {\n          value: get(control._formValues, name),\n          name: name as InternalFieldName,\n        },\n        type: EVENTS.BLUR,\n      }),\n    [name, control._formValues],\n  );\n\n  const ref = React.useCallback(\n    (elm: any) => {\n      const field = get(control._fields, name);\n\n      if (field && elm) {\n        field._f.ref = {\n          focus: () => elm.focus && elm.focus(),\n          select: () => elm.select && elm.select(),\n          setCustomValidity: (message: string) =>\n            elm.setCustomValidity(message),\n          reportValidity: () => elm.reportValidity(),\n        };\n      }\n    },\n    [control._fields, name],\n  );\n\n  const field = React.useMemo(\n    () => ({\n      name,\n      value,\n      ...(isBoolean(disabled) || formState.disabled\n        ? { disabled: formState.disabled || disabled }\n        : {}),\n      onChange,\n      onBlur,\n      ref,\n    }),\n    [name, disabled, formState.disabled, onChange, onBlur, ref, value],\n  );\n\n  React.useEffect(() => {\n    const _shouldUnregisterField =\n      control._options.shouldUnregister || shouldUnregister;\n\n    control.register(name, {\n      ..._props.current.rules,\n      ...(isBoolean(_props.current.disabled)\n        ? { disabled: _props.current.disabled }\n        : {}),\n    });\n\n    const updateMounted = (name: InternalFieldName, value: boolean) => {\n      const field: Field = get(control._fields, name);\n\n      if (field && field._f) {\n        field._f.mount = value;\n      }\n    };\n\n    updateMounted(name, true);\n\n    if (_shouldUnregisterField) {\n      const value = cloneObject(get(control._options.defaultValues, name));\n      set(control._defaultValues, name, value);\n      if (isUndefined(get(control._formValues, name))) {\n        set(control._formValues, name, value);\n      }\n    }\n\n    !isArrayField && control.register(name);\n\n    return () => {\n      (\n        isArrayField\n          ? _shouldUnregisterField && !control._state.action\n          : _shouldUnregisterField\n      )\n        ? control.unregister(name)\n        : updateMounted(name, false);\n    };\n  }, [name, control, isArrayField, shouldUnregister]);\n\n  React.useEffect(() => {\n    control._setDisabledField({\n      disabled,\n      name,\n    });\n  }, [disabled, name, control]);\n\n  return React.useMemo(\n    () => ({\n      field,\n      formState,\n      fieldState,\n    }),\n    [field, formState, fieldState],\n  );\n}\n", "import type { ControllerProps, FieldPath, FieldValues } from './types';\nimport { useController } from './useController';\n\n/**\n * Component based on `useController` hook to work with controlled component.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller/controller) • [Demo](https://codesandbox.io/s/react-hook-form-v6-controller-ts-jwyzw) • [Video](https://www.youtube.com/watch?v=N2UNk_UCVyA)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns provide field handler functions, field and form state.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control } = useForm<FormValues>({\n *     defaultValues: {\n *       test: \"\"\n *     }\n *   });\n *\n *   return (\n *     <form>\n *       <Controller\n *         control={control}\n *         name=\"test\"\n *         render={({ field: { onChange, onBlur, value, ref }, formState, fieldState }) => (\n *           <>\n *             <input\n *               onChange={onChange} // send value to hook form\n *               onBlur={onBlur} // notify when input is touched\n *               value={value} // return updated value\n *               ref={ref} // set ref for focus management\n *             />\n *             <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *             <p>{fieldState.isTouched ? \"touched\" : \"\"}</p>\n *           </>\n *         )}\n *       />\n *     </form>\n *   );\n * }\n * ```\n */\nconst Controller = <\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n  TTransformedValues = TFieldValues,\n>(\n  props: ControllerProps<TFieldValues, TName, TTransformedValues>,\n) =>\n  props.render(useController<TFieldValues, TName, TTransformedValues>(props));\n\nexport { Controller };\n", "import type { FieldValues } from '../types';\n\nimport { isObjectType } from './isObject';\n\nexport const flatten = (obj: FieldValues) => {\n  const output: FieldValues = {};\n\n  for (const key of Object.keys(obj)) {\n    if (isObjectType(obj[key]) && obj[key] !== null) {\n      const nested = flatten(obj[key]);\n\n      for (const nestedKey of Object.keys(nested)) {\n        output[`${key}.${nestedKey}`] = nested[nestedKey];\n      }\n    } else {\n      output[key] = obj[key];\n    }\n  }\n\n  return output;\n};\n", "import React from 'react';\n\nimport { flatten } from './utils/flatten';\nimport type { FieldValues, FormProps } from './types';\nimport { useFormContext } from './useFormContext';\n\nconst POST_REQUEST = 'post';\n\n/**\n * Form component to manage submission.\n *\n * @param props - to setup submission detail. {@link FormProps}\n *\n * @returns form component or headless render prop.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control, formState: { errors } } = useForm();\n *\n *   return (\n *     <Form action=\"/api\" control={control}>\n *       <input {...register(\"name\")} />\n *       <p>{errors?.root?.server && 'Server error'}</p>\n *       <button>Submit</button>\n *     </Form>\n *   );\n * }\n * ```\n */\nfunction Form<\n  TFieldValues extends FieldValues,\n  TTransformedValues = TFieldValues,\n>(props: FormProps<TFieldValues, TTransformedValues>) {\n  const methods = useFormContext<TFieldValues, any, TTransformedValues>();\n  const [mounted, setMounted] = React.useState(false);\n  const {\n    control = methods.control,\n    onSubmit,\n    children,\n    action,\n    method = POST_REQUEST,\n    headers,\n    encType,\n    onError,\n    render,\n    onSuccess,\n    validateStatus,\n    ...rest\n  } = props;\n\n  const submit = async (event?: React.BaseSyntheticEvent) => {\n    let hasError = false;\n    let type = '';\n\n    await control.handleSubmit(async (data) => {\n      const formData = new FormData();\n      let formDataJson = '';\n\n      try {\n        formDataJson = JSON.stringify(data);\n      } catch {}\n\n      const flattenFormValues = flatten(control._formValues);\n\n      for (const key in flattenFormValues) {\n        formData.append(key, flattenFormValues[key]);\n      }\n\n      if (onSubmit) {\n        await onSubmit({\n          data,\n          event,\n          method,\n          formData,\n          formDataJson,\n        });\n      }\n\n      if (action) {\n        try {\n          const shouldStringifySubmissionData = [\n            headers && headers['Content-Type'],\n            encType,\n          ].some((value) => value && value.includes('json'));\n\n          const response = await fetch(String(action), {\n            method,\n            headers: {\n              ...headers,\n              ...(encType && encType !== 'multipart/form-data'\n                ? { 'Content-Type': encType }\n                : {}),\n            },\n            body: shouldStringifySubmissionData ? formDataJson : formData,\n          });\n\n          if (\n            response &&\n            (validateStatus\n              ? !validateStatus(response.status)\n              : response.status < 200 || response.status >= 300)\n          ) {\n            hasError = true;\n            onError && onError({ response });\n            type = String(response.status);\n          } else {\n            onSuccess && onSuccess({ response });\n          }\n        } catch (error: unknown) {\n          hasError = true;\n          onError && onError({ error });\n        }\n      }\n    })(event);\n\n    if (hasError && props.control) {\n      props.control._subjects.state.next({\n        isSubmitSuccessful: false,\n      });\n      props.control.setError('root.server', {\n        type,\n      });\n    }\n  };\n\n  React.useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  return render ? (\n    <>\n      {render({\n        submit,\n      })}\n    </>\n  ) : (\n    <form\n      noValidate={mounted}\n      action={action}\n      method={method}\n      encType={encType}\n      onSubmit={submit}\n      {...rest}\n    >\n      {children}\n    </form>\n  );\n}\n\nexport { Form };\n", "import type {\n  InternalFieldErrors,\n  InternalFieldName,\n  ValidateResult,\n} from '../types';\n\nexport default (\n  name: InternalFieldName,\n  validateAllFieldCriteria: boolean,\n  errors: InternalFieldErrors,\n  type: string,\n  message: ValidateResult,\n) =>\n  validateAllFieldCriteria\n    ? {\n        ...errors[name],\n        types: {\n          ...(errors[name] && errors[name]!.types ? errors[name]!.types : {}),\n          [type]: message || true,\n        },\n      }\n    : {};\n", "export default <T>(value: T) => (Array.isArray(value) ? value : [value]);\n", "import type { Noop } from '../types';\n\nexport type Observer<T> = {\n  next: (value: T) => void;\n};\n\nexport type Subscription = {\n  unsubscribe: Noop;\n};\n\nexport type Subject<T> = {\n  readonly observers: Observer<T>[];\n  subscribe: (value: Observer<T>) => Subscription;\n  unsubscribe: Noop;\n} & Observer<T>;\n\nexport default <T>(): Subject<T> => {\n  let _observers: Observer<T>[] = [];\n\n  const next = (value: T) => {\n    for (const observer of _observers) {\n      observer.next && observer.next(value);\n    }\n  };\n\n  const subscribe = (observer: Observer<T>): Subscription => {\n    _observers.push(observer);\n    return {\n      unsubscribe: () => {\n        _observers = _observers.filter((o) => o !== observer);\n      },\n    };\n  };\n\n  const unsubscribe = () => {\n    _observers = [];\n  };\n\n  return {\n    get observers() {\n      return _observers;\n    },\n    next,\n    subscribe,\n    unsubscribe,\n  };\n};\n", "import type { EmptyObject } from '../types';\n\nimport isObject from './isObject';\n\nexport default (value: unknown): value is EmptyObject =>\n  isObject(value) && !Object.keys(value).length;\n", "import type { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'file';\n", "export default (value: unknown): value is Function =>\n  typeof value === 'function';\n", "import isWeb from './isWeb';\n\nexport default (value: unknown): value is HTMLElement => {\n  if (!isWeb) {\n    return false;\n  }\n\n  const owner = value ? ((value as HTMLElement).ownerDocument as Document) : 0;\n  return (\n    value instanceof\n    (owner && owner.defaultView ? owner.defaultView.HTMLElement : HTMLElement)\n  );\n};\n", "import type { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLSelectElement =>\n  element.type === `select-multiple`;\n", "import type { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'radio';\n", "import type { Ref } from '../types';\n\nimport isHTMLElement from './isHTMLElement';\n\nexport default (ref: Ref) => isHTMLElement(ref) && ref.isConnected;\n", "import isEmptyObject from './isEmptyObject';\nimport isKey from './isKey';\nimport isObject from './isObject';\nimport isUndefined from './isUndefined';\nimport stringToPath from './stringToPath';\n\nfunction baseGet(object: any, updatePath: (string | number)[]) {\n  const length = updatePath.slice(0, -1).length;\n  let index = 0;\n\n  while (index < length) {\n    object = isUndefined(object) ? index++ : object[updatePath[index++]];\n  }\n\n  return object;\n}\n\nfunction isEmptyArray(obj: unknown[]) {\n  for (const key in obj) {\n    if (obj.hasOwnProperty(key) && !isUndefined(obj[key])) {\n      return false;\n    }\n  }\n  return true;\n}\n\nexport default function unset(object: any, path: string | (string | number)[]) {\n  const paths = Array.isArray(path)\n    ? path\n    : isKey(path)\n      ? [path]\n      : stringToPath(path);\n\n  const childObject = paths.length === 1 ? object : baseGet(object, paths);\n\n  const index = paths.length - 1;\n  const key = paths[index];\n\n  if (childObject) {\n    delete childObject[key];\n  }\n\n  if (\n    index !== 0 &&\n    ((isObject(childObject) && isEmptyObject(childObject)) ||\n      (Array.isArray(childObject) && isEmptyArray(childObject)))\n  ) {\n    unset(object, paths.slice(0, -1));\n  }\n\n  return object;\n}\n", "import isFunction from './isFunction';\n\nexport default <T>(data: T): boolean => {\n  for (const key in data) {\n    if (isFunction(data[key])) {\n      return true;\n    }\n  }\n  return false;\n};\n", "import deepEqual from '../utils/deepEqual';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isPrimitive from '../utils/isPrimitive';\nimport isUndefined from '../utils/isUndefined';\nimport objectHasFunction from '../utils/objectHasFunction';\n\nfunction markFieldsDirty<T>(data: T, fields: Record<string, any> = {}) {\n  const isParentNodeArray = Array.isArray(data);\n\n  if (isObject(data) || isParentNodeArray) {\n    for (const key in data) {\n      if (\n        Array.isArray(data[key]) ||\n        (isObject(data[key]) && !objectHasFunction(data[key]))\n      ) {\n        fields[key] = Array.isArray(data[key]) ? [] : {};\n        markFieldsDirty(data[key], fields[key]);\n      } else if (!isNullOrUndefined(data[key])) {\n        fields[key] = true;\n      }\n    }\n  }\n\n  return fields;\n}\n\nfunction getDirtyFieldsFromDefaultValues<T>(\n  data: T,\n  formValues: T,\n  dirtyFieldsFromValues: Record<\n    Extract<keyof T, string>,\n    ReturnType<typeof markFieldsDirty> | boolean\n  >,\n) {\n  const isParentNodeArray = Array.isArray(data);\n\n  if (isObject(data) || isParentNodeArray) {\n    for (const key in data) {\n      if (\n        Array.isArray(data[key]) ||\n        (isObject(data[key]) && !objectHasFunction(data[key]))\n      ) {\n        if (\n          isUndefined(formValues) ||\n          isPrimitive(dirtyFieldsFromValues[key])\n        ) {\n          dirtyFieldsFromValues[key] = Array.isArray(data[key])\n            ? markFieldsDirty(data[key], [])\n            : { ...markFieldsDirty(data[key]) };\n        } else {\n          getDirtyFieldsFromDefaultValues(\n            data[key],\n            isNullOrUndefined(formValues) ? {} : formValues[key],\n            dirtyFieldsFromValues[key],\n          );\n        }\n      } else {\n        dirtyFieldsFromValues[key] = !deepEqual(data[key], formValues[key]);\n      }\n    }\n  }\n\n  return dirtyFieldsFromValues;\n}\n\nexport default <T>(defaultValues: T, formValues: T) =>\n  getDirtyFieldsFromDefaultValues(\n    defaultValues,\n    formValues,\n    markFieldsDirty(formValues),\n  );\n", "import isUndefined from '../utils/isUndefined';\n\ntype CheckboxFieldResult = {\n  isValid: boolean;\n  value: string | string[] | boolean | undefined;\n};\n\nconst defaultResult: CheckboxFieldResult = {\n  value: false,\n  isValid: false,\n};\n\nconst validResult = { value: true, isValid: true };\n\nexport default (options?: HTMLInputElement[]): CheckboxFieldResult => {\n  if (Array.isArray(options)) {\n    if (options.length > 1) {\n      const values = options\n        .filter((option) => option && option.checked && !option.disabled)\n        .map((option) => option.value);\n      return { value: values, isValid: !!values.length };\n    }\n\n    return options[0].checked && !options[0].disabled\n      ? // @ts-expect-error expected to work in the browser\n        options[0].attributes && !isUndefined(options[0].attributes.value)\n        ? isUndefined(options[0].value) || options[0].value === ''\n          ? validResult\n          : { value: options[0].value, isValid: true }\n        : validResult\n      : defaultResult;\n  }\n\n  return defaultResult;\n};\n", "import type { Field, NativeFieldValue } from '../types';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\n\nexport default <T extends NativeFieldValue>(\n  value: T,\n  { valueAsNumber, valueAsDate, setValueAs }: Field['_f'],\n) =>\n  isUndefined(value)\n    ? value\n    : valueAsNumber\n      ? value === ''\n        ? NaN\n        : value\n          ? +value\n          : value\n      : valueAsDate && isString(value)\n        ? new Date(value)\n        : setValueAs\n          ? setValueAs(value)\n          : value;\n", "type RadioFieldResult = {\n  isValid: boolean;\n  value: number | string | null;\n};\n\nconst defaultReturn: RadioFieldResult = {\n  isValid: false,\n  value: null,\n};\n\nexport default (options?: HTMLInputElement[]): RadioFieldResult =>\n  Array.isArray(options)\n    ? options.reduce(\n        (previous, option): RadioFieldResult =>\n          option && option.checked && !option.disabled\n            ? {\n                isValid: true,\n                value: option.value,\n              }\n            : previous,\n        defaultReturn,\n      )\n    : defaultReturn;\n", "import type { Field } from '../types';\nimport isCheckBox from '../utils/isCheckBoxInput';\nimport isFileInput from '../utils/isFileInput';\nimport isMultipleSelect from '../utils/isMultipleSelect';\nimport isRadioInput from '../utils/isRadioInput';\nimport isUndefined from '../utils/isUndefined';\n\nimport getCheckboxValue from './getCheckboxValue';\nimport getFieldValueAs from './getFieldValueAs';\nimport getRadioValue from './getRadioValue';\n\nexport default function getFieldValue(_f: Field['_f']) {\n  const ref = _f.ref;\n\n  if (isFileInput(ref)) {\n    return ref.files;\n  }\n\n  if (isRadioInput(ref)) {\n    return getRadioValue(_f.refs).value;\n  }\n\n  if (isMultipleSelect(ref)) {\n    return [...ref.selectedOptions].map(({ value }) => value);\n  }\n\n  if (isCheckBox(ref)) {\n    return getCheckboxValue(_f.refs).value;\n  }\n\n  return getFieldValueAs(isUndefined(ref.value) ? _f.ref.value : ref.value, _f);\n}\n", "import type {\n  CriteriaMode,\n  Field,\n  FieldName,\n  FieldRefs,\n  FieldValues,\n  InternalFieldName,\n} from '../types';\nimport { get } from '../utils';\nimport set from '../utils/set';\n\nexport default <TFieldValues extends FieldValues>(\n  fieldsNames: Set<InternalFieldName> | InternalFieldName[],\n  _fields: FieldRefs,\n  criteriaMode?: CriteriaMode,\n  shouldUseNativeValidation?: boolean | undefined,\n) => {\n  const fields: Record<InternalFieldName, Field['_f']> = {};\n\n  for (const name of fieldsNames) {\n    const field: Field = get(_fields, name);\n\n    field && set(fields, name, field._f);\n  }\n\n  return {\n    criteriaMode,\n    names: [...fieldsNames] as FieldName<TFieldValues>[],\n    fields,\n    shouldUseNativeValidation,\n  };\n};\n", "export default (value: unknown): value is RegExp => value instanceof RegExp;\n", "import type {\n  ValidationRule,\n  ValidationValue,\n  ValidationValueMessage,\n} from '../types';\nimport isObject from '../utils/isObject';\nimport isRegex from '../utils/isRegex';\nimport isUndefined from '../utils/isUndefined';\n\nexport default <T extends ValidationValue>(\n  rule?: ValidationRule<T> | ValidationValueMessage<T>,\n) =>\n  isUndefined(rule)\n    ? rule\n    : isRegex(rule)\n      ? rule.source\n      : isObject(rule)\n        ? isRegex(rule.value)\n          ? rule.value.source\n          : rule.value\n        : rule;\n", "import { VALIDATION_MODE } from '../constants';\nimport type { Mode, ValidationModeFlags } from '../types';\n\nexport default (mode?: Mode): ValidationModeFlags => ({\n  isOnSubmit: !mode || mode === VALIDATION_MODE.onSubmit,\n  isOnBlur: mode === VALIDATION_MODE.onBlur,\n  isOnChange: mode === VALIDATION_MODE.onChange,\n  isOnAll: mode === VALIDATION_MODE.all,\n  isOnTouch: mode === VALIDATION_MODE.onTouched,\n});\n", "import type { Field, Validate } from '../types';\nimport isFunction from '../utils/isFunction';\nimport isObject from '../utils/isObject';\n\nconst ASYNC_FUNCTION = 'AsyncFunction';\n\nexport default (fieldReference: Field['_f']) =>\n  !!fieldReference &&\n  !!fieldReference.validate &&\n  !!(\n    (isFunction(fieldReference.validate) &&\n      fieldReference.validate.constructor.name === ASYNC_FUNCTION) ||\n    (isObject(fieldReference.validate) &&\n      Object.values(fieldReference.validate).find(\n        (validateFunction: Validate<unknown, unknown>) =>\n          validateFunction.constructor.name === ASYNC_FUNCTION,\n      ))\n  );\n", "import type { InternalFieldName, Names } from '../types';\n\nexport default (\n  name: InternalFieldName,\n  _names: Names,\n  isBlurEvent?: boolean,\n) =>\n  !isBlurEvent &&\n  (_names.watchAll ||\n    _names.watch.has(name) ||\n    [..._names.watch].some(\n      (watchName) =>\n        name.startsWith(watchName) &&\n        /^\\.\\w+/.test(name.slice(watchName.length)),\n    ));\n", "import type { FieldRefs, InternalFieldName, Ref } from '../types';\nimport { get } from '../utils';\nimport isObject from '../utils/isObject';\n\nconst iterateFieldsByAction = (\n  fields: FieldRefs,\n  action: (ref: Ref, name: string) => 1 | undefined | void,\n  fieldsNames?: Set<InternalFieldName> | InternalFieldName[] | 0,\n  abortEarly?: boolean,\n) => {\n  for (const key of fieldsNames || Object.keys(fields)) {\n    const field = get(fields, key);\n\n    if (field) {\n      const { _f, ...currentField } = field;\n\n      if (_f) {\n        if (_f.refs && _f.refs[0] && action(_f.refs[0], key) && !abortEarly) {\n          return true;\n        } else if (_f.ref && action(_f.ref, _f.name) && !abortEarly) {\n          return true;\n        } else {\n          if (iterateFieldsByAction(currentField, action)) {\n            break;\n          }\n        }\n      } else if (isObject(currentField)) {\n        if (iterateFieldsByAction(currentField as FieldRefs, action)) {\n          break;\n        }\n      }\n    }\n  }\n  return;\n};\nexport default iterateFieldsByAction;\n", "import type { FieldError, FieldErrors, FieldValues } from '../types';\nimport get from '../utils/get';\nimport isKey from '../utils/isKey';\n\nexport default function schemaErrorLookup<T extends FieldValues = FieldValues>(\n  errors: FieldErrors<T>,\n  _fields: FieldValues,\n  name: string,\n): {\n  error?: FieldError;\n  name: string;\n} {\n  const error = get(errors, name);\n\n  if (error || isKey(name)) {\n    return {\n      error,\n      name,\n    };\n  }\n\n  const names = name.split('.');\n\n  while (names.length) {\n    const fieldName = names.join('.');\n    const field = get(_fields, fieldName);\n    const foundError = get(errors, fieldName);\n\n    if (field && !Array.isArray(field) && name !== fieldName) {\n      return { name };\n    }\n\n    if (foundError && foundError.type) {\n      return {\n        name: fieldName,\n        error: foundError,\n      };\n    }\n\n    if (foundError && foundError.root && foundError.root.type) {\n      return {\n        name: `${fieldName}.root`,\n        error: foundError.root,\n      };\n    }\n\n    names.pop();\n  }\n\n  return {\n    name,\n  };\n}\n", "import { VALIDATION_MODE } from '../constants';\nimport type {\n  FieldValues,\n  FormState,\n  InternalFieldName,\n  ReadFormState,\n} from '../types';\nimport isEmptyObject from '../utils/isEmptyObject';\n\nexport default <T extends FieldValues, K extends ReadFormState>(\n  formStateData: Partial<FormState<T>> & {\n    name?: InternalFieldName;\n    values?: T;\n  },\n  _proxyFormState: K,\n  updateFormState: (formState: Partial<FormState<T>>) => void,\n  isRoot?: boolean,\n) => {\n  updateFormState(formStateData);\n  const { name, ...formState } = formStateData;\n\n  return (\n    isEmptyObject(formState) ||\n    Object.keys(formState).length >= Object.keys(_proxyFormState).length ||\n    Object.keys(formState).find(\n      (key) =>\n        _proxyFormState[key as keyof ReadFormState] ===\n        (!isRoot || VALIDATION_MODE.all),\n    )\n  );\n};\n", "import type {\n  FieldError,\n  FieldErrors,\n  FieldValues,\n  InternalFieldName,\n} from '../types';\nimport convertToArrayPayload from '../utils/convertToArrayPayload';\nimport get from '../utils/get';\nimport set from '../utils/set';\n\nexport default <T extends FieldValues = FieldValues>(\n  errors: FieldErrors<T>,\n  error: Partial<Record<string, FieldError>>,\n  name: InternalFieldName,\n): FieldErrors<T> => {\n  const fieldArrayErrors = convertToArrayPayload(get(errors, name));\n  set(fieldArrayErrors, 'root', error[name]);\n  set(errors, name, fieldArrayErrors);\n  return errors;\n};\n", "import type { Message } from '../types';\nimport isString from '../utils/isString';\n\nexport default (value: unknown): value is Message => isString(value);\n", "import type { Field<PERSON>rror, Ref, ValidateResult } from '../types';\nimport isBoolean from '../utils/isBoolean';\nimport isMessage from '../utils/isMessage';\n\nexport default function getValidateError(\n  result: ValidateResult,\n  ref: Ref,\n  type = 'validate',\n): FieldError | void {\n  if (\n    isMessage(result) ||\n    (Array.isArray(result) && result.every(isMessage)) ||\n    (isBoolean(result) && !result)\n  ) {\n    return {\n      type,\n      message: isMessage(result) ? result : '',\n      ref,\n    };\n  }\n}\n", "import type { ValidationRule } from '../types';\nimport isObject from '../utils/isObject';\nimport isRegex from '../utils/isRegex';\n\nexport default (validationData?: ValidationRule) =>\n  isObject(validationData) && !isRegex(validationData)\n    ? validationData\n    : {\n        value: validationData,\n        message: '',\n      };\n", "import { INPUT_VALIDATION_RULES } from '../constants';\nimport type {\n  Field,\n  FieldError,\n  FieldValues,\n  InternalFieldErrors,\n  InternalNameSet,\n  MaxType,\n  Message,\n  MinType,\n  NativeFieldValue,\n} from '../types';\nimport get from '../utils/get';\nimport isBoolean from '../utils/isBoolean';\nimport isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isEmptyObject from '../utils/isEmptyObject';\nimport isFileInput from '../utils/isFileInput';\nimport isFunction from '../utils/isFunction';\nimport isHTMLElement from '../utils/isHTMLElement';\nimport isMessage from '../utils/isMessage';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isRadioInput from '../utils/isRadioInput';\nimport isRegex from '../utils/isRegex';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\n\nimport appendErrors from './appendErrors';\nimport getCheckboxValue from './getCheckboxValue';\nimport getRadioValue from './getRadioValue';\nimport getValidateError from './getValidateError';\nimport getValueAndMessage from './getValueAndMessage';\n\nexport default async <T extends FieldValues>(\n  field: Field,\n  disabledFieldNames: InternalNameSet,\n  formValues: T,\n  validateAllFieldCriteria: boolean,\n  shouldUseNativeValidation?: boolean,\n  isFieldArray?: boolean,\n): Promise<InternalFieldErrors> => {\n  const {\n    ref,\n    refs,\n    required,\n    maxLength,\n    minLength,\n    min,\n    max,\n    pattern,\n    validate,\n    name,\n    valueAsNumber,\n    mount,\n  } = field._f;\n  const inputValue: NativeFieldValue = get(formValues, name);\n  if (!mount || disabledFieldNames.has(name)) {\n    return {};\n  }\n  const inputRef: HTMLInputElement = refs ? refs[0] : (ref as HTMLInputElement);\n  const setCustomValidity = (message?: string | boolean) => {\n    if (shouldUseNativeValidation && inputRef.reportValidity) {\n      inputRef.setCustomValidity(isBoolean(message) ? '' : message || '');\n      inputRef.reportValidity();\n    }\n  };\n  const error: InternalFieldErrors = {};\n  const isRadio = isRadioInput(ref);\n  const isCheckBox = isCheckBoxInput(ref);\n  const isRadioOrCheckbox = isRadio || isCheckBox;\n  const isEmpty =\n    ((valueAsNumber || isFileInput(ref)) &&\n      isUndefined(ref.value) &&\n      isUndefined(inputValue)) ||\n    (isHTMLElement(ref) && ref.value === '') ||\n    inputValue === '' ||\n    (Array.isArray(inputValue) && !inputValue.length);\n  const appendErrorsCurry = appendErrors.bind(\n    null,\n    name,\n    validateAllFieldCriteria,\n    error,\n  );\n  const getMinMaxMessage = (\n    exceedMax: boolean,\n    maxLengthMessage: Message,\n    minLengthMessage: Message,\n    maxType: MaxType = INPUT_VALIDATION_RULES.maxLength,\n    minType: MinType = INPUT_VALIDATION_RULES.minLength,\n  ) => {\n    const message = exceedMax ? maxLengthMessage : minLengthMessage;\n    error[name] = {\n      type: exceedMax ? maxType : minType,\n      message,\n      ref,\n      ...appendErrorsCurry(exceedMax ? maxType : minType, message),\n    };\n  };\n\n  if (\n    isFieldArray\n      ? !Array.isArray(inputValue) || !inputValue.length\n      : required &&\n        ((!isRadioOrCheckbox && (isEmpty || isNullOrUndefined(inputValue))) ||\n          (isBoolean(inputValue) && !inputValue) ||\n          (isCheckBox && !getCheckboxValue(refs).isValid) ||\n          (isRadio && !getRadioValue(refs).isValid))\n  ) {\n    const { value, message } = isMessage(required)\n      ? { value: !!required, message: required }\n      : getValueAndMessage(required);\n\n    if (value) {\n      error[name] = {\n        type: INPUT_VALIDATION_RULES.required,\n        message,\n        ref: inputRef,\n        ...appendErrorsCurry(INPUT_VALIDATION_RULES.required, message),\n      };\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(message);\n        return error;\n      }\n    }\n  }\n\n  if (!isEmpty && (!isNullOrUndefined(min) || !isNullOrUndefined(max))) {\n    let exceedMax;\n    let exceedMin;\n    const maxOutput = getValueAndMessage(max);\n    const minOutput = getValueAndMessage(min);\n\n    if (!isNullOrUndefined(inputValue) && !isNaN(inputValue as number)) {\n      const valueNumber =\n        (ref as HTMLInputElement).valueAsNumber ||\n        (inputValue ? +inputValue : inputValue);\n      if (!isNullOrUndefined(maxOutput.value)) {\n        exceedMax = valueNumber > maxOutput.value;\n      }\n      if (!isNullOrUndefined(minOutput.value)) {\n        exceedMin = valueNumber < minOutput.value;\n      }\n    } else {\n      const valueDate =\n        (ref as HTMLInputElement).valueAsDate || new Date(inputValue as string);\n      const convertTimeToDate = (time: unknown) =>\n        new Date(new Date().toDateString() + ' ' + time);\n      const isTime = ref.type == 'time';\n      const isWeek = ref.type == 'week';\n\n      if (isString(maxOutput.value) && inputValue) {\n        exceedMax = isTime\n          ? convertTimeToDate(inputValue) > convertTimeToDate(maxOutput.value)\n          : isWeek\n            ? inputValue > maxOutput.value\n            : valueDate > new Date(maxOutput.value);\n      }\n\n      if (isString(minOutput.value) && inputValue) {\n        exceedMin = isTime\n          ? convertTimeToDate(inputValue) < convertTimeToDate(minOutput.value)\n          : isWeek\n            ? inputValue < minOutput.value\n            : valueDate < new Date(minOutput.value);\n      }\n    }\n\n    if (exceedMax || exceedMin) {\n      getMinMaxMessage(\n        !!exceedMax,\n        maxOutput.message,\n        minOutput.message,\n        INPUT_VALIDATION_RULES.max,\n        INPUT_VALIDATION_RULES.min,\n      );\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(error[name]!.message);\n        return error;\n      }\n    }\n  }\n\n  if (\n    (maxLength || minLength) &&\n    !isEmpty &&\n    (isString(inputValue) || (isFieldArray && Array.isArray(inputValue)))\n  ) {\n    const maxLengthOutput = getValueAndMessage(maxLength);\n    const minLengthOutput = getValueAndMessage(minLength);\n    const exceedMax =\n      !isNullOrUndefined(maxLengthOutput.value) &&\n      inputValue.length > +maxLengthOutput.value;\n    const exceedMin =\n      !isNullOrUndefined(minLengthOutput.value) &&\n      inputValue.length < +minLengthOutput.value;\n\n    if (exceedMax || exceedMin) {\n      getMinMaxMessage(\n        exceedMax,\n        maxLengthOutput.message,\n        minLengthOutput.message,\n      );\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(error[name]!.message);\n        return error;\n      }\n    }\n  }\n\n  if (pattern && !isEmpty && isString(inputValue)) {\n    const { value: patternValue, message } = getValueAndMessage(pattern);\n\n    if (isRegex(patternValue) && !inputValue.match(patternValue)) {\n      error[name] = {\n        type: INPUT_VALIDATION_RULES.pattern,\n        message,\n        ref,\n        ...appendErrorsCurry(INPUT_VALIDATION_RULES.pattern, message),\n      };\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(message);\n        return error;\n      }\n    }\n  }\n\n  if (validate) {\n    if (isFunction(validate)) {\n      const result = await validate(inputValue, formValues);\n      const validateError = getValidateError(result, inputRef);\n\n      if (validateError) {\n        error[name] = {\n          ...validateError,\n          ...appendErrorsCurry(\n            INPUT_VALIDATION_RULES.validate,\n            validateError.message,\n          ),\n        };\n        if (!validateAllFieldCriteria) {\n          setCustomValidity(validateError.message);\n          return error;\n        }\n      }\n    } else if (isObject(validate)) {\n      let validationResult = {} as FieldError;\n\n      for (const key in validate) {\n        if (!isEmptyObject(validationResult) && !validateAllFieldCriteria) {\n          break;\n        }\n\n        const validateError = getValidateError(\n          await validate[key](inputValue, formValues),\n          inputRef,\n          key,\n        );\n\n        if (validateError) {\n          validationResult = {\n            ...validateError,\n            ...appendErrorsCurry(key, validateError.message),\n          };\n\n          setCustomValidity(validateError.message);\n\n          if (validateAllFieldCriteria) {\n            error[name] = validationResult;\n          }\n        }\n      }\n\n      if (!isEmptyObject(validationResult)) {\n        error[name] = {\n          ref: inputRef,\n          ...validationResult,\n        };\n        if (!validateAllFieldCriteria) {\n          return error;\n        }\n      }\n    }\n  }\n\n  setCustomValidity(true);\n  return error;\n};\n", "import { EVENTS, VALIDATION_MODE } from '../constants';\nimport type {\n  BatchField<PERSON>rrayUpdate,\n  ChangeHandler,\n  Control,\n  DeepPartial,\n  DelayCallback,\n  EventType,\n  Field,\n  FieldError,\n  FieldErrors,\n  FieldNamesMarkedBoolean,\n  FieldPath,\n  FieldRefs,\n  FieldValues,\n  FormState,\n  FromSubscribe,\n  GetIsDirty,\n  InternalFieldName,\n  Names,\n  Path,\n  ReadFormState,\n  Ref,\n  SetFieldValue,\n  SetValueConfig,\n  Subjects,\n  UseFormClearErrors,\n  UseFormGetFieldState,\n  UseFormGetValues,\n  UseFormHandleSubmit,\n  UseFormProps,\n  UseFormRegister,\n  UseFormReset,\n  UseFormResetField,\n  UseFormReturn,\n  UseFormSetError,\n  UseFormSetFocus,\n  UseFormSetValue,\n  UseFormSubscribe,\n  UseFormTrigger,\n  UseFormUnregister,\n  UseFormWatch,\n  WatchInternal,\n  WatchObserver,\n} from '../types';\nimport cloneObject from '../utils/cloneObject';\nimport compact from '../utils/compact';\nimport convertToArrayPayload from '../utils/convertToArrayPayload';\nimport createSubject from '../utils/createSubject';\nimport deepEqual from '../utils/deepEqual';\nimport get from '../utils/get';\nimport isBoolean from '../utils/isBoolean';\nimport isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isDateObject from '../utils/isDateObject';\nimport isEmptyObject from '../utils/isEmptyObject';\nimport isFileInput from '../utils/isFileInput';\nimport isFunction from '../utils/isFunction';\nimport isHTMLElement from '../utils/isHTMLElement';\nimport isMultipleSelect from '../utils/isMultipleSelect';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isRadioOrCheckbox from '../utils/isRadioOrCheckbox';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\nimport isWeb from '../utils/isWeb';\nimport live from '../utils/live';\nimport set from '../utils/set';\nimport unset from '../utils/unset';\n\nimport generateWatchOutput from './generateWatchOutput';\nimport getDirtyFields from './getDirtyFields';\nimport getEventValue from './getEventValue';\nimport getFieldValue from './getFieldValue';\nimport getFieldValueAs from './getFieldValueAs';\nimport getResolverOptions from './getResolverOptions';\nimport getRuleValue from './getRuleValue';\nimport getValidationModes from './getValidationModes';\nimport hasPromiseValidation from './hasPromiseValidation';\nimport hasValidation from './hasValidation';\nimport isNameInFieldArray from './isNameInFieldArray';\nimport isWatched from './isWatched';\nimport iterateFieldsByAction from './iterateFieldsByAction';\nimport schemaErrorLookup from './schemaErrorLookup';\nimport shouldRenderFormState from './shouldRenderFormState';\nimport shouldSubscribeByName from './shouldSubscribeByName';\nimport skipValidation from './skipValidation';\nimport unsetEmptyArray from './unsetEmptyArray';\nimport updateFieldArrayRootError from './updateFieldArrayRootError';\nimport validateField from './validateField';\n\nconst defaultOptions = {\n  mode: VALIDATION_MODE.onSubmit,\n  reValidateMode: VALIDATION_MODE.onChange,\n  shouldFocusError: true,\n} as const;\n\nexport function createFormControl<\n  TFieldValues extends FieldValues = FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(\n  props: UseFormProps<TFieldValues, TContext, TTransformedValues> = {},\n): Omit<\n  UseFormReturn<TFieldValues, TContext, TTransformedValues>,\n  'formState'\n> & {\n  formControl: Omit<\n    UseFormReturn<TFieldValues, TContext, TTransformedValues>,\n    'formState'\n  >;\n} {\n  let _options = {\n    ...defaultOptions,\n    ...props,\n  };\n  let _formState: FormState<TFieldValues> = {\n    submitCount: 0,\n    isDirty: false,\n    isReady: false,\n    isLoading: isFunction(_options.defaultValues),\n    isValidating: false,\n    isSubmitted: false,\n    isSubmitting: false,\n    isSubmitSuccessful: false,\n    isValid: false,\n    touchedFields: {},\n    dirtyFields: {},\n    validatingFields: {},\n    errors: _options.errors || {},\n    disabled: _options.disabled || false,\n  };\n  let _fields: FieldRefs = {};\n  let _defaultValues =\n    isObject(_options.defaultValues) || isObject(_options.values)\n      ? cloneObject(_options.defaultValues || _options.values) || {}\n      : {};\n  let _formValues = _options.shouldUnregister\n    ? ({} as TFieldValues)\n    : (cloneObject(_defaultValues) as TFieldValues);\n  let _state = {\n    action: false,\n    mount: false,\n    watch: false,\n  };\n  let _names: Names = {\n    mount: new Set(),\n    disabled: new Set(),\n    unMount: new Set(),\n    array: new Set(),\n    watch: new Set(),\n  };\n  let delayErrorCallback: DelayCallback | null;\n  let timer = 0;\n  const _proxyFormState: ReadFormState = {\n    isDirty: false,\n    dirtyFields: false,\n    validatingFields: false,\n    touchedFields: false,\n    isValidating: false,\n    isValid: false,\n    errors: false,\n  };\n  let _proxySubscribeFormState = {\n    ..._proxyFormState,\n  };\n  const _subjects: Subjects<TFieldValues> = {\n    array: createSubject(),\n    state: createSubject(),\n  };\n\n  const shouldDisplayAllAssociatedErrors =\n    _options.criteriaMode === VALIDATION_MODE.all;\n\n  const debounce =\n    <T extends Function>(callback: T) =>\n    (wait: number) => {\n      clearTimeout(timer);\n      timer = setTimeout(callback, wait);\n    };\n\n  const _setValid = async (shouldUpdateValid?: boolean) => {\n    if (\n      !_options.disabled &&\n      (_proxyFormState.isValid ||\n        _proxySubscribeFormState.isValid ||\n        shouldUpdateValid)\n    ) {\n      const isValid = _options.resolver\n        ? isEmptyObject((await _runSchema()).errors)\n        : await executeBuiltInValidation(_fields, true);\n\n      if (isValid !== _formState.isValid) {\n        _subjects.state.next({\n          isValid,\n        });\n      }\n    }\n  };\n\n  const _updateIsValidating = (names?: string[], isValidating?: boolean) => {\n    if (\n      !_options.disabled &&\n      (_proxyFormState.isValidating ||\n        _proxyFormState.validatingFields ||\n        _proxySubscribeFormState.isValidating ||\n        _proxySubscribeFormState.validatingFields)\n    ) {\n      (names || Array.from(_names.mount)).forEach((name) => {\n        if (name) {\n          isValidating\n            ? set(_formState.validatingFields, name, isValidating)\n            : unset(_formState.validatingFields, name);\n        }\n      });\n\n      _subjects.state.next({\n        validatingFields: _formState.validatingFields,\n        isValidating: !isEmptyObject(_formState.validatingFields),\n      });\n    }\n  };\n\n  const _setFieldArray: BatchFieldArrayUpdate = (\n    name,\n    values = [],\n    method,\n    args,\n    shouldSetValues = true,\n    shouldUpdateFieldsAndState = true,\n  ) => {\n    if (args && method && !_options.disabled) {\n      _state.action = true;\n      if (shouldUpdateFieldsAndState && Array.isArray(get(_fields, name))) {\n        const fieldValues = method(get(_fields, name), args.argA, args.argB);\n        shouldSetValues && set(_fields, name, fieldValues);\n      }\n\n      if (\n        shouldUpdateFieldsAndState &&\n        Array.isArray(get(_formState.errors, name))\n      ) {\n        const errors = method(\n          get(_formState.errors, name),\n          args.argA,\n          args.argB,\n        );\n        shouldSetValues && set(_formState.errors, name, errors);\n        unsetEmptyArray(_formState.errors, name);\n      }\n\n      if (\n        (_proxyFormState.touchedFields ||\n          _proxySubscribeFormState.touchedFields) &&\n        shouldUpdateFieldsAndState &&\n        Array.isArray(get(_formState.touchedFields, name))\n      ) {\n        const touchedFields = method(\n          get(_formState.touchedFields, name),\n          args.argA,\n          args.argB,\n        );\n        shouldSetValues && set(_formState.touchedFields, name, touchedFields);\n      }\n\n      if (_proxyFormState.dirtyFields || _proxySubscribeFormState.dirtyFields) {\n        _formState.dirtyFields = getDirtyFields(_defaultValues, _formValues);\n      }\n\n      _subjects.state.next({\n        name,\n        isDirty: _getDirty(name, values),\n        dirtyFields: _formState.dirtyFields,\n        errors: _formState.errors,\n        isValid: _formState.isValid,\n      });\n    } else {\n      set(_formValues, name, values);\n    }\n  };\n\n  const updateErrors = (name: InternalFieldName, error: FieldError) => {\n    set(_formState.errors, name, error);\n    _subjects.state.next({\n      errors: _formState.errors,\n    });\n  };\n\n  const _setErrors = (errors: FieldErrors<TFieldValues>) => {\n    _formState.errors = errors;\n    _subjects.state.next({\n      errors: _formState.errors,\n      isValid: false,\n    });\n  };\n\n  const updateValidAndValue = (\n    name: InternalFieldName,\n    shouldSkipSetValueAs: boolean,\n    value?: unknown,\n    ref?: Ref,\n  ) => {\n    const field: Field = get(_fields, name);\n\n    if (field) {\n      const defaultValue = get(\n        _formValues,\n        name,\n        isUndefined(value) ? get(_defaultValues, name) : value,\n      );\n\n      isUndefined(defaultValue) ||\n      (ref && (ref as HTMLInputElement).defaultChecked) ||\n      shouldSkipSetValueAs\n        ? set(\n            _formValues,\n            name,\n            shouldSkipSetValueAs ? defaultValue : getFieldValue(field._f),\n          )\n        : setFieldValue(name, defaultValue);\n\n      _state.mount && _setValid();\n    }\n  };\n\n  const updateTouchAndDirty = (\n    name: InternalFieldName,\n    fieldValue: unknown,\n    isBlurEvent?: boolean,\n    shouldDirty?: boolean,\n    shouldRender?: boolean,\n  ): Partial<\n    Pick<FormState<TFieldValues>, 'dirtyFields' | 'isDirty' | 'touchedFields'>\n  > => {\n    let shouldUpdateField = false;\n    let isPreviousDirty = false;\n    const output: Partial<FormState<TFieldValues>> & { name: string } = {\n      name,\n    };\n\n    if (!_options.disabled) {\n      if (!isBlurEvent || shouldDirty) {\n        if (_proxyFormState.isDirty || _proxySubscribeFormState.isDirty) {\n          isPreviousDirty = _formState.isDirty;\n          _formState.isDirty = output.isDirty = _getDirty();\n          shouldUpdateField = isPreviousDirty !== output.isDirty;\n        }\n\n        const isCurrentFieldPristine = deepEqual(\n          get(_defaultValues, name),\n          fieldValue,\n        );\n\n        isPreviousDirty = !!get(_formState.dirtyFields, name);\n        isCurrentFieldPristine\n          ? unset(_formState.dirtyFields, name)\n          : set(_formState.dirtyFields, name, true);\n        output.dirtyFields = _formState.dirtyFields;\n        shouldUpdateField =\n          shouldUpdateField ||\n          ((_proxyFormState.dirtyFields ||\n            _proxySubscribeFormState.dirtyFields) &&\n            isPreviousDirty !== !isCurrentFieldPristine);\n      }\n\n      if (isBlurEvent) {\n        const isPreviousFieldTouched = get(_formState.touchedFields, name);\n\n        if (!isPreviousFieldTouched) {\n          set(_formState.touchedFields, name, isBlurEvent);\n          output.touchedFields = _formState.touchedFields;\n          shouldUpdateField =\n            shouldUpdateField ||\n            ((_proxyFormState.touchedFields ||\n              _proxySubscribeFormState.touchedFields) &&\n              isPreviousFieldTouched !== isBlurEvent);\n        }\n      }\n\n      shouldUpdateField && shouldRender && _subjects.state.next(output);\n    }\n\n    return shouldUpdateField ? output : {};\n  };\n\n  const shouldRenderByError = (\n    name: InternalFieldName,\n    isValid?: boolean,\n    error?: FieldError,\n    fieldState?: {\n      dirty?: FieldNamesMarkedBoolean<TFieldValues>;\n      isDirty?: boolean;\n      touched?: FieldNamesMarkedBoolean<TFieldValues>;\n    },\n  ) => {\n    const previousFieldError = get(_formState.errors, name);\n    const shouldUpdateValid =\n      (_proxyFormState.isValid || _proxySubscribeFormState.isValid) &&\n      isBoolean(isValid) &&\n      _formState.isValid !== isValid;\n\n    if (_options.delayError && error) {\n      delayErrorCallback = debounce(() => updateErrors(name, error));\n      delayErrorCallback(_options.delayError);\n    } else {\n      clearTimeout(timer);\n      delayErrorCallback = null;\n      error\n        ? set(_formState.errors, name, error)\n        : unset(_formState.errors, name);\n    }\n\n    if (\n      (error ? !deepEqual(previousFieldError, error) : previousFieldError) ||\n      !isEmptyObject(fieldState) ||\n      shouldUpdateValid\n    ) {\n      const updatedFormState = {\n        ...fieldState,\n        ...(shouldUpdateValid && isBoolean(isValid) ? { isValid } : {}),\n        errors: _formState.errors,\n        name,\n      };\n\n      _formState = {\n        ..._formState,\n        ...updatedFormState,\n      };\n\n      _subjects.state.next(updatedFormState);\n    }\n  };\n\n  const _runSchema = async (name?: InternalFieldName[]) => {\n    _updateIsValidating(name, true);\n    const result = await _options.resolver!(\n      _formValues as TFieldValues,\n      _options.context,\n      getResolverOptions(\n        name || _names.mount,\n        _fields,\n        _options.criteriaMode,\n        _options.shouldUseNativeValidation,\n      ),\n    );\n    _updateIsValidating(name);\n    return result;\n  };\n\n  const executeSchemaAndUpdateState = async (names?: InternalFieldName[]) => {\n    const { errors } = await _runSchema(names);\n\n    if (names) {\n      for (const name of names) {\n        const error = get(errors, name);\n        error\n          ? set(_formState.errors, name, error)\n          : unset(_formState.errors, name);\n      }\n    } else {\n      _formState.errors = errors;\n    }\n\n    return errors;\n  };\n\n  const executeBuiltInValidation = async (\n    fields: FieldRefs,\n    shouldOnlyCheckValid?: boolean,\n    context: {\n      valid: boolean;\n    } = {\n      valid: true,\n    },\n  ) => {\n    for (const name in fields) {\n      const field = fields[name];\n\n      if (field) {\n        const { _f, ...fieldValue } = field as Field;\n\n        if (_f) {\n          const isFieldArrayRoot = _names.array.has(_f.name);\n          const isPromiseFunction =\n            field._f && hasPromiseValidation((field as Field)._f);\n\n          if (isPromiseFunction && _proxyFormState.validatingFields) {\n            _updateIsValidating([name], true);\n          }\n\n          const fieldError = await validateField(\n            field as Field,\n            _names.disabled,\n            _formValues,\n            shouldDisplayAllAssociatedErrors,\n            _options.shouldUseNativeValidation && !shouldOnlyCheckValid,\n            isFieldArrayRoot,\n          );\n\n          if (isPromiseFunction && _proxyFormState.validatingFields) {\n            _updateIsValidating([name]);\n          }\n\n          if (fieldError[_f.name]) {\n            context.valid = false;\n            if (shouldOnlyCheckValid) {\n              break;\n            }\n          }\n\n          !shouldOnlyCheckValid &&\n            (get(fieldError, _f.name)\n              ? isFieldArrayRoot\n                ? updateFieldArrayRootError(\n                    _formState.errors,\n                    fieldError,\n                    _f.name,\n                  )\n                : set(_formState.errors, _f.name, fieldError[_f.name])\n              : unset(_formState.errors, _f.name));\n        }\n\n        !isEmptyObject(fieldValue) &&\n          (await executeBuiltInValidation(\n            fieldValue,\n            shouldOnlyCheckValid,\n            context,\n          ));\n      }\n    }\n\n    return context.valid;\n  };\n\n  const _removeUnmounted = () => {\n    for (const name of _names.unMount) {\n      const field: Field = get(_fields, name);\n\n      field &&\n        (field._f.refs\n          ? field._f.refs.every((ref) => !live(ref))\n          : !live(field._f.ref)) &&\n        unregister(name as FieldPath<TFieldValues>);\n    }\n\n    _names.unMount = new Set();\n  };\n\n  const _getDirty: GetIsDirty = (name, data) =>\n    !_options.disabled &&\n    (name && data && set(_formValues, name, data),\n    !deepEqual(getValues(), _defaultValues));\n\n  const _getWatch: WatchInternal<TFieldValues> = (\n    names,\n    defaultValue,\n    isGlobal,\n  ) =>\n    generateWatchOutput(\n      names,\n      _names,\n      {\n        ...(_state.mount\n          ? _formValues\n          : isUndefined(defaultValue)\n            ? _defaultValues\n            : isString(names)\n              ? { [names]: defaultValue }\n              : defaultValue),\n      },\n      isGlobal,\n      defaultValue,\n    );\n\n  const _getFieldArray = <TFieldArrayValues>(\n    name: InternalFieldName,\n  ): Partial<TFieldArrayValues>[] =>\n    compact(\n      get(\n        _state.mount ? _formValues : _defaultValues,\n        name,\n        _options.shouldUnregister ? get(_defaultValues, name, []) : [],\n      ),\n    );\n\n  const setFieldValue = (\n    name: InternalFieldName,\n    value: SetFieldValue<TFieldValues>,\n    options: SetValueConfig = {},\n  ) => {\n    const field: Field = get(_fields, name);\n    let fieldValue: unknown = value;\n\n    if (field) {\n      const fieldReference = field._f;\n\n      if (fieldReference) {\n        !fieldReference.disabled &&\n          set(_formValues, name, getFieldValueAs(value, fieldReference));\n\n        fieldValue =\n          isHTMLElement(fieldReference.ref) && isNullOrUndefined(value)\n            ? ''\n            : value;\n\n        if (isMultipleSelect(fieldReference.ref)) {\n          [...fieldReference.ref.options].forEach(\n            (optionRef) =>\n              (optionRef.selected = (\n                fieldValue as InternalFieldName[]\n              ).includes(optionRef.value)),\n          );\n        } else if (fieldReference.refs) {\n          if (isCheckBoxInput(fieldReference.ref)) {\n            fieldReference.refs.forEach((checkboxRef) => {\n              if (!checkboxRef.defaultChecked || !checkboxRef.disabled) {\n                if (Array.isArray(fieldValue)) {\n                  checkboxRef.checked = !!fieldValue.find(\n                    (data: string) => data === checkboxRef.value,\n                  );\n                } else {\n                  checkboxRef.checked =\n                    fieldValue === checkboxRef.value || !!fieldValue;\n                }\n              }\n            });\n          } else {\n            fieldReference.refs.forEach(\n              (radioRef: HTMLInputElement) =>\n                (radioRef.checked = radioRef.value === fieldValue),\n            );\n          }\n        } else if (isFileInput(fieldReference.ref)) {\n          fieldReference.ref.value = '';\n        } else {\n          fieldReference.ref.value = fieldValue;\n\n          if (!fieldReference.ref.type) {\n            _subjects.state.next({\n              name,\n              values: cloneObject(_formValues),\n            });\n          }\n        }\n      }\n    }\n\n    (options.shouldDirty || options.shouldTouch) &&\n      updateTouchAndDirty(\n        name,\n        fieldValue,\n        options.shouldTouch,\n        options.shouldDirty,\n        true,\n      );\n\n    options.shouldValidate && trigger(name as Path<TFieldValues>);\n  };\n\n  const setValues = <\n    T extends InternalFieldName,\n    K extends SetFieldValue<TFieldValues>,\n    U extends SetValueConfig,\n  >(\n    name: T,\n    value: K,\n    options: U,\n  ) => {\n    for (const fieldKey in value) {\n      if (!value.hasOwnProperty(fieldKey)) {\n        return;\n      }\n      const fieldValue = value[fieldKey];\n      const fieldName = name + '.' + fieldKey;\n      const field = get(_fields, fieldName);\n\n      (_names.array.has(name) ||\n        isObject(fieldValue) ||\n        (field && !field._f)) &&\n      !isDateObject(fieldValue)\n        ? setValues(fieldName, fieldValue, options)\n        : setFieldValue(fieldName, fieldValue, options);\n    }\n  };\n\n  const setValue: UseFormSetValue<TFieldValues> = (\n    name,\n    value,\n    options = {},\n  ) => {\n    const field = get(_fields, name);\n    const isFieldArray = _names.array.has(name);\n    const cloneValue = cloneObject(value);\n\n    set(_formValues, name, cloneValue);\n\n    if (isFieldArray) {\n      _subjects.array.next({\n        name,\n        values: cloneObject(_formValues),\n      });\n\n      if (\n        (_proxyFormState.isDirty ||\n          _proxyFormState.dirtyFields ||\n          _proxySubscribeFormState.isDirty ||\n          _proxySubscribeFormState.dirtyFields) &&\n        options.shouldDirty\n      ) {\n        _subjects.state.next({\n          name,\n          dirtyFields: getDirtyFields(_defaultValues, _formValues),\n          isDirty: _getDirty(name, cloneValue),\n        });\n      }\n    } else {\n      field && !field._f && !isNullOrUndefined(cloneValue)\n        ? setValues(name, cloneValue, options)\n        : setFieldValue(name, cloneValue, options);\n    }\n\n    isWatched(name, _names) && _subjects.state.next({ ..._formState, name });\n    _subjects.state.next({\n      name: _state.mount ? name : undefined,\n      values: cloneObject(_formValues),\n    });\n  };\n\n  const onChange: ChangeHandler = async (event) => {\n    _state.mount = true;\n    const target = event.target;\n    let name: string = target.name;\n    let isFieldValueUpdated = true;\n    const field: Field = get(_fields, name);\n    const _updateIsFieldValueUpdated = (fieldValue: unknown) => {\n      isFieldValueUpdated =\n        Number.isNaN(fieldValue) ||\n        (isDateObject(fieldValue) && isNaN(fieldValue.getTime())) ||\n        deepEqual(fieldValue, get(_formValues, name, fieldValue));\n    };\n    const validationModeBeforeSubmit = getValidationModes(_options.mode);\n    const validationModeAfterSubmit = getValidationModes(\n      _options.reValidateMode,\n    );\n\n    if (field) {\n      let error;\n      let isValid;\n      const fieldValue = target.type\n        ? getFieldValue(field._f)\n        : getEventValue(event);\n      const isBlurEvent =\n        event.type === EVENTS.BLUR || event.type === EVENTS.FOCUS_OUT;\n      const shouldSkipValidation =\n        (!hasValidation(field._f) &&\n          !_options.resolver &&\n          !get(_formState.errors, name) &&\n          !field._f.deps) ||\n        skipValidation(\n          isBlurEvent,\n          get(_formState.touchedFields, name),\n          _formState.isSubmitted,\n          validationModeAfterSubmit,\n          validationModeBeforeSubmit,\n        );\n      const watched = isWatched(name, _names, isBlurEvent);\n\n      set(_formValues, name, fieldValue);\n\n      if (isBlurEvent) {\n        if (!target || !target.readOnly) {\n          field._f.onBlur && field._f.onBlur(event);\n          delayErrorCallback && delayErrorCallback(0);\n        }\n      } else if (field._f.onChange) {\n        field._f.onChange(event);\n      }\n\n      const fieldState = updateTouchAndDirty(name, fieldValue, isBlurEvent);\n\n      const shouldRender = !isEmptyObject(fieldState) || watched;\n\n      !isBlurEvent &&\n        _subjects.state.next({\n          name,\n          type: event.type,\n          values: cloneObject(_formValues),\n        });\n\n      if (shouldSkipValidation) {\n        if (_proxyFormState.isValid || _proxySubscribeFormState.isValid) {\n          if (_options.mode === 'onBlur') {\n            if (isBlurEvent) {\n              _setValid();\n            }\n          } else if (!isBlurEvent) {\n            _setValid();\n          }\n        }\n\n        return (\n          shouldRender &&\n          _subjects.state.next({ name, ...(watched ? {} : fieldState) })\n        );\n      }\n\n      !isBlurEvent && watched && _subjects.state.next({ ..._formState });\n\n      if (_options.resolver) {\n        const { errors } = await _runSchema([name]);\n\n        _updateIsFieldValueUpdated(fieldValue);\n\n        if (isFieldValueUpdated) {\n          const previousErrorLookupResult = schemaErrorLookup(\n            _formState.errors,\n            _fields,\n            name,\n          );\n          const errorLookupResult = schemaErrorLookup(\n            errors,\n            _fields,\n            previousErrorLookupResult.name || name,\n          );\n\n          error = errorLookupResult.error;\n          name = errorLookupResult.name;\n\n          isValid = isEmptyObject(errors);\n        }\n      } else {\n        _updateIsValidating([name], true);\n        error = (\n          await validateField(\n            field,\n            _names.disabled,\n            _formValues,\n            shouldDisplayAllAssociatedErrors,\n            _options.shouldUseNativeValidation,\n          )\n        )[name];\n        _updateIsValidating([name]);\n\n        _updateIsFieldValueUpdated(fieldValue);\n\n        if (isFieldValueUpdated) {\n          if (error) {\n            isValid = false;\n          } else if (\n            _proxyFormState.isValid ||\n            _proxySubscribeFormState.isValid\n          ) {\n            isValid = await executeBuiltInValidation(_fields, true);\n          }\n        }\n      }\n\n      if (isFieldValueUpdated) {\n        field._f.deps &&\n          trigger(\n            field._f.deps as\n              | FieldPath<TFieldValues>\n              | FieldPath<TFieldValues>[],\n          );\n        shouldRenderByError(name, isValid, error, fieldState);\n      }\n    }\n  };\n\n  const _focusInput = (ref: Ref, key: string) => {\n    if (get(_formState.errors, key) && ref.focus) {\n      ref.focus();\n      return 1;\n    }\n    return;\n  };\n\n  const trigger: UseFormTrigger<TFieldValues> = async (name, options = {}) => {\n    let isValid;\n    let validationResult;\n    const fieldNames = convertToArrayPayload(name) as InternalFieldName[];\n\n    if (_options.resolver) {\n      const errors = await executeSchemaAndUpdateState(\n        isUndefined(name) ? name : fieldNames,\n      );\n\n      isValid = isEmptyObject(errors);\n      validationResult = name\n        ? !fieldNames.some((name) => get(errors, name))\n        : isValid;\n    } else if (name) {\n      validationResult = (\n        await Promise.all(\n          fieldNames.map(async (fieldName) => {\n            const field = get(_fields, fieldName);\n            return await executeBuiltInValidation(\n              field && field._f ? { [fieldName]: field } : field,\n            );\n          }),\n        )\n      ).every(Boolean);\n      !(!validationResult && !_formState.isValid) && _setValid();\n    } else {\n      validationResult = isValid = await executeBuiltInValidation(_fields);\n    }\n\n    _subjects.state.next({\n      ...(!isString(name) ||\n      ((_proxyFormState.isValid || _proxySubscribeFormState.isValid) &&\n        isValid !== _formState.isValid)\n        ? {}\n        : { name }),\n      ...(_options.resolver || !name ? { isValid } : {}),\n      errors: _formState.errors,\n    });\n\n    options.shouldFocus &&\n      !validationResult &&\n      iterateFieldsByAction(\n        _fields,\n        _focusInput,\n        name ? fieldNames : _names.mount,\n      );\n\n    return validationResult;\n  };\n\n  const getValues: UseFormGetValues<TFieldValues> = (\n    fieldNames?:\n      | FieldPath<TFieldValues>\n      | ReadonlyArray<FieldPath<TFieldValues>>,\n  ) => {\n    const values = {\n      ...(_state.mount ? _formValues : _defaultValues),\n    };\n\n    return isUndefined(fieldNames)\n      ? values\n      : isString(fieldNames)\n        ? get(values, fieldNames)\n        : fieldNames.map((name) => get(values, name));\n  };\n\n  const getFieldState: UseFormGetFieldState<TFieldValues> = (\n    name,\n    formState,\n  ) => ({\n    invalid: !!get((formState || _formState).errors, name),\n    isDirty: !!get((formState || _formState).dirtyFields, name),\n    error: get((formState || _formState).errors, name),\n    isValidating: !!get(_formState.validatingFields, name),\n    isTouched: !!get((formState || _formState).touchedFields, name),\n  });\n\n  const clearErrors: UseFormClearErrors<TFieldValues> = (name) => {\n    name &&\n      convertToArrayPayload(name).forEach((inputName) =>\n        unset(_formState.errors, inputName),\n      );\n\n    _subjects.state.next({\n      errors: name ? _formState.errors : {},\n    });\n  };\n\n  const setError: UseFormSetError<TFieldValues> = (name, error, options) => {\n    const ref = (get(_fields, name, { _f: {} })._f || {}).ref;\n    const currentError = get(_formState.errors, name) || {};\n\n    // Don't override existing error messages elsewhere in the object tree.\n    const { ref: currentRef, message, type, ...restOfErrorTree } = currentError;\n\n    set(_formState.errors, name, {\n      ...restOfErrorTree,\n      ...error,\n      ref,\n    });\n\n    _subjects.state.next({\n      name,\n      errors: _formState.errors,\n      isValid: false,\n    });\n\n    options && options.shouldFocus && ref && ref.focus && ref.focus();\n  };\n\n  const watch: UseFormWatch<TFieldValues> = (\n    name?:\n      | FieldPath<TFieldValues>\n      | ReadonlyArray<FieldPath<TFieldValues>>\n      | WatchObserver<TFieldValues>,\n    defaultValue?: DeepPartial<TFieldValues>,\n  ) =>\n    isFunction(name)\n      ? _subjects.state.subscribe({\n          next: (payload) =>\n            'values' in payload &&\n            name(\n              _getWatch(undefined, defaultValue),\n              payload as {\n                name?: FieldPath<TFieldValues>;\n                type?: EventType;\n                value?: unknown;\n              },\n            ),\n        })\n      : _getWatch(\n          name as InternalFieldName | InternalFieldName[],\n          defaultValue,\n          true,\n        );\n\n  const _subscribe: FromSubscribe<TFieldValues> = (props) =>\n    _subjects.state.subscribe({\n      next: (\n        formState: Partial<FormState<TFieldValues>> & {\n          name?: InternalFieldName;\n          values?: TFieldValues | undefined;\n          type?: EventType;\n        },\n      ) => {\n        if (\n          shouldSubscribeByName(props.name, formState.name, props.exact) &&\n          shouldRenderFormState(\n            formState,\n            (props.formState as ReadFormState) || _proxyFormState,\n            _setFormState,\n            props.reRenderRoot,\n          )\n        ) {\n          props.callback({\n            values: { ..._formValues } as TFieldValues,\n            ..._formState,\n            ...formState,\n            defaultValues:\n              _defaultValues as FormState<TFieldValues>['defaultValues'],\n          });\n        }\n      },\n    }).unsubscribe;\n\n  const subscribe: UseFormSubscribe<TFieldValues> = (props) => {\n    _state.mount = true;\n    _proxySubscribeFormState = {\n      ..._proxySubscribeFormState,\n      ...props.formState,\n    };\n    return _subscribe({\n      ...props,\n      formState: _proxySubscribeFormState,\n    });\n  };\n\n  const unregister: UseFormUnregister<TFieldValues> = (name, options = {}) => {\n    for (const fieldName of name ? convertToArrayPayload(name) : _names.mount) {\n      _names.mount.delete(fieldName);\n      _names.array.delete(fieldName);\n\n      if (!options.keepValue) {\n        unset(_fields, fieldName);\n        unset(_formValues, fieldName);\n      }\n\n      !options.keepError && unset(_formState.errors, fieldName);\n      !options.keepDirty && unset(_formState.dirtyFields, fieldName);\n      !options.keepTouched && unset(_formState.touchedFields, fieldName);\n      !options.keepIsValidating &&\n        unset(_formState.validatingFields, fieldName);\n      !_options.shouldUnregister &&\n        !options.keepDefaultValue &&\n        unset(_defaultValues, fieldName);\n    }\n\n    _subjects.state.next({\n      values: cloneObject(_formValues),\n    });\n\n    _subjects.state.next({\n      ..._formState,\n      ...(!options.keepDirty ? {} : { isDirty: _getDirty() }),\n    });\n\n    !options.keepIsValid && _setValid();\n  };\n\n  const _setDisabledField: Control<TFieldValues>['_setDisabledField'] = ({\n    disabled,\n    name,\n  }) => {\n    if (\n      (isBoolean(disabled) && _state.mount) ||\n      !!disabled ||\n      _names.disabled.has(name)\n    ) {\n      disabled ? _names.disabled.add(name) : _names.disabled.delete(name);\n    }\n  };\n\n  const register: UseFormRegister<TFieldValues> = (name, options = {}) => {\n    let field = get(_fields, name);\n    const disabledIsDefined =\n      isBoolean(options.disabled) || isBoolean(_options.disabled);\n\n    set(_fields, name, {\n      ...(field || {}),\n      _f: {\n        ...(field && field._f ? field._f : { ref: { name } }),\n        name,\n        mount: true,\n        ...options,\n      },\n    });\n    _names.mount.add(name);\n\n    if (field) {\n      _setDisabledField({\n        disabled: isBoolean(options.disabled)\n          ? options.disabled\n          : _options.disabled,\n        name,\n      });\n    } else {\n      updateValidAndValue(name, true, options.value);\n    }\n\n    return {\n      ...(disabledIsDefined\n        ? { disabled: options.disabled || _options.disabled }\n        : {}),\n      ...(_options.progressive\n        ? {\n            required: !!options.required,\n            min: getRuleValue(options.min),\n            max: getRuleValue(options.max),\n            minLength: getRuleValue<number>(options.minLength) as number,\n            maxLength: getRuleValue(options.maxLength) as number,\n            pattern: getRuleValue(options.pattern) as string,\n          }\n        : {}),\n      name,\n      onChange,\n      onBlur: onChange,\n      ref: (ref: HTMLInputElement | null): void => {\n        if (ref) {\n          register(name, options);\n          field = get(_fields, name);\n\n          const fieldRef = isUndefined(ref.value)\n            ? ref.querySelectorAll\n              ? (ref.querySelectorAll('input,select,textarea')[0] as Ref) || ref\n              : ref\n            : ref;\n          const radioOrCheckbox = isRadioOrCheckbox(fieldRef);\n          const refs = field._f.refs || [];\n\n          if (\n            radioOrCheckbox\n              ? refs.find((option: Ref) => option === fieldRef)\n              : fieldRef === field._f.ref\n          ) {\n            return;\n          }\n\n          set(_fields, name, {\n            _f: {\n              ...field._f,\n              ...(radioOrCheckbox\n                ? {\n                    refs: [\n                      ...refs.filter(live),\n                      fieldRef,\n                      ...(Array.isArray(get(_defaultValues, name)) ? [{}] : []),\n                    ],\n                    ref: { type: fieldRef.type, name },\n                  }\n                : { ref: fieldRef }),\n            },\n          });\n\n          updateValidAndValue(name, false, undefined, fieldRef);\n        } else {\n          field = get(_fields, name, {});\n\n          if (field._f) {\n            field._f.mount = false;\n          }\n\n          (_options.shouldUnregister || options.shouldUnregister) &&\n            !(isNameInFieldArray(_names.array, name) && _state.action) &&\n            _names.unMount.add(name);\n        }\n      },\n    };\n  };\n\n  const _focusError = () =>\n    _options.shouldFocusError &&\n    iterateFieldsByAction(_fields, _focusInput, _names.mount);\n\n  const _disableForm = (disabled?: boolean) => {\n    if (isBoolean(disabled)) {\n      _subjects.state.next({ disabled });\n      iterateFieldsByAction(\n        _fields,\n        (ref, name) => {\n          const currentField: Field = get(_fields, name);\n          if (currentField) {\n            ref.disabled = currentField._f.disabled || disabled;\n\n            if (Array.isArray(currentField._f.refs)) {\n              currentField._f.refs.forEach((inputRef) => {\n                inputRef.disabled = currentField._f.disabled || disabled;\n              });\n            }\n          }\n        },\n        0,\n        false,\n      );\n    }\n  };\n\n  const handleSubmit: UseFormHandleSubmit<TFieldValues, TTransformedValues> =\n    (onValid, onInvalid) => async (e) => {\n      let onValidError = undefined;\n      if (e) {\n        e.preventDefault && e.preventDefault();\n        (e as React.BaseSyntheticEvent).persist &&\n          (e as React.BaseSyntheticEvent).persist();\n      }\n      let fieldValues: TFieldValues | TTransformedValues | {} =\n        cloneObject(_formValues);\n\n      _subjects.state.next({\n        isSubmitting: true,\n      });\n\n      if (_options.resolver) {\n        const { errors, values } = await _runSchema();\n        _formState.errors = errors;\n        fieldValues = cloneObject(values) as TFieldValues;\n      } else {\n        await executeBuiltInValidation(_fields);\n      }\n\n      if (_names.disabled.size) {\n        for (const name of _names.disabled) {\n          unset(fieldValues, name);\n        }\n      }\n\n      unset(_formState.errors, 'root');\n\n      if (isEmptyObject(_formState.errors)) {\n        _subjects.state.next({\n          errors: {},\n        });\n        try {\n          await onValid(fieldValues as TTransformedValues, e);\n        } catch (error) {\n          onValidError = error;\n        }\n      } else {\n        if (onInvalid) {\n          await onInvalid({ ..._formState.errors }, e);\n        }\n        _focusError();\n        setTimeout(_focusError);\n      }\n\n      _subjects.state.next({\n        isSubmitted: true,\n        isSubmitting: false,\n        isSubmitSuccessful: isEmptyObject(_formState.errors) && !onValidError,\n        submitCount: _formState.submitCount + 1,\n        errors: _formState.errors,\n      });\n      if (onValidError) {\n        throw onValidError;\n      }\n    };\n\n  const resetField: UseFormResetField<TFieldValues> = (name, options = {}) => {\n    if (get(_fields, name)) {\n      if (isUndefined(options.defaultValue)) {\n        setValue(name, cloneObject(get(_defaultValues, name)));\n      } else {\n        setValue(\n          name,\n          options.defaultValue as Parameters<typeof setValue<typeof name>>[1],\n        );\n        set(_defaultValues, name, cloneObject(options.defaultValue));\n      }\n\n      if (!options.keepTouched) {\n        unset(_formState.touchedFields, name);\n      }\n\n      if (!options.keepDirty) {\n        unset(_formState.dirtyFields, name);\n        _formState.isDirty = options.defaultValue\n          ? _getDirty(name, cloneObject(get(_defaultValues, name)))\n          : _getDirty();\n      }\n\n      if (!options.keepError) {\n        unset(_formState.errors, name);\n        _proxyFormState.isValid && _setValid();\n      }\n\n      _subjects.state.next({ ..._formState });\n    }\n  };\n\n  const _reset: UseFormReset<TFieldValues> = (\n    formValues,\n    keepStateOptions = {},\n  ) => {\n    const updatedValues = formValues ? cloneObject(formValues) : _defaultValues;\n    const cloneUpdatedValues = cloneObject(updatedValues);\n    const isEmptyResetValues = isEmptyObject(formValues);\n    const values = isEmptyResetValues ? _defaultValues : cloneUpdatedValues;\n\n    if (!keepStateOptions.keepDefaultValues) {\n      _defaultValues = updatedValues;\n    }\n\n    if (!keepStateOptions.keepValues) {\n      if (keepStateOptions.keepDirtyValues) {\n        const fieldsToCheck = new Set([\n          ..._names.mount,\n          ...Object.keys(getDirtyFields(_defaultValues, _formValues)),\n        ]);\n        for (const fieldName of Array.from(fieldsToCheck)) {\n          get(_formState.dirtyFields, fieldName)\n            ? set(values, fieldName, get(_formValues, fieldName))\n            : setValue(\n                fieldName as FieldPath<TFieldValues>,\n                get(values, fieldName),\n              );\n        }\n      } else {\n        if (isWeb && isUndefined(formValues)) {\n          for (const name of _names.mount) {\n            const field = get(_fields, name);\n            if (field && field._f) {\n              const fieldReference = Array.isArray(field._f.refs)\n                ? field._f.refs[0]\n                : field._f.ref;\n\n              if (isHTMLElement(fieldReference)) {\n                const form = fieldReference.closest('form');\n                if (form) {\n                  form.reset();\n                  break;\n                }\n              }\n            }\n          }\n        }\n\n        if (keepStateOptions.keepFieldsRef) {\n          for (const fieldName of _names.mount) {\n            setValue(\n              fieldName as FieldPath<TFieldValues>,\n              get(values, fieldName),\n            );\n          }\n        } else {\n          _fields = {};\n        }\n      }\n\n      _formValues = _options.shouldUnregister\n        ? keepStateOptions.keepDefaultValues\n          ? (cloneObject(_defaultValues) as TFieldValues)\n          : ({} as TFieldValues)\n        : (cloneObject(values) as TFieldValues);\n\n      _subjects.array.next({\n        values: { ...values },\n      });\n\n      _subjects.state.next({\n        values: { ...values } as TFieldValues,\n      });\n    }\n\n    _names = {\n      mount: keepStateOptions.keepDirtyValues ? _names.mount : new Set(),\n      unMount: new Set(),\n      array: new Set(),\n      disabled: new Set(),\n      watch: new Set(),\n      watchAll: false,\n      focus: '',\n    };\n\n    _state.mount =\n      !_proxyFormState.isValid ||\n      !!keepStateOptions.keepIsValid ||\n      !!keepStateOptions.keepDirtyValues;\n\n    _state.watch = !!_options.shouldUnregister;\n\n    _subjects.state.next({\n      submitCount: keepStateOptions.keepSubmitCount\n        ? _formState.submitCount\n        : 0,\n      isDirty: isEmptyResetValues\n        ? false\n        : keepStateOptions.keepDirty\n          ? _formState.isDirty\n          : !!(\n              keepStateOptions.keepDefaultValues &&\n              !deepEqual(formValues, _defaultValues)\n            ),\n      isSubmitted: keepStateOptions.keepIsSubmitted\n        ? _formState.isSubmitted\n        : false,\n      dirtyFields: isEmptyResetValues\n        ? {}\n        : keepStateOptions.keepDirtyValues\n          ? keepStateOptions.keepDefaultValues && _formValues\n            ? getDirtyFields(_defaultValues, _formValues)\n            : _formState.dirtyFields\n          : keepStateOptions.keepDefaultValues && formValues\n            ? getDirtyFields(_defaultValues, formValues)\n            : keepStateOptions.keepDirty\n              ? _formState.dirtyFields\n              : {},\n      touchedFields: keepStateOptions.keepTouched\n        ? _formState.touchedFields\n        : {},\n      errors: keepStateOptions.keepErrors ? _formState.errors : {},\n      isSubmitSuccessful: keepStateOptions.keepIsSubmitSuccessful\n        ? _formState.isSubmitSuccessful\n        : false,\n      isSubmitting: false,\n      defaultValues: _defaultValues as FormState<TFieldValues>['defaultValues'],\n    });\n  };\n\n  const reset: UseFormReset<TFieldValues> = (formValues, keepStateOptions) =>\n    _reset(\n      isFunction(formValues)\n        ? (formValues as Function)(_formValues as TFieldValues)\n        : formValues,\n      keepStateOptions,\n    );\n\n  const setFocus: UseFormSetFocus<TFieldValues> = (name, options = {}) => {\n    const field = get(_fields, name);\n    const fieldReference = field && field._f;\n\n    if (fieldReference) {\n      const fieldRef = fieldReference.refs\n        ? fieldReference.refs[0]\n        : fieldReference.ref;\n\n      if (fieldRef.focus) {\n        fieldRef.focus();\n        options.shouldSelect &&\n          isFunction(fieldRef.select) &&\n          fieldRef.select();\n      }\n    }\n  };\n\n  const _setFormState = (\n    updatedFormState: Partial<FormState<TFieldValues>>,\n  ) => {\n    _formState = {\n      ..._formState,\n      ...updatedFormState,\n    };\n  };\n\n  const _resetDefaultValues = () =>\n    isFunction(_options.defaultValues) &&\n    (_options.defaultValues as Function)().then((values: TFieldValues) => {\n      reset(values, _options.resetOptions);\n      _subjects.state.next({\n        isLoading: false,\n      });\n    });\n\n  const methods = {\n    control: {\n      register,\n      unregister,\n      getFieldState,\n      handleSubmit,\n      setError,\n      _subscribe,\n      _runSchema,\n      _focusError,\n      _getWatch,\n      _getDirty,\n      _setValid,\n      _setFieldArray,\n      _setDisabledField,\n      _setErrors,\n      _getFieldArray,\n      _reset,\n      _resetDefaultValues,\n      _removeUnmounted,\n      _disableForm,\n      _subjects,\n      _proxyFormState,\n      get _fields() {\n        return _fields;\n      },\n      get _formValues() {\n        return _formValues;\n      },\n      get _state() {\n        return _state;\n      },\n      set _state(value) {\n        _state = value;\n      },\n      get _defaultValues() {\n        return _defaultValues;\n      },\n      get _names() {\n        return _names;\n      },\n      set _names(value) {\n        _names = value;\n      },\n      get _formState() {\n        return _formState;\n      },\n      get _options() {\n        return _options;\n      },\n      set _options(value) {\n        _options = {\n          ..._options,\n          ...value,\n        };\n      },\n    },\n    subscribe,\n    trigger,\n    register,\n    handleSubmit,\n    watch,\n    setValue,\n    getValues,\n    reset,\n    resetField,\n    clearErrors,\n    unregister,\n    setError,\n    setFocus,\n    getFieldState,\n  };\n\n  return {\n    ...methods,\n    formControl: methods,\n  };\n}\n", "import type { Field } from '../types';\n\nexport default (options: Field['_f']) =>\n  options.mount &&\n  (options.required ||\n    options.min ||\n    options.max ||\n    options.maxLength ||\n    options.minLength ||\n    options.pattern ||\n    options.validate);\n", "import type { ValidationModeFlags } from '../types';\n\nexport default (\n  isBlurEvent: boolean,\n  isTouched: boolean,\n  isSubmitted: boolean,\n  reValidateMode: {\n    isOnBlur: boolean;\n    isOnChange: boolean;\n  },\n  mode: Partial<ValidationModeFlags>,\n) => {\n  if (mode.isOnAll) {\n    return false;\n  } else if (!isSubmitted && mode.isOnTouch) {\n    return !(isTouched || isBlurEvent);\n  } else if (isSubmitted ? reValidateMode.isOnBlur : mode.isOnBlur) {\n    return !isBlurEvent;\n  } else if (isSubmitted ? reValidateMode.isOnChange : mode.isOnChange) {\n    return isBlurEvent;\n  }\n  return true;\n};\n", "import convertToArrayPayload from '../utils/convertToArrayPayload';\n\nexport default <T extends string | readonly string[] | undefined>(\n  name?: T,\n  signalName?: string,\n  exact?: boolean,\n) =>\n  !name ||\n  !signalName ||\n  name === signalName ||\n  convertToArrayPayload(name).some(\n    (currentName) =>\n      currentName &&\n      (exact\n        ? currentName === signalName\n        : currentName.startsWith(signalName) ||\n          signalName.startsWith(currentName)),\n  );\n", "import type { FieldElement } from '../types';\n\nimport isCheckBoxInput from './isCheckBoxInput';\nimport isRadioInput from './isRadioInput';\n\nexport default (ref: FieldElement): ref is HTMLInputElement =>\n  isRadioInput(ref) || isCheckBoxInput(ref);\n", "import compact from '../utils/compact';\nimport get from '../utils/get';\nimport unset from '../utils/unset';\n\nexport default <T>(ref: T, name: string) =>\n  !compact(get(ref, name)).length && unset(ref, name);\n", "export default () => {\n  if (typeof crypto !== 'undefined' && crypto.randomUUID) {\n    return crypto.randomUUID();\n  }\n\n  const d =\n    typeof performance === 'undefined' ? Date.now() : performance.now() * 1000;\n\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\n    const r = (Math.random() * 16 + d) % 16 | 0;\n\n    return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16);\n  });\n};\n", "import type { FieldArrayMethodProps, InternalFieldName } from '../types';\nimport isUndefined from '../utils/isUndefined';\n\nexport default (\n  name: InternalFieldName,\n  index: number,\n  options: FieldArrayMethodProps = {},\n): string =>\n  options.shouldFocus || isUndefined(options.shouldFocus)\n    ? options.focusName ||\n      `${name}.${isUndefined(options.focusIndex) ? index : options.focusIndex}.`\n    : '';\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default <T>(data: T[], value: T | T[]): T[] => [\n  ...data,\n  ...convertToArrayPayload(value),\n];\n", "export default <T>(value: T | T[]): undefined[] | undefined =>\n  Array.isArray(value) ? value.map(() => undefined) : undefined;\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default function insert<T>(data: T[], index: number): (T | undefined)[];\nexport default function insert<T>(\n  data: T[],\n  index: number,\n  value: T | T[],\n): T[];\nexport default function insert<T>(\n  data: T[],\n  index: number,\n  value?: T | T[],\n): (T | undefined)[] {\n  return [\n    ...data.slice(0, index),\n    ...convertToArrayPayload(value),\n    ...data.slice(index),\n  ];\n}\n", "import isUndefined from './isUndefined';\n\nexport default <T>(\n  data: (T | undefined)[],\n  from: number,\n  to: number,\n): (T | undefined)[] => {\n  if (!Array.isArray(data)) {\n    return [];\n  }\n\n  if (isUndefined(data[to])) {\n    data[to] = undefined;\n  }\n  data.splice(to, 0, data.splice(from, 1)[0]);\n\n  return data;\n};\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default <T>(data: T[], value: T | T[]): T[] => [\n  ...convertToArrayPayload(value),\n  ...convertToArrayPayload(data),\n];\n", "import compact from './compact';\nimport convertToArrayPayload from './convertToArrayPayload';\nimport isUndefined from './isUndefined';\n\nfunction removeAtIndexes<T>(data: T[], indexes: number[]): T[] {\n  let i = 0;\n  const temp = [...data];\n\n  for (const index of indexes) {\n    temp.splice(index - i, 1);\n    i++;\n  }\n\n  return compact(temp).length ? temp : [];\n}\n\nexport default <T>(data: T[], index?: number | number[]): T[] =>\n  isUndefined(index)\n    ? []\n    : removeAtIndexes(\n        data,\n        (convertToArrayPayload(index) as number[]).sort((a, b) => a - b),\n      );\n", "export default <T>(data: T[], indexA: number, indexB: number): void => {\n  [data[indexA], data[indexB]] = [data[indexB], data[indexA]];\n};\n", "export default <T>(fieldValues: T[], index: number, value: T) => {\n  fieldValues[index] = value;\n  return fieldValues;\n};\n", "import React from 'react';\n\nimport generateId from './logic/generateId';\nimport getFocusFieldName from './logic/getFocusFieldName';\nimport getValidationModes from './logic/getValidationModes';\nimport isWatched from './logic/isWatched';\nimport iterateFieldsByAction from './logic/iterateFieldsByAction';\nimport updateFieldArrayRootError from './logic/updateFieldArrayRootError';\nimport validateField from './logic/validateField';\nimport appendAt from './utils/append';\nimport cloneObject from './utils/cloneObject';\nimport convertToArrayPayload from './utils/convertToArrayPayload';\nimport fillEmptyArray from './utils/fillEmptyArray';\nimport get from './utils/get';\nimport insertAt from './utils/insert';\nimport isEmptyObject from './utils/isEmptyObject';\nimport moveArrayAt from './utils/move';\nimport prependAt from './utils/prepend';\nimport removeArrayAt from './utils/remove';\nimport set from './utils/set';\nimport swapArrayAt from './utils/swap';\nimport unset from './utils/unset';\nimport updateAt from './utils/update';\nimport { VALIDATION_MODE } from './constants';\nimport type {\n  Control,\n  Field,\n  FieldArray,\n  FieldArrayMethodProps,\n  FieldArrayPath,\n  FieldArrayWithId,\n  FieldErrors,\n  FieldPath,\n  FieldValues,\n  FormState,\n  InternalFieldName,\n  RegisterOptions,\n  UseFieldArrayProps,\n  UseFieldArrayReturn,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect';\n\n/**\n * A custom hook that exposes convenient methods to perform operations with a list of dynamic inputs that need to be appended, updated, removed etc. • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn) • [Video](https://youtu.be/4MrbfGSFY2A)\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usefieldarray) • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn)\n *\n * @param props - useFieldArray props\n *\n * @returns methods - functions to manipulate with the Field Arrays (dynamic inputs) {@link UseFieldArrayReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, control, handleSubmit, reset, trigger, setError } = useForm({\n *     defaultValues: {\n *       test: []\n *     }\n *   });\n *   const { fields, append } = useFieldArray({\n *     control,\n *     name: \"test\"\n *   });\n *\n *   return (\n *     <form onSubmit={handleSubmit(data => console.log(data))}>\n *       {fields.map((item, index) => (\n *          <input key={item.id} {...register(`test.${index}.firstName`)}  />\n *       ))}\n *       <button type=\"button\" onClick={() => append({ firstName: \"bill\" })}>\n *         append\n *       </button>\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nexport function useFieldArray<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldArrayName extends\n    FieldArrayPath<TFieldValues> = FieldArrayPath<TFieldValues>,\n  TKeyName extends string = 'id',\n  TTransformedValues = TFieldValues,\n>(\n  props: UseFieldArrayProps<\n    TFieldValues,\n    TFieldArrayName,\n    TKeyName,\n    TTransformedValues\n  >,\n): UseFieldArrayReturn<TFieldValues, TFieldArrayName, TKeyName> {\n  const methods = useFormContext();\n  const {\n    control = methods.control,\n    name,\n    keyName = 'id',\n    shouldUnregister,\n    rules,\n  } = props;\n  const [fields, setFields] = React.useState(control._getFieldArray(name));\n  const ids = React.useRef<string[]>(\n    control._getFieldArray(name).map(generateId),\n  );\n  const _fieldIds = React.useRef(fields);\n  const _actioned = React.useRef(false);\n\n  _fieldIds.current = fields;\n  control._names.array.add(name);\n\n  React.useMemo(\n    () =>\n      rules &&\n      (control as Control<TFieldValues, any, TTransformedValues>).register(\n        name as FieldPath<TFieldValues>,\n        rules as RegisterOptions<TFieldValues>,\n      ),\n    [control, rules, name],\n  );\n\n  useIsomorphicLayoutEffect(\n    () =>\n      control._subjects.array.subscribe({\n        next: ({\n          values,\n          name: fieldArrayName,\n        }: {\n          values?: FieldValues;\n          name?: InternalFieldName;\n        }) => {\n          if (fieldArrayName === name || !fieldArrayName) {\n            const fieldValues = get(values, name);\n            if (Array.isArray(fieldValues)) {\n              setFields(fieldValues);\n              ids.current = fieldValues.map(generateId);\n            }\n          }\n        },\n      }).unsubscribe,\n    [control, name],\n  );\n\n  const updateValues = React.useCallback(\n    <\n      T extends Partial<\n        FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n      >[],\n    >(\n      updatedFieldArrayValues: T,\n    ) => {\n      _actioned.current = true;\n      control._setFieldArray(name, updatedFieldArrayValues);\n    },\n    [control, name],\n  );\n\n  const append = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const appendValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = appendAt(\n      control._getFieldArray(name),\n      appendValue,\n    );\n    control._names.focus = getFocusFieldName(\n      name,\n      updatedFieldArrayValues.length - 1,\n      options,\n    );\n    ids.current = appendAt(ids.current, appendValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, appendAt, {\n      argA: fillEmptyArray(value),\n    });\n  };\n\n  const prepend = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const prependValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = prependAt(\n      control._getFieldArray(name),\n      prependValue,\n    );\n    control._names.focus = getFocusFieldName(name, 0, options);\n    ids.current = prependAt(ids.current, prependValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, prependAt, {\n      argA: fillEmptyArray(value),\n    });\n  };\n\n  const remove = (index?: number | number[]) => {\n    const updatedFieldArrayValues: Partial<\n      FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n    >[] = removeArrayAt(control._getFieldArray(name), index);\n    ids.current = removeArrayAt(ids.current, index);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    !Array.isArray(get(control._fields, name)) &&\n      set(control._fields, name, undefined);\n    control._setFieldArray(name, updatedFieldArrayValues, removeArrayAt, {\n      argA: index,\n    });\n  };\n\n  const insert = (\n    index: number,\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const insertValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = insertAt(\n      control._getFieldArray(name),\n      index,\n      insertValue,\n    );\n    control._names.focus = getFocusFieldName(name, index, options);\n    ids.current = insertAt(ids.current, index, insertValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, insertAt, {\n      argA: index,\n      argB: fillEmptyArray(value),\n    });\n  };\n\n  const swap = (indexA: number, indexB: number) => {\n    const updatedFieldArrayValues = control._getFieldArray(name);\n    swapArrayAt(updatedFieldArrayValues, indexA, indexB);\n    swapArrayAt(ids.current, indexA, indexB);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(\n      name,\n      updatedFieldArrayValues,\n      swapArrayAt,\n      {\n        argA: indexA,\n        argB: indexB,\n      },\n      false,\n    );\n  };\n\n  const move = (from: number, to: number) => {\n    const updatedFieldArrayValues = control._getFieldArray(name);\n    moveArrayAt(updatedFieldArrayValues, from, to);\n    moveArrayAt(ids.current, from, to);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(\n      name,\n      updatedFieldArrayValues,\n      moveArrayAt,\n      {\n        argA: from,\n        argB: to,\n      },\n      false,\n    );\n  };\n\n  const update = (\n    index: number,\n    value: FieldArray<TFieldValues, TFieldArrayName>,\n  ) => {\n    const updateValue = cloneObject(value);\n    const updatedFieldArrayValues = updateAt(\n      control._getFieldArray<\n        FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n      >(name),\n      index,\n      updateValue as FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>,\n    );\n    ids.current = [...updatedFieldArrayValues].map((item, i) =>\n      !item || i === index ? generateId() : ids.current[i],\n    );\n    updateValues(updatedFieldArrayValues);\n    setFields([...updatedFieldArrayValues]);\n    control._setFieldArray(\n      name,\n      updatedFieldArrayValues,\n      updateAt,\n      {\n        argA: index,\n        argB: updateValue,\n      },\n      true,\n      false,\n    );\n  };\n\n  const replace = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n  ) => {\n    const updatedFieldArrayValues = convertToArrayPayload(cloneObject(value));\n    ids.current = updatedFieldArrayValues.map(generateId);\n    updateValues([...updatedFieldArrayValues]);\n    setFields([...updatedFieldArrayValues]);\n    control._setFieldArray(\n      name,\n      [...updatedFieldArrayValues],\n      <T>(data: T): T => data,\n      {},\n      true,\n      false,\n    );\n  };\n\n  React.useEffect(() => {\n    control._state.action = false;\n\n    isWatched(name, control._names) &&\n      control._subjects.state.next({\n        ...control._formState,\n      } as FormState<TFieldValues>);\n\n    if (\n      _actioned.current &&\n      (!getValidationModes(control._options.mode).isOnSubmit ||\n        control._formState.isSubmitted) &&\n      !getValidationModes(control._options.reValidateMode).isOnSubmit\n    ) {\n      if (control._options.resolver) {\n        control._runSchema([name]).then((result) => {\n          const error = get(result.errors, name);\n          const existingError = get(control._formState.errors, name);\n\n          if (\n            existingError\n              ? (!error && existingError.type) ||\n                (error &&\n                  (existingError.type !== error.type ||\n                    existingError.message !== error.message))\n              : error && error.type\n          ) {\n            error\n              ? set(control._formState.errors, name, error)\n              : unset(control._formState.errors, name);\n            control._subjects.state.next({\n              errors: control._formState.errors as FieldErrors<TFieldValues>,\n            });\n          }\n        });\n      } else {\n        const field: Field = get(control._fields, name);\n        if (\n          field &&\n          field._f &&\n          !(\n            getValidationModes(control._options.reValidateMode).isOnSubmit &&\n            getValidationModes(control._options.mode).isOnSubmit\n          )\n        ) {\n          validateField(\n            field,\n            control._names.disabled,\n            control._formValues,\n            control._options.criteriaMode === VALIDATION_MODE.all,\n            control._options.shouldUseNativeValidation,\n            true,\n          ).then(\n            (error) =>\n              !isEmptyObject(error) &&\n              control._subjects.state.next({\n                errors: updateFieldArrayRootError(\n                  control._formState.errors as FieldErrors<TFieldValues>,\n                  error,\n                  name,\n                ) as FieldErrors<TFieldValues>,\n              }),\n          );\n        }\n      }\n    }\n\n    control._subjects.state.next({\n      name,\n      values: cloneObject(control._formValues) as TFieldValues,\n    });\n\n    control._names.focus &&\n      iterateFieldsByAction(control._fields, (ref, key: string) => {\n        if (\n          control._names.focus &&\n          key.startsWith(control._names.focus) &&\n          ref.focus\n        ) {\n          ref.focus();\n          return 1;\n        }\n        return;\n      });\n\n    control._names.focus = '';\n\n    control._setValid();\n    _actioned.current = false;\n  }, [fields, name, control]);\n\n  React.useEffect(() => {\n    !get(control._formValues, name) && control._setFieldArray(name);\n\n    return () => {\n      const updateMounted = (name: InternalFieldName, value: boolean) => {\n        const field: Field = get(control._fields, name);\n        if (field && field._f) {\n          field._f.mount = value;\n        }\n      };\n\n      control._options.shouldUnregister || shouldUnregister\n        ? control.unregister(name as FieldPath<TFieldValues>)\n        : updateMounted(name, false);\n    };\n  }, [name, control, keyName, shouldUnregister]);\n\n  return {\n    swap: React.useCallback(swap, [updateValues, name, control]),\n    move: React.useCallback(move, [updateValues, name, control]),\n    prepend: React.useCallback(prepend, [updateValues, name, control]),\n    append: React.useCallback(append, [updateValues, name, control]),\n    remove: React.useCallback(remove, [updateValues, name, control]),\n    insert: React.useCallback(insert, [updateValues, name, control]),\n    update: React.useCallback(update, [updateValues, name, control]),\n    replace: React.useCallback(replace, [updateValues, name, control]),\n    fields: React.useMemo(\n      () =>\n        fields.map((field, index) => ({\n          ...field,\n          [keyName]: ids.current[index] || generateId(),\n        })) as FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>[],\n      [fields, keyName],\n    ),\n  };\n}\n", "import React from 'react';\n\nimport getProxyFormState from './logic/getProxyFormState';\nimport deepEqual from './utils/deepEqual';\nimport isFunction from './utils/isFunction';\nimport { createFormControl } from './logic';\nimport type {\n  FieldValues,\n  FormState,\n  UseFormProps,\n  UseFormReturn,\n} from './types';\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect';\n\n/**\n * Custom hook to manage the entire form.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useform) • [Demo](https://codesandbox.io/s/react-hook-form-get-started-ts-5ksmm) • [Video](https://www.youtube.com/watch?v=RkXv4AXXC_4)\n *\n * @param props - form configuration and validation parameters.\n *\n * @returns methods - individual functions to manage the form state. {@link UseFormReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, watch, formState: { errors } } = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   console.log(watch(\"example\"));\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input defaultValue=\"test\" {...register(\"example\")} />\n *       <input {...register(\"exampleRequired\", { required: true })} />\n *       {errors.exampleRequired && <span>This field is required</span>}\n *       <button>Submit</button>\n *     </form>\n *   );\n * }\n * ```\n */\nexport function useForm<\n  TFieldValues extends FieldValues = FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(\n  props: UseFormProps<TFieldValues, TContext, TTransformedValues> = {},\n): UseFormReturn<TFieldValues, TContext, TTransformedValues> {\n  const _formControl = React.useRef<\n    UseFormReturn<TFieldValues, TContext, TTransformedValues> | undefined\n  >(undefined);\n  const _values = React.useRef<typeof props.values>(undefined);\n  const [formState, updateFormState] = React.useState<FormState<TFieldValues>>({\n    isDirty: false,\n    isValidating: false,\n    isLoading: isFunction(props.defaultValues),\n    isSubmitted: false,\n    isSubmitting: false,\n    isSubmitSuccessful: false,\n    isValid: false,\n    submitCount: 0,\n    dirtyFields: {},\n    touchedFields: {},\n    validatingFields: {},\n    errors: props.errors || {},\n    disabled: props.disabled || false,\n    isReady: false,\n    defaultValues: isFunction(props.defaultValues)\n      ? undefined\n      : props.defaultValues,\n  });\n\n  if (!_formControl.current) {\n    if (props.formControl) {\n      _formControl.current = {\n        ...props.formControl,\n        formState,\n      };\n\n      if (props.defaultValues && !isFunction(props.defaultValues)) {\n        props.formControl.reset(props.defaultValues, props.resetOptions);\n      }\n    } else {\n      const { formControl, ...rest } = createFormControl(props);\n\n      _formControl.current = {\n        ...rest,\n        formState,\n      };\n    }\n  }\n\n  const control = _formControl.current.control;\n  control._options = props;\n\n  useIsomorphicLayoutEffect(() => {\n    const sub = control._subscribe({\n      formState: control._proxyFormState,\n      callback: () => updateFormState({ ...control._formState }),\n      reRenderRoot: true,\n    });\n\n    updateFormState((data) => ({\n      ...data,\n      isReady: true,\n    }));\n\n    control._formState.isReady = true;\n\n    return sub;\n  }, [control]);\n\n  React.useEffect(\n    () => control._disableForm(props.disabled),\n    [control, props.disabled],\n  );\n\n  React.useEffect(() => {\n    if (props.mode) {\n      control._options.mode = props.mode;\n    }\n    if (props.reValidateMode) {\n      control._options.reValidateMode = props.reValidateMode;\n    }\n  }, [control, props.mode, props.reValidateMode]);\n\n  React.useEffect(() => {\n    if (props.errors) {\n      control._setErrors(props.errors);\n      control._focusError();\n    }\n  }, [control, props.errors]);\n\n  React.useEffect(() => {\n    props.shouldUnregister &&\n      control._subjects.state.next({\n        values: control._getWatch(),\n      });\n  }, [control, props.shouldUnregister]);\n\n  React.useEffect(() => {\n    if (control._proxyFormState.isDirty) {\n      const isDirty = control._getDirty();\n      if (isDirty !== formState.isDirty) {\n        control._subjects.state.next({\n          isDirty,\n        });\n      }\n    }\n  }, [control, formState.isDirty]);\n\n  React.useEffect(() => {\n    if (props.values && !deepEqual(props.values, _values.current)) {\n      control._reset(props.values, {\n        keepFieldsRef: true,\n        ...control._options.resetOptions,\n      });\n      _values.current = props.values;\n      updateFormState((state) => ({ ...state }));\n    } else {\n      control._resetDefaultValues();\n    }\n  }, [control, props.values]);\n\n  React.useEffect(() => {\n    if (!control._state.mount) {\n      control._setValid();\n      control._state.mount = true;\n    }\n\n    if (control._state.watch) {\n      control._state.watch = false;\n      control._subjects.state.next({ ...control._formState });\n    }\n\n    control._removeUnmounted();\n  });\n\n  _formControl.current.formState = getProxyFormState(formState, control);\n\n  return _formControl.current;\n}\n"], "names": ["isCheckBoxInput", "element", "type", "isDateObject", "value", "Date", "isNullOrUndefined", "isObjectType", "isObject", "Array", "isArray", "getEventValue", "event", "target", "checked", "isNameInFieldArray", "names", "name", "has", "substring", "search", "getNodeParentName", "isWeb", "window", "HTMLElement", "document", "cloneObject", "data", "copy", "isFileListInstance", "FileList", "Blob", "Object", "create", "getPrototypeOf", "tempObject", "prototypeCopy", "constructor", "prototype", "hasOwnProperty", "isPlainObject", "key", "is<PERSON>ey", "test", "isUndefined", "val", "undefined", "compact", "filter", "Boolean", "stringToPath", "input", "replace", "split", "get", "object", "path", "defaultValue", "result", "reduce", "isBoolean", "set", "index", "temp<PERSON>ath", "length", "lastIndex", "newValue", "objValue", "isNaN", "EVENTS", "VALIDATION_MODE", "INPUT_VALIDATION_RULES", "HookFormContext", "React", "createContext", "displayName", "useFormContext", "useContext", "getProxyFormState", "formState", "control", "localProxyFormState", "isRoot", "defaultValues", "_defaultValues", "defineProperty", "_key", "_proxyFormState", "useIsomorphicLayoutEffect", "useLayoutEffect", "useEffect", "useFormState", "props", "methods", "disabled", "exact", "updateFormState", "useState", "_formState", "_localProxyFormState", "useRef", "isDirty", "isLoading", "dirtyFields", "touchedFields", "validatingFields", "isValidating", "<PERSON><PERSON><PERSON><PERSON>", "errors", "_subscribe", "current", "callback", "_setValid", "useMemo", "isString", "generateWatchOutput", "_names", "formValues", "isGlobal", "watch", "add", "map", "fieldName", "watchAll", "isPrimitive", "deepEqual", "object1", "object2", "_internal_visited", "WeakSet", "getTime", "keys1", "keys", "keys2", "val1", "includes", "val2", "useWatch", "compute", "_defaultValue", "_compute", "_computeFormValues", "defaultValueMemo", "_getWatch", "updateValue", "values", "_formValues", "computedFormValues", "_removeUnmounted", "useController", "shouldUnregister", "isArrayField", "array", "_props", "_registerProps", "register", "rules", "fieldState", "defineProperties", "invalid", "enumerable", "isTouched", "error", "onChange", "useCallback", "onBlur", "ref", "elm", "field", "_fields", "_f", "focus", "select", "setCustomValidity", "message", "reportValidity", "_shouldUnregisterField", "_options", "updateMounted", "mount", "_state", "action", "unregister", "_setDisabledField", "flatten", "obj", "output", "nested", "nested<PERSON><PERSON>", "POST_REQUEST", "appendErrors", "validateAllFieldCriteria", "types", "convertToArrayPayload", "createSubject", "_observers", "observers", "next", "observer", "subscribe", "push", "unsubscribe", "o", "isEmptyObject", "isFileInput", "isFunction", "isHTMLElement", "owner", "ownerDocument", "defaultView", "isMultipleSelect", "isRadioInput", "live", "isConnected", "unset", "paths", "childObject", "updatePath", "slice", "baseGet", "isEmptyArray", "objectHasFunction", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fields", "isParentNodeArray", "getDirtyFieldsFromDefaultValues", "dirtyField<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getDirty<PERSON>ields", "defaultResult", "validResult", "getCheckboxValue", "options", "option", "attributes", "getFieldValueAs", "valueAsNumber", "valueAsDate", "setValueAs", "NaN", "defaultReturn", "getRadioValue", "previous", "getFieldValue", "files", "refs", "selectedOptions", "isCheckBox", "isRegex", "RegExp", "getRuleValue", "rule", "source", "getValidationModes", "mode", "isOnSubmit", "isOnBlur", "isOnChange", "isOnAll", "isOnTouch", "ASYNC_FUNCTION", "hasPromiseValidation", "fieldReference", "validate", "find", "validateFunction", "isWatched", "isBlurEvent", "some", "watchName", "startsWith", "iterateFieldsByAction", "fieldsNames", "abort<PERSON><PERSON><PERSON>", "current<PERSON><PERSON>", "schemaErrorLookup", "join", "found<PERSON><PERSON>r", "root", "pop", "updateFieldArrayRootError", "fieldArrayErrors", "isMessage", "getValidateError", "every", "getValueAndMessage", "validationData", "validateField", "async", "disabled<PERSON>ieldN<PERSON>s", "shouldUseNativeValidation", "isFieldArray", "required", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "min", "max", "pattern", "inputValue", "inputRef", "isRadio", "isRadioOrCheckbox", "isEmpty", "appendErrors<PERSON><PERSON><PERSON>", "bind", "getMinMaxMessage", "exceedMax", "maxLengthMessage", "minLengthMessage", "maxType", "minType", "exceedMin", "maxOutput", "minOutput", "valueDate", "convertTimeToDate", "time", "toDateString", "isTime", "isWeek", "valueNumber", "maxLengthOutput", "minLengthOutput", "patternValue", "match", "validateError", "validationResult", "defaultOptions", "reValidateMode", "shouldFocusError", "createFormControl", "delayError<PERSON><PERSON><PERSON>", "submitCount", "isReady", "isSubmitted", "isSubmitting", "isSubmitSuccessful", "Set", "unMount", "timer", "_proxySubscribeFormState", "_subjects", "state", "shouldDisplayAllAssociatedErrors", "criteriaMode", "shouldUpdateValid", "resolver", "_runSchema", "executeBuiltInValidation", "_updateIsValidating", "from", "for<PERSON>ach", "updateValidAndValue", "shouldSkipSetValueAs", "defaultChecked", "setFieldValue", "updateTouchAndDirty", "fieldValue", "should<PERSON>irty", "shouldRender", "shouldUpdateField", "is<PERSON>revious<PERSON><PERSON>y", "_getDirty", "isCurrentFieldPristine", "isPreviousFieldTouched", "shouldRenderByError", "previousFieldError", "delayError", "updateErrors", "wait", "clearTimeout", "setTimeout", "updatedFormState", "context", "getResolverOptions", "should<PERSON>nly<PERSON><PERSON><PERSON><PERSON>d", "valid", "isFieldArrayRoot", "isPromiseFunction", "fieldError", "getV<PERSON>ues", "optionRef", "selected", "checkboxRef", "radioRef", "shouldTouch", "shouldValidate", "trigger", "set<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "setValue", "cloneValue", "isFieldValueUpdated", "_updateIsFieldValueUpdated", "Number", "validationModeBeforeSubmit", "validationModeAfterSubmit", "shouldSkipValidation", "deps", "skipValidation", "watched", "readOnly", "previousErrorLookupResult", "errorLookupResult", "_focusInput", "fieldNames", "executeSchemaAndUpdateState", "Promise", "all", "shouldFocus", "getFieldState", "setError", "currentError", "currentRef", "restOfErrorTree", "signalName", "currentName", "formStateData", "shouldRenderFormState", "_setFormState", "reRenderRoot", "delete", "keepValue", "keepError", "keep<PERSON>irty", "keepTouched", "keepIsValidating", "keepDefaultValue", "keepIsValid", "disabledIsDefined", "progressive", "fieldRef", "querySelectorAll", "radioOrCheckbox", "_focusError", "handleSubmit", "onValid", "onInvalid", "e", "onValidError", "preventDefault", "persist", "field<PERSON><PERSON><PERSON>", "size", "_reset", "keepStateOptions", "updatedValues", "cloneUpdatedValues", "isEmptyResetValues", "keepDefaultValues", "keepV<PERSON>ues", "keepDirtyV<PERSON>ues", "fieldsToCheck", "form", "closest", "reset", "keepFieldsRef", "keepSubmitCount", "keepIsSubmitted", "keepErrors", "keepIsSubmitSuccessful", "_setFieldArray", "method", "args", "shouldSetValues", "shouldUpdateFieldsAndState", "argA", "argB", "unsetEmptyArray", "_setErrors", "_getFieldArray", "_resetDefaultValues", "then", "resetOptions", "_disableForm", "payload", "reset<PERSON>ield", "clearErrors", "inputName", "setFocus", "shouldSelect", "formControl", "generateId", "crypto", "randomUUID", "d", "performance", "now", "c", "r", "Math", "random", "toString", "getFocusFieldName", "focusName", "focusIndex", "appendAt", "fillEmptyArray", "insert", "moveArrayAt", "to", "splice", "prependAt", "removeArrayAt", "indexes", "i", "temp", "removeAtIndexes", "sort", "a", "b", "swapArrayAt", "indexA", "indexB", "updateAt", "render", "mounted", "setMounted", "onSubmit", "children", "headers", "encType", "onError", "onSuccess", "validateStatus", "rest", "submit", "<PERSON><PERSON><PERSON><PERSON>", "formData", "FormData", "formDataJson", "JSON", "stringify", "_a", "flattenForm<PERSON><PERSON>ues", "append", "shouldStringifySubmissionData", "response", "fetch", "String", "body", "status", "createElement", "Fragment", "noValidate", "Provider", "keyName", "setFields", "ids", "_fieldIds", "_actioned", "fieldArrayName", "updateValues", "updatedFieldArrayValues", "existingError", "swap", "move", "prepend", "prependValue", "appendValue", "remove", "insertValue", "insertAt", "update", "item", "_formControl", "_values", "sub"], "mappings": "oCAEAA,EAAgBC,GACG,aAAjBA,EAAQC,KCHVC,EAAgBC,GAAkCA,aAAiBC,KCAnEC,EAAgBF,GAAuD,MAATA,ECGvD,MAAMG,EAAgBH,GACV,iBAAVA,EAET,IAAAI,EAAkCJ,IAC/BE,EAAkBF,KAClBK,MAAMC,QAAQN,IACfG,EAAaH,KACZD,EAAaC,GCLhBO,EAAgBC,GACdJ,EAASI,IAAWA,EAAgBC,OAChCb,EAAiBY,EAAgBC,QAC9BD,EAAgBC,OAAOC,QACvBF,EAAgBC,OAAOT,MAC1BQ,ECNNG,EAAe,CAACC,EAA+BC,IAC7CD,EAAME,ICLO,CAACD,GACdA,EAAKE,UAAU,EAAGF,EAAKG,OAAO,iBAAmBH,EDIvCI,CAAkBJ,IEL9BK,EAAiC,oBAAXC,aACU,IAAvBA,OAAOC,aACM,oBAAbC,SCEK,SAAUC,EAAeC,GACrC,IAAIC,EACJ,MAAMlB,EAAUD,MAAMC,QAAQiB,GACxBE,EACgB,oBAAbC,UAA2BH,aAAgBG,SAEpD,GAAIH,aAAgBtB,KAClBuB,EAAO,IAAIvB,KAAKsB,OACX,IACHL,IAAUK,aAAgBI,MAAQF,KACnCnB,IAAWF,EAASmB,GAcrB,OAAOA,EAVP,GAFAC,EAAOlB,EAAU,GAAKsB,OAAOC,OAAOD,OAAOE,eAAeP,IAErDjB,GChBM,CAACyB,IACd,MAAMC,EACJD,EAAWE,aAAeF,EAAWE,YAAYC,UAEnD,OACE9B,EAAS4B,IAAkBA,EAAcG,eAAe,kBDWvCC,CAAcb,GAG7B,IAAK,MAAMc,KAAOd,EACZA,EAAKY,eAAeE,KACtBb,EAAKa,GAAOf,EAAYC,EAAKc,UAJjCb,EAAOD,EAYX,OAAOC,CACT,CEhCA,IAAAc,EAAgBtC,GAAkB,QAAQuC,KAAKvC,GCA/CwC,EAAgBC,QAA2CC,IAARD,ECAnDE,EAAwB3C,GACtBK,MAAMC,QAAQN,GAASA,EAAM4C,OAAOC,SAAW,GCCjDC,EAAgBC,GACdJ,EAAQI,EAAMC,QAAQ,YAAa,IAAIC,MAAM,UCG/CC,EAAe,CACbC,EACAC,EACAC,KAEA,IAAKD,IAAShD,EAAS+C,GACrB,OAAOE,EAGT,MAAMC,GAAUhB,EAAMc,GAAQ,CAACA,GAAQN,EAAaM,IAAOG,OACzD,CAACD,EAAQjB,IACPnC,EAAkBoD,GAAUA,EAASA,EAAOjB,GAC9Cc,GAGF,OAAOX,EAAYc,IAAWA,IAAWH,EACrCX,EAAYW,EAAOC,IACjBC,EACAF,EAAOC,GACTE,GCzBNE,EAAgBxD,GAAsD,kBAAVA,ECM5DyD,EAAe,CACbN,EACAC,EACApD,KAEA,IAAI0D,GAAQ,EACZ,MAAMC,EAAWrB,EAAMc,GAAQ,CAACA,GAAQN,EAAaM,GAC/CQ,EAASD,EAASC,OAClBC,EAAYD,EAAS,EAE3B,OAASF,EAAQE,GAAQ,CACvB,MAAMvB,EAAMsB,EAASD,GACrB,IAAII,EAAW9D,EAEf,GAAI0D,IAAUG,EAAW,CACvB,MAAME,EAAWZ,EAAOd,GACxByB,EACE1D,EAAS2D,IAAa1D,MAAMC,QAAQyD,GAChCA,EACCC,OAAOL,EAASD,EAAQ,IAEvB,CAAA,EADA,GAIV,GAAY,cAARrB,GAA+B,gBAARA,GAAiC,cAARA,EAClD,OAGFc,EAAOd,GAAOyB,EACdX,EAASA,EAAOd,KCnCb,MAAM4B,EACL,OADKA,EAEA,WAFAA,EAGH,SAGGC,EACH,SADGA,EAED,WAFCA,EAGD,WAHCA,EAIA,YAJAA,EAKN,MAGMC,EACN,MADMA,EAEN,MAFMA,EAGA,YAHAA,EAIA,YAJAA,EAKF,UALEA,EAMD,WANCA,EAOD,WCjBNC,EAAkBC,EAAMC,cAAoC,MAClEF,EAAgBG,YAAc,kBAgCvB,MAAMC,EAAiB,IAK5BH,EAAMI,WAAWL,GCvCnB,IAAAM,EAAe,CAKbC,EACAC,EACAC,EACAC,GAAS,KAET,MAAMxB,EAAS,CACbyB,cAAeH,EAAQI,gBAGzB,IAAK,MAAM3C,KAAOsC,EAChB/C,OAAOqD,eAAe3B,EAAQjB,EAAK,CACjCa,IAAK,KACH,MAAMgC,EAAO7C,EAOb,OALIuC,EAAQO,gBAAgBD,KAAUhB,IACpCU,EAAQO,gBAAgBD,IAASJ,GAAUZ,GAG7CW,IAAwBA,EAAoBK,IAAQ,GAC7CP,EAAUO,MAKvB,OAAO5B,GC9BF,MAAM8B,EACO,oBAAXjE,OAAyBkD,EAAMgB,gBAAkBhB,EAAMiB,UCsC1D,SAAUC,EAIdC,GAEA,MAAMC,EAAUjB,KACVI,QAAEA,EAAUa,EAAQb,QAAOc,SAAEA,EAAQ7E,KAAEA,EAAI8E,MAAEA,GAAUH,GAAS,CAAA,GAC/Db,EAAWiB,GAAmBvB,EAAMwB,SAASjB,EAAQkB,YACtDC,EAAuB1B,EAAM2B,OAAO,CACxCC,SAAS,EACTC,WAAW,EACXC,aAAa,EACbC,eAAe,EACfC,kBAAkB,EAClBC,cAAc,EACdC,SAAS,EACTC,QAAQ,IAwBV,OArBApB,EACE,IACER,EAAQ6B,WAAW,CACjB5F,OACA8D,UAAWoB,EAAqBW,QAChCf,QACAgB,SAAWhC,KACRe,GACCE,EAAgB,IACXhB,EAAQkB,cACRnB,OAIb,CAAC9D,EAAM6E,EAAUC,IAGnBtB,EAAMiB,UAAU,KACdS,EAAqBW,QAAQH,SAAW3B,EAAQgC,WAAU,IACzD,CAAChC,IAEGP,EAAMwC,QACX,IACEnC,EACEC,EACAC,EACAmB,EAAqBW,SACrB,GAEJ,CAAC/B,EAAWC,GAEhB,CC5FA,IAAAkC,EAAgB9G,GAAqD,iBAAVA,ECI3D+G,EAAe,CACbnG,EACAoG,EACAC,EACAC,EACA7D,IAEIyD,EAASlG,IACXsG,GAAYF,EAAOG,MAAMC,IAAIxG,GACtBsC,EAAI+D,EAAYrG,EAAOyC,IAG5BhD,MAAMC,QAAQM,GACTA,EAAMyG,IACVC,IACCJ,GAAYF,EAAOG,MAAMC,IAAIE,GAC7BpE,EAAI+D,EAAYK,MAKtBJ,IAAaF,EAAOO,UAAW,GAExBN,GCtBTO,EAAgBxH,GACdE,EAAkBF,KAAWG,EAAaH,GCD9B,SAAUyH,EACtBC,EACAC,EACAC,EAAoB,IAAIC,SAExB,GAAIL,EAAYE,IAAYF,EAAYG,GACtC,OAAOD,IAAYC,EAGrB,GAAI5H,EAAa2H,IAAY3H,EAAa4H,GACxC,OAAOD,EAAQI,YAAcH,EAAQG,UAGvC,MAAMC,EAAQnG,OAAOoG,KAAKN,GACpBO,EAAQrG,OAAOoG,KAAKL,GAE1B,GAAII,EAAMnE,SAAWqE,EAAMrE,OACzB,OAAO,EAGT,GAAIgE,EAAkB9G,IAAI4G,IAAYE,EAAkB9G,IAAI6G,GAC1D,OAAO,EAETC,EAAkBR,IAAIM,GACtBE,EAAkBR,IAAIO,GAEtB,IAAK,MAAMtF,KAAO0F,EAAO,CACvB,MAAMG,EAAOR,EAAQrF,GAErB,IAAK4F,EAAME,SAAS9F,GAClB,OAAO,EAGT,GAAY,QAARA,EAAe,CACjB,MAAM+F,EAAOT,EAAQtF,GAErB,GACGtC,EAAamI,IAASnI,EAAaqI,IACnChI,EAAS8H,IAAS9H,EAASgI,IAC3B/H,MAAMC,QAAQ4H,IAAS7H,MAAMC,QAAQ8H,IACjCX,EAAUS,EAAME,EAAMR,GACvBM,IAASE,EAEb,OAAO,GAKb,OAAO,CACT,CCyMM,SAAUC,EACd7C,GAEA,MAAMC,EAAUjB,KACVI,QACJA,EAAUa,EAAQb,QAAO/D,KACzBA,EAAIwC,aACJA,EAAYqC,SACZA,EAAQC,MACRA,EAAK2C,QACLA,GACE9C,GAAS,CAAA,EACP+C,EAAgBlE,EAAM2B,OAAO3C,GAC7BmF,EAAWnE,EAAM2B,OAAOsC,GACxBG,EAAqBpE,EAAM2B,YAAOtD,GAExC8F,EAAS9B,QAAU4B,EAEnB,MAAMI,EAAmBrE,EAAMwC,QAC7B,IACEjC,EAAQ+D,UACN9H,EACA0H,EAAc7B,SAElB,CAAC9B,EAAS/D,KAGLb,EAAO4I,GAAevE,EAAMwB,SACjC2C,EAAS9B,QAAU8B,EAAS9B,QAAQgC,GAAoBA,GAuC1D,OApCAtD,EACE,IACER,EAAQ6B,WAAW,CACjB5F,OACA8D,UAAW,CACTkE,QAAQ,GAEVlD,QACAgB,SAAWhC,IACT,IAAKe,EAAU,CACb,MAAMuB,EAAaF,EACjBlG,EACA+D,EAAQoC,OACRrC,EAAUkE,QAAUjE,EAAQkE,aAC5B,EACAP,EAAc7B,SAGhB,GAAI8B,EAAS9B,QAAS,CACpB,MAAMqC,EAAqBP,EAAS9B,QAAQO,GAEvCQ,EAAUsB,EAAoBN,EAAmB/B,WACpDkC,EAAYG,GACZN,EAAmB/B,QAAUqC,QAG/BH,EAAY3B,OAKtB,CAACrC,EAASc,EAAU7E,EAAM8E,IAG5BtB,EAAMiB,UAAU,IAAMV,EAAQoE,oBAEvBhJ,CACT,CCnRM,SAAUiJ,EAKdzD,GAEA,MAAMC,EAAUjB,KACV3D,KACJA,EAAI6E,SACJA,EAAQd,QACRA,EAAUa,EAAQb,QAAOsE,iBACzBA,EAAgB7F,aAChBA,GACEmC,EACE2D,EAAexI,EAAmBiE,EAAQoC,OAAOoC,MAAOvI,GAExD6H,EAAmBrE,EAAMwC,QAC7B,IACE3D,EACE0B,EAAQkE,YACRjI,EACAqC,EAAI0B,EAAQI,eAAgBnE,EAAMwC,IAEtC,CAACuB,EAAS/D,EAAMwC,IAGZrD,EAAQqI,EAAS,CACrBzD,UACA/D,OACAwC,aAAcqF,EACd/C,OAAO,IAGHhB,EAAYY,EAAa,CAC7BX,UACA/D,OACA8E,OAAO,IAGH0D,EAAShF,EAAM2B,OAAOR,GAEtB8D,EAAiBjF,EAAM2B,OAC3BpB,EAAQ2E,SAAS1I,EAAM,IAClB2E,EAAMgE,MACTxJ,WACIwD,EAAUgC,EAAME,UAAY,CAAEA,SAAUF,EAAME,UAAa,MAInE2D,EAAO3C,QAAUlB,EAEjB,MAAMiE,EAAapF,EAAMwC,QACvB,IACEjF,OAAO8H,iBACL,GACA,CACEC,QAAS,CACPC,YAAY,EACZ1G,IAAK,MAAQA,EAAIyB,EAAU6B,OAAQ3F,IAErCoF,QAAS,CACP2D,YAAY,EACZ1G,IAAK,MAAQA,EAAIyB,EAAUwB,YAAatF,IAE1CgJ,UAAW,CACTD,YAAY,EACZ1G,IAAK,MAAQA,EAAIyB,EAAUyB,cAAevF,IAE5CyF,aAAc,CACZsD,YAAY,EACZ1G,IAAK,MAAQA,EAAIyB,EAAU0B,iBAAkBxF,IAE/CiJ,MAAO,CACLF,YAAY,EACZ1G,IAAK,IAAMA,EAAIyB,EAAU6B,OAAQ3F,MAIzC,CAAC8D,EAAW9D,IAGRkJ,EAAW1F,EAAM2F,YACpBxJ,GACC8I,EAAe5C,QAAQqD,SAAS,CAC9BtJ,OAAQ,CACNT,MAAOO,EAAcC,GACrBK,KAAMA,GAERf,KAAMmE,IAEV,CAACpD,IAGGoJ,EAAS5F,EAAM2F,YACnB,IACEV,EAAe5C,QAAQuD,OAAO,CAC5BxJ,OAAQ,CACNT,MAAOkD,EAAI0B,EAAQkE,YAAajI,GAChCA,KAAMA,GAERf,KAAMmE,IAEV,CAACpD,EAAM+D,EAAQkE,cAGXoB,EAAM7F,EAAM2F,YACfG,IACC,MAAMC,EAAQlH,EAAI0B,EAAQyF,QAASxJ,GAE/BuJ,GAASD,IACXC,EAAME,GAAGJ,IAAM,CACbK,MAAO,IAAMJ,EAAII,OAASJ,EAAII,QAC9BC,OAAQ,IAAML,EAAIK,QAAUL,EAAIK,SAChCC,kBAAoBC,GAClBP,EAAIM,kBAAkBC,GACxBC,eAAgB,IAAMR,EAAIQ,oBAIhC,CAAC/F,EAAQyF,QAASxJ,IAGduJ,EAAQ/F,EAAMwC,QAClB,KAAA,CACEhG,OACAb,WACIwD,EAAUkC,IAAaf,EAAUe,SACjC,CAAEA,SAAUf,EAAUe,UAAYA,GAClC,GACJqE,WACAE,SACAC,QAEF,CAACrJ,EAAM6E,EAAUf,EAAUe,SAAUqE,EAAUE,EAAQC,EAAKlK,IAoD9D,OAjDAqE,EAAMiB,UAAU,KACd,MAAMsF,EACJhG,EAAQiG,SAAS3B,kBAAoBA,EAEvCtE,EAAQ2E,SAAS1I,EAAM,IAClBwI,EAAO3C,QAAQ8C,SACdhG,EAAU6F,EAAO3C,QAAQhB,UACzB,CAAEA,SAAU2D,EAAO3C,QAAQhB,UAC3B,KAGN,MAAMoF,EAAgB,CAACjK,EAAyBb,KAC9C,MAAMoK,EAAelH,EAAI0B,EAAQyF,QAASxJ,GAEtCuJ,GAASA,EAAME,KACjBF,EAAME,GAAGS,MAAQ/K,IAMrB,GAFA8K,EAAcjK,GAAM,GAEhB+J,EAAwB,CAC1B,MAAM5K,EAAQsB,EAAY4B,EAAI0B,EAAQiG,SAAS9F,cAAelE,IAC9D4C,EAAImB,EAAQI,eAAgBnE,EAAMb,GAC9BwC,EAAYU,EAAI0B,EAAQkE,YAAajI,KACvC4C,EAAImB,EAAQkE,YAAajI,EAAMb,GAMnC,OAFCmJ,GAAgBvE,EAAQ2E,SAAS1I,GAE3B,MAEHsI,EACIyB,IAA2BhG,EAAQoG,OAAOC,OAC1CL,GAEFhG,EAAQsG,WAAWrK,GACnBiK,EAAcjK,GAAM,KAEzB,CAACA,EAAM+D,EAASuE,EAAcD,IAEjC7E,EAAMiB,UAAU,KACdV,EAAQuG,kBAAkB,CACxBzF,WACA7E,UAED,CAAC6E,EAAU7E,EAAM+D,IAEbP,EAAMwC,QACX,KAAA,CACEuD,QACAzF,YACA8E,eAEF,CAACW,EAAOzF,EAAW8E,GAEvB,CCrMA,MCzCa2B,EAAWC,IACtB,MAAMC,EAAsB,CAAA,EAE5B,IAAK,MAAMjJ,KAAOT,OAAOoG,KAAKqD,GAC5B,GAAIlL,EAAakL,EAAIhJ,KAAsB,OAAbgJ,EAAIhJ,GAAe,CAC/C,MAAMkJ,EAASH,EAAQC,EAAIhJ,IAE3B,IAAK,MAAMmJ,KAAa5J,OAAOoG,KAAKuD,GAClCD,EAAO,GAAGjJ,KAAOmJ,KAAeD,EAAOC,QAGzCF,EAAOjJ,GAAOgJ,EAAIhJ,GAItB,OAAOiJ,GCbHG,EAAe,OCArB,IAAAC,EAAe,CACb7K,EACA8K,EACAnF,EACA1G,EACA4K,IAEAiB,EACI,IACKnF,EAAO3F,GACV+K,MAAO,IACDpF,EAAO3F,IAAS2F,EAAO3F,GAAO+K,MAAQpF,EAAO3F,GAAO+K,MAAQ,CAAA,EAChE9L,CAACA,GAAO4K,IAAW,IAGvB,CAAA,ECrBNmB,EAAmB7L,GAAcK,MAAMC,QAAQN,GAASA,EAAQ,CAACA,GCgBjE8L,EAAe,KACb,IAAIC,EAA4B,GAqBhC,MAAO,CACL,aAAIC,GACF,OAAOD,GAETE,KAvBYjM,IACZ,IAAK,MAAMkM,KAAYH,EACrBG,EAASD,MAAQC,EAASD,KAAKjM,IAsBjCmM,UAlBiBD,IACjBH,EAAWK,KAAKF,GACT,CACLG,YAAa,KACXN,EAAaA,EAAWnJ,OAAQ0J,GAAMA,IAAMJ,MAehDG,YAVkB,KAClBN,EAAa,MC/BjBQ,EAAgBvM,GACdI,EAASJ,KAAW4B,OAAOoG,KAAKhI,GAAO4D,OCHzC4I,EAAgB3M,GACG,SAAjBA,EAAQC,KCHV2M,EAAgBzM,GACG,mBAAVA,ECCT0M,EAAgB1M,IACd,IAAKkB,EACH,OAAO,EAGT,MAAMyL,EAAQ3M,EAAUA,EAAsB4M,cAA6B,EAC3E,OACE5M,aACC2M,GAASA,EAAME,YAAcF,EAAME,YAAYzL,YAAcA,cCRlE0L,EAAgBjN,GACG,oBAAjBA,EAAQC,KCDViN,EAAgBlN,GACG,UAAjBA,EAAQC,KCCVkN,GAAgB9C,GAAawC,EAAcxC,IAAQA,EAAI+C,YCsBzC,SAAUC,GAAM/J,EAAaC,GACzC,MAAM+J,EAAQ9M,MAAMC,QAAQ8C,GACxBA,EACAd,EAAMc,GACJ,CAACA,GACDN,EAAaM,GAEbgK,EAA+B,IAAjBD,EAAMvJ,OAAeT,EA3B3C,SAAiBA,EAAakK,GAC5B,MAAMzJ,EAASyJ,EAAWC,MAAM,GAAG,GAAI1J,OACvC,IAAIF,EAAQ,EAEZ,KAAOA,EAAQE,GACbT,EAASX,EAAYW,GAAUO,IAAUP,EAAOkK,EAAW3J,MAG7D,OAAOP,CACT,CAkBoDoK,CAAQpK,EAAQgK,GAE5DzJ,EAAQyJ,EAAMvJ,OAAS,EACvBvB,EAAM8K,EAAMzJ,GAclB,OAZI0J,UACKA,EAAY/K,GAIT,IAAVqB,IACEtD,EAASgN,IAAgBb,EAAca,IACtC/M,MAAMC,QAAQ8M,IA5BrB,SAAsB/B,GACpB,IAAK,MAAMhJ,KAAOgJ,EAChB,GAAIA,EAAIlJ,eAAeE,KAASG,EAAY6I,EAAIhJ,IAC9C,OAAO,EAGX,OAAO,CACT,CAqBqCmL,CAAaJ,KAE9CF,GAAM/J,EAAQgK,EAAMG,MAAM,GAAG,IAGxBnK,CACT,CCjDA,IAAAsK,GAAmBlM,IACjB,IAAK,MAAMc,KAAOd,EAChB,GAAIkL,EAAWlL,EAAKc,IAClB,OAAO,EAGX,OAAO,GCDT,SAASqL,GAAmBnM,EAASoM,EAA8B,IACjE,MAAMC,EAAoBvN,MAAMC,QAAQiB,GAExC,GAAInB,EAASmB,IAASqM,EACpB,IAAK,MAAMvL,KAAOd,EAEdlB,MAAMC,QAAQiB,EAAKc,KAClBjC,EAASmB,EAAKc,MAAUoL,GAAkBlM,EAAKc,KAEhDsL,EAAOtL,GAAOhC,MAAMC,QAAQiB,EAAKc,IAAQ,GAAK,CAAA,EAC9CqL,GAAgBnM,EAAKc,GAAMsL,EAAOtL,KACxBnC,EAAkBqB,EAAKc,MACjCsL,EAAOtL,IAAO,GAKpB,OAAOsL,CACT,CAEA,SAASE,GACPtM,EACA0F,EACA6G,GAKA,MAAMF,EAAoBvN,MAAMC,QAAQiB,GAExC,GAAInB,EAASmB,IAASqM,EACpB,IAAK,MAAMvL,KAAOd,EAEdlB,MAAMC,QAAQiB,EAAKc,KAClBjC,EAASmB,EAAKc,MAAUoL,GAAkBlM,EAAKc,IAG9CG,EAAYyE,IACZO,EAAYsG,EAAsBzL,IAElCyL,EAAsBzL,GAAOhC,MAAMC,QAAQiB,EAAKc,IAC5CqL,GAAgBnM,EAAKc,GAAM,IAC3B,IAAKqL,GAAgBnM,EAAKc,KAE9BwL,GACEtM,EAAKc,GACLnC,EAAkB+G,GAAc,CAAA,EAAKA,EAAW5E,GAChDyL,EAAsBzL,IAI1ByL,EAAsBzL,IAAQoF,EAAUlG,EAAKc,GAAM4E,EAAW5E,IAKpE,OAAOyL,CACT,CAEA,IAAAC,GAAe,CAAIhJ,EAAkBkC,IACnC4G,GACE9I,EACAkC,EACAyG,GAAgBzG,IC/DpB,MAAM+G,GAAqC,CACzChO,OAAO,EACPuG,SAAS,GAGL0H,GAAc,CAAEjO,OAAO,EAAMuG,SAAS,GAE5C,IAAA2H,GAAgBC,IACd,GAAI9N,MAAMC,QAAQ6N,GAAU,CAC1B,GAAIA,EAAQvK,OAAS,EAAG,CACtB,MAAMiF,EAASsF,EACZvL,OAAQwL,GAAWA,GAAUA,EAAO1N,UAAY0N,EAAO1I,UACvD2B,IAAK+G,GAAWA,EAAOpO,OAC1B,MAAO,CAAEA,MAAO6I,EAAQtC,UAAWsC,EAAOjF,QAG5C,OAAOuK,EAAQ,GAAGzN,UAAYyN,EAAQ,GAAGzI,SAErCyI,EAAQ,GAAGE,aAAe7L,EAAY2L,EAAQ,GAAGE,WAAWrO,OAC1DwC,EAAY2L,EAAQ,GAAGnO,QAA+B,KAArBmO,EAAQ,GAAGnO,MAC1CiO,GACA,CAAEjO,MAAOmO,EAAQ,GAAGnO,MAAOuG,SAAS,GACtC0H,GACFD,GAGN,OAAOA,IC7BTM,GAAe,CACbtO,GACEuO,gBAAeC,cAAaC,gBAE9BjM,EAAYxC,GACRA,EACAuO,EACY,KAAVvO,EACE0O,IACA1O,GACGA,EACDA,EACJwO,GAAe1H,EAAS9G,GACtB,IAAIC,KAAKD,GACTyO,EACEA,EAAWzO,GACXA,ECfZ,MAAM2O,GAAkC,CACtCpI,SAAS,EACTvG,MAAO,MAGT,IAAA4O,GAAgBT,GACd9N,MAAMC,QAAQ6N,GACVA,EAAQ5K,OACN,CAACsL,EAAUT,IACTA,GAAUA,EAAO1N,UAAY0N,EAAO1I,SAChC,CACEa,SAAS,EACTvG,MAAOoO,EAAOpO,OAEhB6O,EACNF,IAEFA,GCXQ,SAAUG,GAAcxE,GACpC,MAAMJ,EAAMI,EAAGJ,IAEf,OAAIsC,EAAYtC,GACPA,EAAI6E,MAGThC,EAAa7C,GACR0E,GAActE,EAAG0E,MAAMhP,MAG5B8M,EAAiB5C,GACZ,IAAIA,EAAI+E,iBAAiB5H,IAAI,EAAGrH,WAAYA,GAGjDkP,EAAWhF,GACNgE,GAAiB5D,EAAG0E,MAAMhP,MAG5BsO,GAAgB9L,EAAY0H,EAAIlK,OAASsK,EAAGJ,IAAIlK,MAAQkK,EAAIlK,MAAOsK,EAC5E,CCpBA,ICXA6E,GAAgBnP,GAAoCA,aAAiBoP,OCSrEC,GACEC,GAEA9M,EAAY8M,GACRA,EACAH,GAAQG,GACNA,EAAKC,OACLnP,EAASkP,GACPH,GAAQG,EAAKtP,OACXsP,EAAKtP,MAAMuP,OACXD,EAAKtP,MACPsP,ECjBVE,GAAgBC,IAAW,CACzBC,YAAaD,GAAQA,IAASvL,EAC9ByL,SAAUF,IAASvL,EACnB0L,WAAYH,IAASvL,EACrB2L,QAASJ,IAASvL,EAClB4L,UAAWL,IAASvL,ICJtB,MAAM6L,GAAiB,gBAEvB,IAAAC,GAAgBC,KACZA,KACAA,EAAeC,aAEdzD,EAAWwD,EAAeC,WACzBD,EAAeC,SAASjO,YAAYpB,OAASkP,IAC9C3P,EAAS6P,EAAeC,WACvBtO,OAAOiH,OAAOoH,EAAeC,UAAUC,KACpCC,GACCA,EAAiBnO,YAAYpB,OAASkP,KCbhDM,GAAe,CACbxP,EACAmG,EACAsJ,KAECA,IACAtJ,EAAOO,UACNP,EAAOG,MAAMrG,IAAID,IACjB,IAAImG,EAAOG,OAAOoJ,KACfC,GACC3P,EAAK4P,WAAWD,IAChB,SAASjO,KAAK1B,EAAKyM,MAAMkD,EAAU5M,WCT3C,MAAM8M,GAAwB,CAC5B/C,EACA1C,EACA0F,EACAC,KAEA,IAAK,MAAMvO,KAAOsO,GAAe/O,OAAOoG,KAAK2F,GAAS,CACpD,MAAMvD,EAAQlH,EAAIyK,EAAQtL,GAE1B,GAAI+H,EAAO,CACT,MAAME,GAAEA,KAAOuG,GAAiBzG,EAEhC,GAAIE,EAAI,CACN,GAAIA,EAAG0E,MAAQ1E,EAAG0E,KAAK,IAAM/D,EAAOX,EAAG0E,KAAK,GAAI3M,KAASuO,EACvD,OAAO,EACF,GAAItG,EAAGJ,KAAOe,EAAOX,EAAGJ,IAAKI,EAAGzJ,QAAU+P,EAC/C,OAAO,EAEP,GAAIF,GAAsBG,EAAc5F,GACtC,WAGC,GAAI7K,EAASyQ,IACdH,GAAsBG,EAA2B5F,GACnD,SCxBI,SAAU6F,GACtBtK,EACA6D,EACAxJ,GAKA,MAAMiJ,EAAQ5G,EAAIsD,EAAQ3F,GAE1B,GAAIiJ,GAASxH,EAAMzB,GACjB,MAAO,CACLiJ,QACAjJ,QAIJ,MAAMD,EAAQC,EAAKoC,MAAM,KAEzB,KAAOrC,EAAMgD,QAAQ,CACnB,MAAM0D,EAAY1G,EAAMmQ,KAAK,KACvB3G,EAAQlH,EAAImH,EAAS/C,GACrB0J,EAAa9N,EAAIsD,EAAQc,GAE/B,GAAI8C,IAAU/J,MAAMC,QAAQ8J,IAAUvJ,IAASyG,EAC7C,MAAO,CAAEzG,QAGX,GAAImQ,GAAcA,EAAWlR,KAC3B,MAAO,CACLe,KAAMyG,EACNwC,MAAOkH,GAIX,GAAIA,GAAcA,EAAWC,MAAQD,EAAWC,KAAKnR,KACnD,MAAO,CACLe,KAAM,GAAGyG,SACTwC,MAAOkH,EAAWC,MAItBrQ,EAAMsQ,MAGR,MAAO,CACLrQ,OAEJ,CC3CA,ICCAsQ,GAAe,CACb3K,EACAsD,EACAjJ,KAEA,MAAMuQ,EAAmBvF,EAAsB3I,EAAIsD,EAAQ3F,IAG3D,OAFA4C,EAAI2N,EAAkB,OAAQtH,EAAMjJ,IACpC4C,EAAI+C,EAAQ3F,EAAMuQ,GACX5K,GCfT6K,GAAgBrR,GAAqC8G,EAAS9G,GCChD,SAAUsR,GACtBhO,EACA4G,EACApK,EAAO,YAEP,GACEuR,GAAU/N,IACTjD,MAAMC,QAAQgD,IAAWA,EAAOiO,MAAMF,KACtC7N,EAAUF,KAAYA,EAEvB,MAAO,CACLxD,OACA4K,QAAS2G,GAAU/N,GAAUA,EAAS,GACtC4G,MAGN,CChBA,IAAAsH,GAAgBC,GACdrR,EAASqR,KAAoBtC,GAAQsC,GACjCA,EACA,CACEzR,MAAOyR,EACP/G,QAAS,ICwBjBgH,GAAeC,MACbvH,EACAwH,EACA3K,EACA0E,EACAkG,EACAC,KAEA,MAAM5H,IACJA,EAAG8E,KACHA,EAAI+C,SACJA,EAAQC,UACRA,EAASC,UACTA,EAASC,IACTA,EAAGC,IACHA,EAAGC,QACHA,EAAOlC,SACPA,EAAQrP,KACRA,EAAI0N,cACJA,EAAaxD,MACbA,GACEX,EAAME,GACJ+H,EAA+BnP,EAAI+D,EAAYpG,GACrD,IAAKkK,GAAS6G,EAAmB9Q,IAAID,GACnC,MAAO,CAAA,EAET,MAAMyR,EAA6BtD,EAAOA,EAAK,GAAM9E,EAC/CO,EAAqBC,IACrBmH,GAA6BS,EAAS3H,iBACxC2H,EAAS7H,kBAAkBjH,EAAUkH,GAAW,GAAKA,GAAW,IAChE4H,EAAS3H,mBAGPb,EAA6B,CAAA,EAC7ByI,EAAUxF,EAAa7C,GACvBgF,EAAatP,EAAgBsK,GAC7BsI,EAAoBD,GAAWrD,EAC/BuD,GACFlE,GAAiB/B,EAAYtC,KAC7B1H,EAAY0H,EAAIlK,QAChBwC,EAAY6P,IACb3F,EAAcxC,IAAsB,KAAdA,EAAIlK,OACZ,KAAfqS,GACChS,MAAMC,QAAQ+R,KAAgBA,EAAWzO,OACtC8O,EAAoBhH,EAAaiH,KACrC,KACA9R,EACA8K,EACA7B,GAEI8I,EAAmB,CACvBC,EACAC,EACAC,EACAC,EAAmB7O,EACnB8O,EAAmB9O,KAEnB,MAAMuG,EAAUmI,EAAYC,EAAmBC,EAC/CjJ,EAAMjJ,GAAQ,CACZf,KAAM+S,EAAYG,EAAUC,EAC5BvI,UACAR,SACGwI,EAAkBG,EAAYG,EAAUC,EAASvI,KAIxD,GACEoH,GACKzR,MAAMC,QAAQ+R,KAAgBA,EAAWzO,OAC1CmO,KACGS,IAAsBC,GAAWvS,EAAkBmS,KACnD7O,EAAU6O,KAAgBA,GAC1BnD,IAAehB,GAAiBc,GAAMzI,SACtCgM,IAAY3D,GAAcI,GAAMzI,SACvC,CACA,MAAMvG,MAAEA,EAAK0K,QAAEA,GAAY2G,GAAUU,GACjC,CAAE/R,QAAS+R,EAAUrH,QAASqH,GAC9BP,GAAmBO,GAEvB,GAAI/R,IACF8J,EAAMjJ,GAAQ,CACZf,KAAMqE,EACNuG,UACAR,IAAKoI,KACFI,EAAkBvO,EAAiCuG,KAEnDiB,GAEH,OADAlB,EAAkBC,GACXZ,EAKb,KAAK2I,GAAavS,EAAkBgS,IAAShS,EAAkBiS,IAAO,CACpE,IAAIU,EACAK,EACJ,MAAMC,EAAY3B,GAAmBW,GAC/BiB,EAAY5B,GAAmBU,GAErC,GAAKhS,EAAkBmS,IAAgBrO,MAAMqO,GAUtC,CACL,MAAMgB,EACHnJ,EAAyBsE,aAAe,IAAIvO,KAAKoS,GAC9CiB,EAAqBC,GACzB,IAAItT,MAAK,IAAIA,MAAOuT,eAAiB,IAAMD,GACvCE,EAAqB,QAAZvJ,EAAIpK,KACb4T,EAAqB,QAAZxJ,EAAIpK,KAEfgH,EAASqM,EAAUnT,QAAUqS,IAC/BQ,EAAYY,EACRH,EAAkBjB,GAAciB,EAAkBH,EAAUnT,OAC5D0T,EACErB,EAAac,EAAUnT,MACvBqT,EAAY,IAAIpT,KAAKkT,EAAUnT,QAGnC8G,EAASsM,EAAUpT,QAAUqS,IAC/Ba,EAAYO,EACRH,EAAkBjB,GAAciB,EAAkBF,EAAUpT,OAC5D0T,EACErB,EAAae,EAAUpT,MACvBqT,EAAY,IAAIpT,KAAKmT,EAAUpT,YA/B2B,CAClE,MAAM2T,EACHzJ,EAAyBqE,gBACzB8D,GAAcA,EAAaA,GACzBnS,EAAkBiT,EAAUnT,SAC/B6S,EAAYc,EAAcR,EAAUnT,OAEjCE,EAAkBkT,EAAUpT,SAC/BkT,EAAYS,EAAcP,EAAUpT,OA2BxC,IAAI6S,GAAaK,KACfN,IACIC,EACFM,EAAUzI,QACV0I,EAAU1I,QACVvG,EACAA,IAEGwH,GAEH,OADAlB,EAAkBX,EAAMjJ,GAAO6J,SACxBZ,EAKb,IACGkI,GAAaC,KACbQ,IACA3L,EAASuL,IAAgBP,GAAgBzR,MAAMC,QAAQ+R,IACxD,CACA,MAAMuB,EAAkBpC,GAAmBQ,GACrC6B,EAAkBrC,GAAmBS,GACrCY,GACH3S,EAAkB0T,EAAgB5T,QACnCqS,EAAWzO,QAAUgQ,EAAgB5T,MACjCkT,GACHhT,EAAkB2T,EAAgB7T,QACnCqS,EAAWzO,QAAUiQ,EAAgB7T,MAEvC,IAAI6S,GAAaK,KACfN,EACEC,EACAe,EAAgBlJ,QAChBmJ,EAAgBnJ,UAEbiB,GAEH,OADAlB,EAAkBX,EAAMjJ,GAAO6J,SACxBZ,EAKb,GAAIsI,IAAYK,GAAW3L,EAASuL,GAAa,CAC/C,MAAQrS,MAAO8T,EAAYpJ,QAAEA,GAAY8G,GAAmBY,GAE5D,GAAIjD,GAAQ2E,KAAkBzB,EAAW0B,MAAMD,KAC7ChK,EAAMjJ,GAAQ,CACZf,KAAMqE,EACNuG,UACAR,SACGwI,EAAkBvO,EAAgCuG,KAElDiB,GAEH,OADAlB,EAAkBC,GACXZ,EAKb,GAAIoG,EACF,GAAIzD,EAAWyD,GAAW,CACxB,MACM8D,EAAgB1C,SADDpB,EAASmC,EAAYpL,GACKqL,GAE/C,GAAI0B,IACFlK,EAAMjJ,GAAQ,IACTmT,KACAtB,EACDvO,EACA6P,EAActJ,WAGbiB,GAEH,OADAlB,EAAkBuJ,EAActJ,SACzBZ,OAGN,GAAI1J,EAAS8P,GAAW,CAC7B,IAAI+D,EAAmB,CAAA,EAEvB,IAAK,MAAM5R,KAAO6N,EAAU,CAC1B,IAAK3D,EAAc0H,KAAsBtI,EACvC,MAGF,MAAMqI,EAAgB1C,SACdpB,EAAS7N,GAAKgQ,EAAYpL,GAChCqL,EACAjQ,GAGE2R,IACFC,EAAmB,IACdD,KACAtB,EAAkBrQ,EAAK2R,EAActJ,UAG1CD,EAAkBuJ,EAActJ,SAE5BiB,IACF7B,EAAMjJ,GAAQoT,IAKpB,IAAK1H,EAAc0H,KACjBnK,EAAMjJ,GAAQ,CACZqJ,IAAKoI,KACF2B,IAEAtI,GACH,OAAO7B,EAOf,OADAW,GAAkB,GACXX,GCnMT,MAAMoK,GAAiB,CACrBzE,KAAMvL,EACNiQ,eAAgBjQ,EAChBkQ,kBAAkB,GAGd,SAAUC,GAKd7O,EAAkE,IAUlE,IAwCI8O,EAxCAzJ,EAAW,IACVqJ,MACA1O,GAEDM,EAAsC,CACxCyO,YAAa,EACbtO,SAAS,EACTuO,SAAS,EACTtO,UAAWuG,EAAW5B,EAAS9F,eAC/BuB,cAAc,EACdmO,aAAa,EACbC,cAAc,EACdC,oBAAoB,EACpBpO,SAAS,EACTH,cAAe,CAAA,EACfD,YAAa,CAAA,EACbE,iBAAkB,CAAA,EAClBG,OAAQqE,EAASrE,QAAU,CAAA,EAC3Bd,SAAUmF,EAASnF,WAAY,GAE7B2E,EAAqB,CAAA,EACrBrF,GACF5E,EAASyK,EAAS9F,gBAAkB3E,EAASyK,EAAShC,UAClDvH,EAAYuJ,EAAS9F,eAAiB8F,EAAShC,SAC/C,CAAA,EACFC,EAAc+B,EAAS3B,iBACtB,CAAA,EACA5H,EAAY0D,GACbgG,EAAS,CACXC,QAAQ,EACRF,OAAO,EACP5D,OAAO,GAELH,EAAgB,CAClB+D,MAAO,IAAI6J,IACXlP,SAAU,IAAIkP,IACdC,QAAS,IAAID,IACbxL,MAAO,IAAIwL,IACXzN,MAAO,IAAIyN,KAGTE,EAAQ,EACZ,MAAM3P,EAAiC,CACrCc,SAAS,EACTE,aAAa,EACbE,kBAAkB,EAClBD,eAAe,EACfE,cAAc,EACdC,SAAS,EACTC,QAAQ,GAEV,IAAIuO,EAA2B,IAC1B5P,GAEL,MAAM6P,EAAoC,CACxC5L,MAAO0C,IACPmJ,MAAOnJ,KAGHoJ,EACJrK,EAASsK,eAAiBjR,EAStB0C,EAAY+K,MAAOyD,IACvB,IACGvK,EAASnF,WACTP,EAAgBoB,SACfwO,EAAyBxO,SACzB6O,GACF,CACA,MAAM7O,EAAUsE,EAASwK,SACrB9I,SAAqB+I,KAAc9O,cAC7B+O,EAAyBlL,GAAS,GAExC9D,IAAYT,EAAWS,SACzByO,EAAUC,MAAMhJ,KAAK,CACnB1F,cAMFiP,EAAsB,CAAC5U,EAAkB0F,MAE1CuE,EAASnF,WACTP,EAAgBmB,cACfnB,EAAgBkB,kBAChB0O,EAAyBzO,cACzByO,EAAyB1O,qBAE1BzF,GAASP,MAAMoV,KAAKzO,EAAO+D,QAAQ2K,QAAS7U,IACvCA,IACFyF,EACI7C,EAAIqC,EAAWO,iBAAkBxF,EAAMyF,GACvC4G,GAAMpH,EAAWO,iBAAkBxF,MAI3CmU,EAAUC,MAAMhJ,KAAK,CACnB5F,iBAAkBP,EAAWO,iBAC7BC,cAAeiG,EAAczG,EAAWO,sBA8ExCsP,EAAsB,CAC1B9U,EACA+U,EACA5V,EACAkK,KAEA,MAAME,EAAelH,EAAImH,EAASxJ,GAElC,GAAIuJ,EAAO,CACT,MAAM/G,EAAeH,EACnB4F,EACAjI,EACA2B,EAAYxC,GAASkD,EAAI8B,EAAgBnE,GAAQb,GAGnDwC,EAAYa,IACX6G,GAAQA,EAAyB2L,gBAClCD,EACInS,EACEqF,EACAjI,EACA+U,EAAuBvS,EAAeyL,GAAc1E,EAAME,KAE5DwL,EAAcjV,EAAMwC,GAExB2H,EAAOD,OAASnE,MAIdmP,EAAsB,CAC1BlV,EACAmV,EACA1F,EACA2F,EACAC,KAIA,IAAIC,GAAoB,EACpBC,GAAkB,EACtB,MAAM9K,EAA8D,CAClEzK,QAGF,IAAKgK,EAASnF,SAAU,CACtB,IAAK4K,GAAe2F,EAAa,EAC3B9Q,EAAgBc,SAAW8O,EAAyB9O,WACtDmQ,EAAkBtQ,EAAWG,QAC7BH,EAAWG,QAAUqF,EAAOrF,QAAUoQ,IACtCF,EAAoBC,IAAoB9K,EAAOrF,SAGjD,MAAMqQ,EAAyB7O,EAC7BvE,EAAI8B,EAAgBnE,GACpBmV,GAGFI,IAAoBlT,EAAI4C,EAAWK,YAAatF,GAChDyV,EACIpJ,GAAMpH,EAAWK,YAAatF,GAC9B4C,EAAIqC,EAAWK,YAAatF,GAAM,GACtCyK,EAAOnF,YAAcL,EAAWK,YAChCgQ,EACEA,IACEhR,EAAgBgB,aAChB4O,EAAyB5O,cACzBiQ,KAAqBE,EAG3B,GAAIhG,EAAa,CACf,MAAMiG,EAAyBrT,EAAI4C,EAAWM,cAAevF,GAExD0V,IACH9S,EAAIqC,EAAWM,cAAevF,EAAMyP,GACpChF,EAAOlF,cAAgBN,EAAWM,cAClC+P,EACEA,IACEhR,EAAgBiB,eAChB2O,EAAyB3O,gBACzBmQ,IAA2BjG,GAInC6F,GAAqBD,GAAgBlB,EAAUC,MAAMhJ,KAAKX,GAG5D,OAAO6K,EAAoB7K,EAAS,CAAA,GAGhCkL,EAAsB,CAC1B3V,EACA0F,EACAuD,EACAL,KAMA,MAAMgN,EAAqBvT,EAAI4C,EAAWU,OAAQ3F,GAC5CuU,GACHjQ,EAAgBoB,SAAWwO,EAAyBxO,UACrD/C,EAAU+C,IACVT,EAAWS,UAAYA,EAhOzB,IAAqBI,EA6OrB,GAXIkE,EAAS6L,YAAc5M,GAlONnD,EAmOW,IAzHb,EAAC9F,EAAyBiJ,KAC7CrG,EAAIqC,EAAWU,OAAQ3F,EAAMiJ,GAC7BkL,EAAUC,MAAMhJ,KAAK,CACnBzF,OAAQV,EAAWU,UAsHiBmQ,CAAa9V,EAAMiJ,GAAvDwK,EAlODsC,IACCC,aAAa/B,GACbA,EAAQgC,WAAWnQ,EAAUiQ,IAiO7BtC,EAAmBzJ,EAAS6L,cAE5BG,aAAa/B,GACbR,EAAqB,KACrBxK,EACIrG,EAAIqC,EAAWU,OAAQ3F,EAAMiJ,GAC7BoD,GAAMpH,EAAWU,OAAQ3F,KAI5BiJ,GAASrC,EAAUgP,EAAoB3M,GAAS2M,KAChDlK,EAAc9C,IACf2L,EACA,CACA,MAAM2B,EAAmB,IACpBtN,KACC2L,GAAqB5R,EAAU+C,GAAW,CAAEA,WAAY,GAC5DC,OAAQV,EAAWU,OACnB3F,QAGFiF,EAAa,IACRA,KACAiR,GAGL/B,EAAUC,MAAMhJ,KAAK8K,KAInBzB,EAAa3D,MAAO9Q,IACxB2U,EAAoB3U,GAAM,GAC1B,MAAMyC,QAAeuH,EAASwK,SAC5BvM,EACA+B,EAASmM,QdzaA,EACbrG,EACAtG,EACA8K,EACAtD,KAEA,MAAMlE,EAAiD,CAAA,EAEvD,IAAK,MAAM9M,KAAQ8P,EAAa,CAC9B,MAAMvG,EAAelH,EAAImH,EAASxJ,GAElCuJ,GAAS3G,EAAIkK,EAAQ9M,EAAMuJ,EAAME,IAGnC,MAAO,CACL6K,eACAvU,MAAO,IAAI+P,GACXhD,SACAkE,8BcwZEoF,CACEpW,GAAQmG,EAAO+D,MACfV,EACAQ,EAASsK,aACTtK,EAASgH,4BAIb,OADA2D,EAAoB3U,GACbyC,GAoBHiS,EAA2B5D,MAC/BhE,EACAuJ,EACAF,EAEI,CACFG,OAAO,MAGT,IAAK,MAAMtW,KAAQ8M,EAAQ,CACzB,MAAMvD,EAAQuD,EAAO9M,GAErB,GAAIuJ,EAAO,CACT,MAAME,GAAEA,KAAO0L,GAAe5L,EAE9B,GAAIE,EAAI,CACN,MAAM8M,EAAmBpQ,EAAOoC,MAAMtI,IAAIwJ,EAAGzJ,MACvCwW,EACJjN,EAAME,IAAM0F,GAAsB5F,EAAgBE,IAEhD+M,GAAqBlS,EAAgBkB,kBACvCmP,EAAoB,CAAC3U,IAAO,GAG9B,MAAMyW,QAAmB5F,GACvBtH,EACApD,EAAOtB,SACPoD,EACAoM,EACArK,EAASgH,4BAA8BqF,EACvCE,GAOF,GAJIC,GAAqBlS,EAAgBkB,kBACvCmP,EAAoB,CAAC3U,IAGnByW,EAAWhN,EAAGzJ,QAChBmW,EAAQG,OAAQ,EACZD,GACF,OAIHA,IACEhU,EAAIoU,EAAYhN,EAAGzJ,MAChBuW,EACEjG,GACErL,EAAWU,OACX8Q,EACAhN,EAAGzJ,MAEL4C,EAAIqC,EAAWU,OAAQ8D,EAAGzJ,KAAMyW,EAAWhN,EAAGzJ,OAChDqM,GAAMpH,EAAWU,OAAQ8D,EAAGzJ,QAGnC0L,EAAcyJ,UACNT,EACLS,EACAkB,EACAF,IAKR,OAAOA,EAAQG,OAiBXd,EAAwB,CAACxV,EAAMU,KAClCsJ,EAASnF,WACT7E,GAAQU,GAAQkC,EAAIqF,EAAajI,EAAMU,IACvCkG,EAAU8P,KAAavS,IAEpB2D,EAAyC,CAC7C/H,EACAyC,EACA6D,IAEAH,EACEnG,EACAoG,EACA,IACMgE,EAAOD,MACPjC,EACAtG,EAAYa,GACV2B,EACA8B,EAASlG,GACP,CAAEA,CAACA,GAAQyC,GACXA,GAEV6D,EACA7D,GAcEyS,EAAgB,CACpBjV,EACAb,EACAmO,EAA0B,CAAA,KAE1B,MAAM/D,EAAelH,EAAImH,EAASxJ,GAClC,IAAImV,EAAsBhW,EAE1B,GAAIoK,EAAO,CACT,MAAM6F,EAAiB7F,EAAME,GAEzB2F,KACDA,EAAevK,UACdjC,EAAIqF,EAAajI,EAAMyN,GAAgBtO,EAAOiQ,IAEhD+F,EACEtJ,EAAcuD,EAAe/F,MAAQhK,EAAkBF,GACnD,GACAA,EAEF8M,EAAiBmD,EAAe/F,KAClC,IAAI+F,EAAe/F,IAAIiE,SAASuH,QAC7B8B,GACEA,EAAUC,SACTzB,EACA7N,SAASqP,EAAUxX,QAEhBiQ,EAAejB,KACpBpP,EAAgBqQ,EAAe/F,KACjC+F,EAAejB,KAAK0G,QAASgC,IACtBA,EAAY7B,gBAAmB6B,EAAYhS,WAC1CrF,MAAMC,QAAQ0V,GAChB0B,EAAYhX,UAAYsV,EAAW7F,KAChC5O,GAAiBA,IAASmW,EAAY1X,OAGzC0X,EAAYhX,QACVsV,IAAe0B,EAAY1X,SAAWgW,KAK9C/F,EAAejB,KAAK0G,QACjBiC,GACEA,EAASjX,QAAUiX,EAAS3X,QAAUgW,GAGpCxJ,EAAYyD,EAAe/F,KACpC+F,EAAe/F,IAAIlK,MAAQ,IAE3BiQ,EAAe/F,IAAIlK,MAAQgW,EAEtB/F,EAAe/F,IAAIpK,MACtBkV,EAAUC,MAAMhJ,KAAK,CACnBpL,OACAgI,OAAQvH,EAAYwH,QAO7BqF,EAAQ8H,aAAe9H,EAAQyJ,cAC9B7B,EACElV,EACAmV,EACA7H,EAAQyJ,YACRzJ,EAAQ8H,aACR,GAGJ9H,EAAQ0J,gBAAkBC,GAAQjX,IAG9BkX,EAAY,CAKhBlX,EACAb,EACAmO,KAEA,IAAK,MAAM6J,KAAYhY,EAAO,CAC5B,IAAKA,EAAMmC,eAAe6V,GACxB,OAEF,MAAMhC,EAAahW,EAAMgY,GACnB1Q,EAAYzG,EAAO,IAAMmX,EACzB5N,EAAQlH,EAAImH,EAAS/C,IAE1BN,EAAOoC,MAAMtI,IAAID,IAChBT,EAAS4V,IACR5L,IAAUA,EAAME,MAClBvK,EAAaiW,GACV+B,EAAUzQ,EAAW0O,EAAY7H,GACjC2H,EAAcxO,EAAW0O,EAAY7H,KAIvC8J,EAA0C,CAC9CpX,EACAb,EACAmO,EAAU,CAAA,KAEV,MAAM/D,EAAQlH,EAAImH,EAASxJ,GACrBiR,EAAe9K,EAAOoC,MAAMtI,IAAID,GAChCqX,EAAa5W,EAAYtB,GAE/ByD,EAAIqF,EAAajI,EAAMqX,GAEnBpG,GACFkD,EAAU5L,MAAM6C,KAAK,CACnBpL,OACAgI,OAAQvH,EAAYwH,MAInB3D,EAAgBc,SACfd,EAAgBgB,aAChB4O,EAAyB9O,SACzB8O,EAAyB5O,cAC3BgI,EAAQ8H,aAERjB,EAAUC,MAAMhJ,KAAK,CACnBpL,OACAsF,YAAa4H,GAAe/I,EAAgB8D,GAC5C7C,QAASoQ,EAAUxV,EAAMqX,OAI7B9N,GAAUA,EAAME,IAAOpK,EAAkBgY,GAErCpC,EAAcjV,EAAMqX,EAAY/J,GADhC4J,EAAUlX,EAAMqX,EAAY/J,GAIlCkC,GAAUxP,EAAMmG,IAAWgO,EAAUC,MAAMhJ,KAAK,IAAKnG,EAAYjF,SACjEmU,EAAUC,MAAMhJ,KAAK,CACnBpL,KAAMmK,EAAOD,MAAQlK,OAAO6B,EAC5BmG,OAAQvH,EAAYwH,MAIlBiB,EAA0B4H,MAAOnR,IACrCwK,EAAOD,OAAQ,EACf,MAAMtK,EAASD,EAAMC,OACrB,IAAII,EAAeJ,EAAOI,KACtBsX,GAAsB,EAC1B,MAAM/N,EAAelH,EAAImH,EAASxJ,GAC5BuX,EAA8BpC,IAClCmC,EACEE,OAAOrU,MAAMgS,IACZjW,EAAaiW,IAAehS,MAAMgS,EAAWlO,YAC9CL,EAAUuO,EAAY9S,EAAI4F,EAAajI,EAAMmV,KAE3CsC,EAA6B9I,GAAmB3E,EAAS4E,MACzD8I,EAA4B/I,GAChC3E,EAASsJ,gBAGX,GAAI/J,EAAO,CACT,IAAIN,EACAvD,EACJ,MAAMyP,EAAavV,EAAOX,KACtBgP,GAAc1E,EAAME,IACpB/J,EAAcC,GACZ8P,EACJ9P,EAAMV,OAASmE,GAAezD,EAAMV,OAASmE,EACzCuU,KC9uBIrK,ED+uBQ/D,EAAME,IC9uBpBS,QACPoD,EAAQ4D,UACP5D,EAAQ+D,KACR/D,EAAQgE,KACRhE,EAAQ6D,WACR7D,EAAQ8D,WACR9D,EAAQiE,SACRjE,EAAQ+B,WDwuBDrF,EAASwK,UACTnS,EAAI4C,EAAWU,OAAQ3F,IACvBuJ,EAAME,GAAGmO,OElvBL,EACbnI,EACAzG,EACA4K,EACAN,EAIA1E,KAEIA,EAAKI,WAEG4E,GAAehF,EAAKK,YACrBjG,GAAayG,IACbmE,EAAcN,EAAexE,SAAWF,EAAKE,WAC9CW,IACCmE,EAAcN,EAAevE,WAAaH,EAAKG,aACjDU,GFkuBHoI,CACEpI,EACApN,EAAI4C,EAAWM,cAAevF,GAC9BiF,EAAW2O,YACX8D,EACAD,GAEEK,EAAUtI,GAAUxP,EAAMmG,EAAQsJ,GAExC7M,EAAIqF,EAAajI,EAAMmV,GAEnB1F,EACG7P,GAAWA,EAAOmY,WACrBxO,EAAME,GAAGL,QAAUG,EAAME,GAAGL,OAAOzJ,GACnC8T,GAAsBA,EAAmB,IAElClK,EAAME,GAAGP,UAClBK,EAAME,GAAGP,SAASvJ,GAGpB,MAAMiJ,EAAasM,EAAoBlV,EAAMmV,EAAY1F,GAEnD4F,GAAgB3J,EAAc9C,IAAekP,EASnD,IAPCrI,GACC0E,EAAUC,MAAMhJ,KAAK,CACnBpL,OACAf,KAAMU,EAAMV,KACZ+I,OAAQvH,EAAYwH,KAGpB0P,EAWF,OAVIrT,EAAgBoB,SAAWwO,EAAyBxO,WAChC,WAAlBsE,EAAS4E,KACPa,GACF1J,IAEQ0J,GACV1J,KAKFsP,GACAlB,EAAUC,MAAMhJ,KAAK,CAAEpL,UAAU8X,EAAU,CAAA,EAAKlP,IAMpD,IAFC6G,GAAeqI,GAAW3D,EAAUC,MAAMhJ,KAAK,IAAKnG,IAEjD+E,EAASwK,SAAU,CACrB,MAAM7O,OAAEA,SAAiB8O,EAAW,CAACzU,IAIrC,GAFAuX,EAA2BpC,GAEvBmC,EAAqB,CACvB,MAAMU,EAA4B/H,GAChChL,EAAWU,OACX6D,EACAxJ,GAEIiY,EAAoBhI,GACxBtK,EACA6D,EACAwO,EAA0BhY,MAAQA,GAGpCiJ,EAAQgP,EAAkBhP,MAC1BjJ,EAAOiY,EAAkBjY,KAEzB0F,EAAUgG,EAAc/F,SAG1BgP,EAAoB,CAAC3U,IAAO,GAC5BiJ,SACQ4H,GACJtH,EACApD,EAAOtB,SACPoD,EACAoM,EACArK,EAASgH,4BAEXhR,GACF2U,EAAoB,CAAC3U,IAErBuX,EAA2BpC,GAEvBmC,IACErO,EACFvD,GAAU,GAEVpB,EAAgBoB,SAChBwO,EAAyBxO,WAEzBA,QAAgBgP,EAAyBlL,GAAS,KAKpD8N,IACF/N,EAAME,GAAGmO,MACPX,GACE1N,EAAME,GAAGmO,MAIbjC,EAAoB3V,EAAM0F,EAASuD,EAAOL,IC71BnC,IAAC0E,GDk2BR4K,GAAc,CAAC7O,EAAU7H,KAC7B,GAAIa,EAAI4C,EAAWU,OAAQnE,IAAQ6H,EAAIK,MAErC,OADAL,EAAIK,QACG,GAKLuN,GAAwCnG,MAAO9Q,EAAMsN,EAAU,CAAA,KACnE,IAAI5H,EACA0N,EACJ,MAAM+E,EAAanN,EAAsBhL,GAEzC,GAAIgK,EAASwK,SAAU,CACrB,MAAM7O,OAlb0BmL,OAAO/Q,IACzC,MAAM4F,OAAEA,SAAiB8O,EAAW1U,GAEpC,GAAIA,EACF,IAAK,MAAMC,KAAQD,EAAO,CACxB,MAAMkJ,EAAQ5G,EAAIsD,EAAQ3F,GAC1BiJ,EACIrG,EAAIqC,EAAWU,OAAQ3F,EAAMiJ,GAC7BoD,GAAMpH,EAAWU,OAAQ3F,QAG/BiF,EAAWU,OAASA,EAGtB,OAAOA,GAoagByS,CACnBzW,EAAY3B,GAAQA,EAAOmY,GAG7BzS,EAAUgG,EAAc/F,GACxByN,EAAmBpT,GACdmY,EAAWzI,KAAM1P,GAASqC,EAAIsD,EAAQ3F,IACvC0F,OACK1F,GACToT,SACQiF,QAAQC,IACZH,EAAW3R,IAAIsK,MAAOrK,IACpB,MAAM8C,EAAQlH,EAAImH,EAAS/C,GAC3B,aAAaiO,EACXnL,GAASA,EAAME,GAAK,CAAEhD,CAACA,GAAY8C,GAAUA,OAInDmH,MAAM1O,UACLoR,GAAqBnO,EAAWS,UAAYK,KAE/CqN,EAAmB1N,QAAgBgP,EAAyBlL,GAqB9D,OAlBA2K,EAAUC,MAAMhJ,KAAK,KACdnF,EAASjG,KACZsE,EAAgBoB,SAAWwO,EAAyBxO,UACpDA,IAAYT,EAAWS,QACrB,CAAA,EACA,CAAE1F,WACFgK,EAASwK,WAAaxU,EAAO,CAAE0F,WAAY,GAC/CC,OAAQV,EAAWU,SAGrB2H,EAAQiL,cACLnF,GACDvD,GACErG,EACA0O,GACAlY,EAAOmY,EAAahS,EAAO+D,OAGxBkJ,GAGHsD,GACJyB,IAIA,MAAMnQ,EAAS,IACTmC,EAAOD,MAAQjC,EAAc9D,GAGnC,OAAOxC,EAAYwW,GACfnQ,EACA/B,EAASkS,GACP9V,EAAI2F,EAAQmQ,GACZA,EAAW3R,IAAKxG,GAASqC,EAAI2F,EAAQhI,KAGvCwY,GAAoD,CACxDxY,EACA8D,KAAS,CAETgF,UAAWzG,GAAKyB,GAAamB,GAAYU,OAAQ3F,GACjDoF,UAAW/C,GAAKyB,GAAamB,GAAYK,YAAatF,GACtDiJ,MAAO5G,GAAKyB,GAAamB,GAAYU,OAAQ3F,GAC7CyF,eAAgBpD,EAAI4C,EAAWO,iBAAkBxF,GACjDgJ,YAAa3G,GAAKyB,GAAamB,GAAYM,cAAevF,KActDyY,GAA0C,CAACzY,EAAMiJ,EAAOqE,KAC5D,MAAMjE,GAAOhH,EAAImH,EAASxJ,EAAM,CAAEyJ,GAAI,KAAMA,IAAM,CAAA,GAAIJ,IAChDqP,EAAerW,EAAI4C,EAAWU,OAAQ3F,IAAS,CAAA,GAG7CqJ,IAAKsP,EAAU9O,QAAEA,EAAO5K,KAAEA,KAAS2Z,GAAoBF,EAE/D9V,EAAIqC,EAAWU,OAAQ3F,EAAM,IACxB4Y,KACA3P,EACHI,QAGF8K,EAAUC,MAAMhJ,KAAK,CACnBpL,OACA2F,OAAQV,EAAWU,OACnBD,SAAS,IAGX4H,GAAWA,EAAQiL,aAAelP,GAAOA,EAAIK,OAASL,EAAIK,SA6BtD9D,GAA2CjB,GAC/CwP,EAAUC,MAAM9I,UAAU,CACxBF,KACEtH,IGt/BO,IACb9D,EACA6Y,EACA/T,EAFA9E,EH4/B8B2E,EAAM3E,KG3/BpC6Y,EH2/B0C/U,EAAU9D,KG1/BpD8E,EH0/B0DH,EAAMG,MGx/B/D9E,GACA6Y,GACD7Y,IAAS6Y,IACT7N,EAAsBhL,GAAM0P,KACzBoJ,GACCA,IACChU,EACGgU,IAAgBD,EAChBC,EAAYlJ,WAAWiJ,IACvBA,EAAWjJ,WAAWkJ,OTPjB,EACbC,EAIAzU,EACAS,EACAd,KAEAc,EAAgBgU,GAChB,MAAM/Y,KAAEA,KAAS8D,GAAciV,EAE/B,OACErN,EAAc5H,IACd/C,OAAOoG,KAAKrD,GAAWf,QAAUhC,OAAOoG,KAAK7C,GAAiBvB,QAC9DhC,OAAOoG,KAAKrD,GAAWwL,KACpB9N,GACC8C,EAAgB9C,OACdyC,GAAUZ,KMq+BV2V,CACElV,EACCa,EAAMb,WAA+BQ,EACtC2U,GACAtU,EAAMuU,eAGRvU,EAAMmB,SAAS,CACbkC,OAAQ,IAAKC,MACVhD,KACAnB,EACHI,cACEC,OAIPqH,YAcCnB,GAA8C,CAACrK,EAAMsN,EAAU,CAAA,KACnE,IAAK,MAAM7G,KAAazG,EAAOgL,EAAsBhL,GAAQmG,EAAO+D,MAClE/D,EAAO+D,MAAMiP,OAAO1S,GACpBN,EAAOoC,MAAM4Q,OAAO1S,GAEf6G,EAAQ8L,YACX/M,GAAM7C,EAAS/C,GACf4F,GAAMpE,EAAaxB,KAGpB6G,EAAQ+L,WAAahN,GAAMpH,EAAWU,OAAQc,IAC9C6G,EAAQgM,WAAajN,GAAMpH,EAAWK,YAAamB,IACnD6G,EAAQiM,aAAelN,GAAMpH,EAAWM,cAAekB,IACvD6G,EAAQkM,kBACPnN,GAAMpH,EAAWO,iBAAkBiB,IACpCuD,EAAS3B,mBACPiF,EAAQmM,kBACTpN,GAAMlI,EAAgBsC,GAG1B0N,EAAUC,MAAMhJ,KAAK,CACnBpD,OAAQvH,EAAYwH,KAGtBkM,EAAUC,MAAMhJ,KAAK,IAChBnG,KACEqI,EAAQgM,UAAiB,CAAElU,QAASoQ,KAAhB,CAAA,KAG1BlI,EAAQoM,aAAe3T,KAGpBuE,GAAgE,EACpEzF,WACA7E,YAGG2C,EAAUkC,IAAasF,EAAOD,OAC7BrF,GACFsB,EAAOtB,SAAS5E,IAAID,MAEpB6E,EAAWsB,EAAOtB,SAAS0B,IAAIvG,GAAQmG,EAAOtB,SAASsU,OAAOnZ,KAI5D0I,GAA0C,CAAC1I,EAAMsN,EAAU,CAAA,KAC/D,IAAI/D,EAAQlH,EAAImH,EAASxJ,GACzB,MAAM2Z,EACJhX,EAAU2K,EAAQzI,WAAalC,EAAUqH,EAASnF,UAwBpD,OAtBAjC,EAAI4G,EAASxJ,EAAM,IACbuJ,GAAS,CAAA,EACbE,GAAI,IACEF,GAASA,EAAME,GAAKF,EAAME,GAAK,CAAEJ,IAAK,CAAErJ,SAC5CA,OACAkK,OAAO,KACJoD,KAGPnH,EAAO+D,MAAM3D,IAAIvG,GAEbuJ,EACFe,GAAkB,CAChBzF,SAAUlC,EAAU2K,EAAQzI,UACxByI,EAAQzI,SACRmF,EAASnF,SACb7E,SAGF8U,EAAoB9U,GAAM,EAAMsN,EAAQnO,OAGnC,IACDwa,EACA,CAAE9U,SAAUyI,EAAQzI,UAAYmF,EAASnF,UACzC,MACAmF,EAAS4P,YACT,CACE1I,WAAY5D,EAAQ4D,SACpBG,IAAK7C,GAAalB,EAAQ+D,KAC1BC,IAAK9C,GAAalB,EAAQgE,KAC1BF,UAAW5C,GAAqBlB,EAAQ8D,WACxCD,UAAW3C,GAAalB,EAAQ6D,WAChCI,QAAS/C,GAAalB,EAAQiE,UAEhC,GACJvR,OACAkJ,WACAE,OAAQF,EACRG,IAAMA,IACJ,GAAIA,EAAK,CACPX,GAAS1I,EAAMsN,GACf/D,EAAQlH,EAAImH,EAASxJ,GAErB,MAAM6Z,EAAWlY,EAAY0H,EAAIlK,QAC7BkK,EAAIyQ,kBACDzQ,EAAIyQ,iBAAiB,yBAAyB,IAEjDzQ,EACE0Q,EI5nCD,CAAC1Q,GACd6C,EAAa7C,IAAQtK,EAAgBsK,GJ2nCLsI,CAAkBkI,GACpC1L,EAAO5E,EAAME,GAAG0E,MAAQ,GAE9B,GACE4L,EACI5L,EAAKmB,KAAM/B,GAAgBA,IAAWsM,GACtCA,IAAatQ,EAAME,GAAGJ,IAE1B,OAGFzG,EAAI4G,EAASxJ,EAAM,CACjByJ,GAAI,IACCF,EAAME,MACLsQ,EACA,CACE5L,KAAM,IACDA,EAAKpM,OAAOoK,IACf0N,KACIra,MAAMC,QAAQ4C,EAAI8B,EAAgBnE,IAAS,CAAC,IAAM,IAExDqJ,IAAK,CAAEpK,KAAM4a,EAAS5a,KAAMe,SAE9B,CAAEqJ,IAAKwQ,MAIf/E,EAAoB9U,GAAM,OAAO6B,EAAWgY,QAE5CtQ,EAAQlH,EAAImH,EAASxJ,EAAM,CAAA,GAEvBuJ,EAAME,KACRF,EAAME,GAAGS,OAAQ,IAGlBF,EAAS3B,kBAAoBiF,EAAQjF,qBAClCvI,EAAmBqG,EAAOoC,MAAOvI,KAASmK,EAAOC,SACnDjE,EAAO6N,QAAQzN,IAAIvG,MAMvBga,GAAc,IAClBhQ,EAASuJ,kBACT1D,GAAsBrG,EAAS0O,GAAa/R,EAAO+D,OAyB/C+P,GACJ,CAACC,EAASC,IAAcrJ,MAAOsJ,IAC7B,IAAIC,EACAD,IACFA,EAAEE,gBAAkBF,EAAEE,iBACrBF,EAA+BG,SAC7BH,EAA+BG,WAEpC,IAAIC,EACF/Z,EAAYwH,GAMd,GAJAkM,EAAUC,MAAMhJ,KAAK,CACnByI,cAAc,IAGZ7J,EAASwK,SAAU,CACrB,MAAM7O,OAAEA,EAAMqC,OAAEA,SAAiByM,IACjCxP,EAAWU,OAASA,EACpB6U,EAAc/Z,EAAYuH,cAEpB0M,EAAyBlL,GAGjC,GAAIrD,EAAOtB,SAAS4V,KAClB,IAAK,MAAMza,KAAQmG,EAAOtB,SACxBwH,GAAMmO,EAAaxa,GAMvB,GAFAqM,GAAMpH,EAAWU,OAAQ,QAErB+F,EAAczG,EAAWU,QAAS,CACpCwO,EAAUC,MAAMhJ,KAAK,CACnBzF,OAAQ,CAAA,IAEV,UACQuU,EAAQM,EAAmCJ,GACjD,MAAOnR,GACPoR,EAAepR,QAGbkR,SACIA,EAAU,IAAKlV,EAAWU,QAAUyU,GAE5CJ,KACA/D,WAAW+D,IAUb,GAPA7F,EAAUC,MAAMhJ,KAAK,CACnBwI,aAAa,EACbC,cAAc,EACdC,mBAAoBpI,EAAczG,EAAWU,UAAY0U,EACzD3G,YAAazO,EAAWyO,YAAc,EACtC/N,OAAQV,EAAWU,SAEjB0U,EACF,MAAMA,GAoCNK,GAAqC,CACzCtU,EACAuU,EAAmB,CAAA,KAEnB,MAAMC,EAAgBxU,EAAa3F,EAAY2F,GAAcjC,EACvD0W,EAAqBpa,EAAYma,GACjCE,EAAqBpP,EAActF,GACnC4B,EAAS8S,EAAqB3W,EAAiB0W,EAMrD,GAJKF,EAAiBI,oBACpB5W,EAAiByW,IAGdD,EAAiBK,WAAY,CAChC,GAAIL,EAAiBM,gBAAiB,CACpC,MAAMC,EAAgB,IAAInH,IAAI,IACzB5N,EAAO+D,SACPnJ,OAAOoG,KAAK+F,GAAe/I,EAAgB8D,MAEhD,IAAK,MAAMxB,KAAajH,MAAMoV,KAAKsG,GACjC7Y,EAAI4C,EAAWK,YAAamB,GACxB7D,EAAIoF,EAAQvB,EAAWpE,EAAI4F,EAAaxB,IACxC2Q,EACE3Q,EACApE,EAAI2F,EAAQvB,QAGf,CACL,GAAIpG,GAASsB,EAAYyE,GACvB,IAAK,MAAMpG,KAAQmG,EAAO+D,MAAO,CAC/B,MAAMX,EAAQlH,EAAImH,EAASxJ,GAC3B,GAAIuJ,GAASA,EAAME,GAAI,CACrB,MAAM2F,EAAiB5P,MAAMC,QAAQ8J,EAAME,GAAG0E,MAC1C5E,EAAME,GAAG0E,KAAK,GACd5E,EAAME,GAAGJ,IAEb,GAAIwC,EAAcuD,GAAiB,CACjC,MAAM+L,EAAO/L,EAAegM,QAAQ,QACpC,GAAID,EAAM,CACRA,EAAKE,QACL,SAOV,GAAIV,EAAiBW,cACnB,IAAK,MAAM7U,KAAaN,EAAO+D,MAC7BkN,EACE3Q,EACApE,EAAI2F,EAAQvB,SAIhB+C,EAAU,CAAA,EAIdvB,EAAc+B,EAAS3B,iBACnBsS,EAAiBI,kBACdta,EAAY0D,GACZ,CAAA,EACF1D,EAAYuH,GAEjBmM,EAAU5L,MAAM6C,KAAK,CACnBpD,OAAQ,IAAKA,KAGfmM,EAAUC,MAAMhJ,KAAK,CACnBpD,OAAQ,IAAKA,KAIjB7B,EAAS,CACP+D,MAAOyQ,EAAiBM,gBAAkB9U,EAAO+D,MAAQ,IAAI6J,IAC7DC,QAAS,IAAID,IACbxL,MAAO,IAAIwL,IACXlP,SAAU,IAAIkP,IACdzN,MAAO,IAAIyN,IACXrN,UAAU,EACVgD,MAAO,IAGTS,EAAOD,OACJ5F,EAAgBoB,WACfiV,EAAiBjB,eACjBiB,EAAiBM,gBAErB9Q,EAAO7D,QAAU0D,EAAS3B,iBAE1B8L,EAAUC,MAAMhJ,KAAK,CACnBsI,YAAaiH,EAAiBY,gBAC1BtW,EAAWyO,YACX,EACJtO,SAAS0V,IAELH,EAAiBrB,UACfrU,EAAWG,WAETuV,EAAiBI,mBAChBnU,EAAUR,EAAYjC,KAE/ByP,cAAa+G,EAAiBa,iBAC1BvW,EAAW2O,YAEftO,YAAawV,EACT,CAAA,EACAH,EAAiBM,gBACfN,EAAiBI,mBAAqB9S,EACpCiF,GAAe/I,EAAgB8D,GAC/BhD,EAAWK,YACbqV,EAAiBI,mBAAqB3U,EACpC8G,GAAe/I,EAAgBiC,GAC/BuU,EAAiBrB,UACfrU,EAAWK,YACX,CAAA,EACVC,cAAeoV,EAAiBpB,YAC5BtU,EAAWM,cACX,CAAA,EACJI,OAAQgV,EAAiBc,WAAaxW,EAAWU,OAAS,CAAA,EAC1DmO,qBAAoB6G,EAAiBe,wBACjCzW,EAAW6O,mBAEfD,cAAc,EACd3P,cAAeC,KAIbkX,GAAoC,CAACjV,EAAYuU,IACrDD,GACE9O,EAAWxF,GACNA,EAAwB6B,GACzB7B,EACJuU,GAqBE1B,GACJ/C,IAEAjR,EAAa,IACRA,KACAiR,IAaDtR,GAAU,CACdb,QAAS,CACP2E,YACA2B,cACAmO,iBACAyB,gBACAxB,YACA7S,cACA6O,aACAuF,eACAlS,YACA0N,YACAzP,YACA4V,eA/vC0C,CAC5C3b,EACAgI,EAAS,GACT4T,EACAC,EACAC,GAAkB,EAClBC,GAA6B,KAE7B,GAAIF,GAAQD,IAAW5R,EAASnF,SAAU,CAExC,GADAsF,EAAOC,QAAS,EACZ2R,GAA8Bvc,MAAMC,QAAQ4C,EAAImH,EAASxJ,IAAQ,CACnE,MAAMwa,EAAcoB,EAAOvZ,EAAImH,EAASxJ,GAAO6b,EAAKG,KAAMH,EAAKI,MAC/DH,GAAmBlZ,EAAI4G,EAASxJ,EAAMwa,GAGxC,GACEuB,GACAvc,MAAMC,QAAQ4C,EAAI4C,EAAWU,OAAQ3F,IACrC,CACA,MAAM2F,EAASiW,EACbvZ,EAAI4C,EAAWU,OAAQ3F,GACvB6b,EAAKG,KACLH,EAAKI,MAEPH,GAAmBlZ,EAAIqC,EAAWU,OAAQ3F,EAAM2F,GKlPzC,EAAI0D,EAAQrJ,MACxB8B,EAAQO,EAAIgH,EAAKrJ,IAAO+C,QAAUsJ,GAAMhD,EAAKrJ,ILkPxCkc,CAAgBjX,EAAWU,OAAQ3F,GAGrC,IACGsE,EAAgBiB,eACf2O,EAAyB3O,gBAC3BwW,GACAvc,MAAMC,QAAQ4C,EAAI4C,EAAWM,cAAevF,IAC5C,CACA,MAAMuF,EAAgBqW,EACpBvZ,EAAI4C,EAAWM,cAAevF,GAC9B6b,EAAKG,KACLH,EAAKI,MAEPH,GAAmBlZ,EAAIqC,EAAWM,cAAevF,EAAMuF,IAGrDjB,EAAgBgB,aAAe4O,EAAyB5O,eAC1DL,EAAWK,YAAc4H,GAAe/I,EAAgB8D,IAG1DkM,EAAUC,MAAMhJ,KAAK,CACnBpL,OACAoF,QAASoQ,EAAUxV,EAAMgI,GACzB1C,YAAaL,EAAWK,YACxBK,OAAQV,EAAWU,OACnBD,QAAST,EAAWS,eAGtB9C,EAAIqF,EAAajI,EAAMgI,IA0sCvBsC,qBACA6R,WAhsCgBxW,IAClBV,EAAWU,OAASA,EACpBwO,EAAUC,MAAMhJ,KAAK,CACnBzF,OAAQV,EAAWU,OACnBD,SAAS,KA6rCT0W,eAl6BFpc,GAEA8B,EACEO,EACE8H,EAAOD,MAAQjC,EAAc9D,EAC7BnE,EACAgK,EAAS3B,iBAAmBhG,EAAI8B,EAAgBnE,EAAM,IAAM,KA65B9D0a,UACA2B,oBA3BwB,IAC1BzQ,EAAW5B,EAAS9F,gBACnB8F,EAAS9F,gBAA6BoY,KAAMtU,IAC3CqT,GAAMrT,EAAQgC,EAASuS,cACvBpI,EAAUC,MAAMhJ,KAAK,CACnB/F,WAAW,MAuBb8C,iBA98BqB,KACvB,IAAK,MAAMnI,KAAQmG,EAAO6N,QAAS,CACjC,MAAMzK,EAAelH,EAAImH,EAASxJ,GAElCuJ,IACGA,EAAME,GAAG0E,KACN5E,EAAME,GAAG0E,KAAKuC,MAAOrH,IAAS8C,GAAK9C,KAClC8C,GAAK5C,EAAME,GAAGJ,OACnBgB,GAAWrK,GAGfmG,EAAO6N,QAAU,IAAID,KAo8BnByI,aApTkB3X,IAChBlC,EAAUkC,KACZsP,EAAUC,MAAMhJ,KAAK,CAAEvG,aACvBgL,GACErG,EACA,CAACH,EAAKrJ,KACJ,MAAMgQ,EAAsB3N,EAAImH,EAASxJ,GACrCgQ,IACF3G,EAAIxE,SAAWmL,EAAavG,GAAG5E,UAAYA,EAEvCrF,MAAMC,QAAQuQ,EAAavG,GAAG0E,OAChC6B,EAAavG,GAAG0E,KAAK0G,QAASpD,IAC5BA,EAAS5M,SAAWmL,EAAavG,GAAG5E,UAAYA,MAKxD,GACA,KAmSFsP,YACA7P,kBACA,WAAIkF,GACF,OAAOA,GAET,eAAIvB,GACF,OAAOA,GAET,UAAIkC,GACF,OAAOA,GAET,UAAIA,CAAOhL,GACTgL,EAAShL,GAEX,kBAAIgF,GACF,OAAOA,GAET,UAAIgC,GACF,OAAOA,GAET,UAAIA,CAAOhH,GACTgH,EAAShH,GAEX,cAAI8F,GACF,OAAOA,GAET,YAAI+E,GACF,OAAOA,GAET,YAAIA,CAAS7K,GACX6K,EAAW,IACNA,KACA7K,KAITmM,UAvfiD3G,IACjDwF,EAAOD,OAAQ,EACfgK,EAA2B,IACtBA,KACAvP,EAAMb,WAEJ8B,GAAW,IACbjB,EACHb,UAAWoQ,KAgfb+C,WACAvO,YACAuR,gBACA3T,MAljBwC,CACxCtG,EAIAwC,IAEAoJ,EAAW5L,GACPmU,EAAUC,MAAM9I,UAAU,CACxBF,KAAOqR,GACL,WAAYA,GACZzc,EACE8H,OAAUjG,EAAWW,GACrBia,KAON3U,EACE9H,EACAwC,GACA,GA4hBN4U,WACAV,aACA2E,SACAqB,WA9QkD,CAAC1c,EAAMsN,EAAU,CAAA,KAC/DjL,EAAImH,EAASxJ,KACX2B,EAAY2L,EAAQ9K,cACtB4U,EAASpX,EAAMS,EAAY4B,EAAI8B,EAAgBnE,MAE/CoX,EACEpX,EACAsN,EAAQ9K,cAEVI,EAAIuB,EAAgBnE,EAAMS,EAAY6M,EAAQ9K,gBAG3C8K,EAAQiM,aACXlN,GAAMpH,EAAWM,cAAevF,GAG7BsN,EAAQgM,YACXjN,GAAMpH,EAAWK,YAAatF,GAC9BiF,EAAWG,QAAUkI,EAAQ9K,aACzBgT,EAAUxV,EAAMS,EAAY4B,EAAI8B,EAAgBnE,KAChDwV,KAGDlI,EAAQ+L,YACXhN,GAAMpH,EAAWU,OAAQ3F,GACzBsE,EAAgBoB,SAAWK,KAG7BoO,EAAUC,MAAMhJ,KAAK,IAAKnG,MAmP5B0X,YAxlBqD3c,IACrDA,GACEgL,EAAsBhL,GAAM6U,QAAS+H,GACnCvQ,GAAMpH,EAAWU,OAAQiX,IAG7BzI,EAAUC,MAAMhJ,KAAK,CACnBzF,OAAQ3F,EAAOiF,EAAWU,OAAS,CAAA,KAklBrC0E,cACAoO,YACAoE,SAzG8C,CAAC7c,EAAMsN,EAAU,CAAA,KAC/D,MAAM/D,EAAQlH,EAAImH,EAASxJ,GACrBoP,EAAiB7F,GAASA,EAAME,GAEtC,GAAI2F,EAAgB,CAClB,MAAMyK,EAAWzK,EAAejB,KAC5BiB,EAAejB,KAAK,GACpBiB,EAAe/F,IAEfwQ,EAASnQ,QACXmQ,EAASnQ,QACT4D,EAAQwP,cACNlR,EAAWiO,EAASlQ,SACpBkQ,EAASlQ,YA6Ff6O,kBAGF,MAAO,IACF5T,GACHmY,YAAanY,GAEjB,CM7hDA,IAAAoY,GAAe,KACb,GAAsB,oBAAXC,QAA0BA,OAAOC,WAC1C,OAAOD,OAAOC,aAGhB,MAAMC,EACmB,oBAAhBC,YAA8Bhe,KAAKie,MAA4B,IAApBD,YAAYC,MAEhE,MAAO,uCAAuClb,QAAQ,QAAUmb,IAC9D,MAAMC,GAAqB,GAAhBC,KAAKC,SAAgBN,GAAK,GAAK,EAE1C,OAAa,KAALG,EAAWC,EAAS,EAAJA,EAAW,GAAKG,SAAS,OCRrDC,GAAe,CACb3d,EACA6C,EACAyK,EAAiC,CAAA,IAEjCA,EAAQiL,aAAe5W,EAAY2L,EAAQiL,aACvCjL,EAAQsQ,WACR,GAAG5d,KAAQ2B,EAAY2L,EAAQuQ,YAAchb,EAAQyK,EAAQuQ,cAC7D,GCTNC,GAAe,CAAIpd,EAAWvB,IAAwB,IACjDuB,KACAsK,EAAsB7L,ICJ3B4e,GAAmB5e,GACjBK,MAAMC,QAAQN,GAASA,EAAMqH,IAAI,aAAmB3E,ECOxC,SAAUmc,GACtBtd,EACAmC,EACA1D,GAEA,MAAO,IACFuB,EAAK+L,MAAM,EAAG5J,MACdmI,EAAsB7L,MACtBuB,EAAK+L,MAAM5J,GAElB,CChBA,IAAAob,GAAe,CACbvd,EACAkU,EACAsJ,IAEK1e,MAAMC,QAAQiB,IAIfiB,EAAYjB,EAAKwd,MACnBxd,EAAKwd,QAAMrc,GAEbnB,EAAKyd,OAAOD,EAAI,EAAGxd,EAAKyd,OAAOvJ,EAAM,GAAG,IAEjClU,GARE,GCNX0d,GAAe,CAAI1d,EAAWvB,IAAwB,IACjD6L,EAAsB7L,MACtB6L,EAAsBtK,ICY3B,IAAA2d,GAAe,CAAI3d,EAAWmC,IAC5BlB,EAAYkB,GACR,GAdN,SAA4BnC,EAAW4d,GACrC,IAAIC,EAAI,EACR,MAAMC,EAAO,IAAI9d,GAEjB,IAAK,MAAMmC,KAASyb,EAClBE,EAAKL,OAAOtb,EAAQ0b,EAAG,GACvBA,IAGF,OAAOzc,EAAQ0c,GAAMzb,OAASyb,EAAO,EACvC,CAKMC,CACE/d,EACCsK,EAAsBnI,GAAoB6b,KAAK,CAACC,EAAGC,IAAMD,EAAIC,ICrBtEC,GAAe,CAAIne,EAAWoe,EAAgBC,MAC3Cre,EAAKoe,GAASpe,EAAKqe,IAAW,CAACre,EAAKqe,GAASre,EAAKoe,KCDrDE,GAAe,CAAIxE,EAAkB3X,EAAe1D,KAClDqb,EAAY3X,GAAS1D,EACdqb,sBjDgDP7V,GAEAA,EAAMsa,OAAO7W,EAAuDzD,iBEtBtE,SAGEA,GACA,MAAMC,EAAUjB,KACTub,EAASC,GAAc3b,EAAMwB,UAAS,IACvCjB,QACJA,EAAUa,EAAQb,QAAOqb,SACzBA,EAAQC,SACRA,EAAQjV,OACRA,EAAMwR,OACNA,EAAShR,EAAY0U,QACrBA,EAAOC,QACPA,EAAOC,QACPA,EAAOP,OACPA,EAAMQ,UACNA,EAASC,eACTA,KACGC,GACDhb,EAEEib,EAAS9O,MAAOnR,IACpB,IAAIkgB,GAAW,EACX5gB,EAAO,SAEL8E,EAAQkW,aAAanJ,MAAOpQ,IAChC,MAAMof,EAAW,IAAIC,SACrB,IAAIC,EAAe,GAEnB,IACEA,EAAeC,KAAKC,UAAUxf,GAC9B,MAAAyf,GAAM,CAER,MAAMC,EAAoB7V,EAAQxG,EAAQkE,aAE1C,IAAK,MAAMzG,KAAO4e,EAChBN,EAASO,OAAO7e,EAAK4e,EAAkB5e,IAazC,GAVI4d,SACIA,EAAS,CACb1e,OACAf,QACAic,SACAkE,WACAE,iBAIA5V,EACF,IACE,MAAMkW,EAAgC,CACpChB,GAAWA,EAAQ,gBACnBC,GACA7P,KAAMvQ,GAAUA,GAASA,EAAMmI,SAAS,SAEpCiZ,QAAiBC,MAAMC,OAAOrW,GAAS,CAC3CwR,SACA0D,QAAS,IACJA,KACCC,GAAuB,wBAAZA,EACX,CAAE,eAAgBA,GAClB,IAENmB,KAAMJ,EAAgCN,EAAeF,IAIrDS,IACCb,GACIA,EAAea,EAASI,QACzBJ,EAASI,OAAS,KAAOJ,EAASI,QAAU,MAEhDd,GAAW,EACXL,GAAWA,EAAQ,CAAEe,aACrBthB,EAAOwhB,OAAOF,EAASI,SAEvBlB,GAAaA,EAAU,CAAEc,aAE3B,MAAOtX,GACP4W,GAAW,EACXL,GAAWA,EAAQ,CAAEvW,YAxDrBlF,CA2DHpE,GAECkgB,GAAYlb,EAAMZ,UACpBY,EAAMZ,QAAQoQ,UAAUC,MAAMhJ,KAAK,CACjC0I,oBAAoB,IAEtBnP,EAAMZ,QAAQ0U,SAAS,cAAe,CACpCxZ,WASN,OAJAuE,EAAMiB,UAAU,KACd0a,GAAW,IACV,IAEIF,EACLzb,EAAAod,cAAApd,EAAAqd,SAAA,KACG5B,EAAO,CACNW,YAIJpc,EAAAod,cAAA,OAAA,CACEE,WAAY5B,EACZ9U,OAAQA,EACRwR,OAAQA,EACR2D,QAASA,EACTH,SAAUQ,KACND,GAEHN,EAGP,uBZjEE1a,IAEA,MAAM0a,SAAEA,KAAa3e,GAASiE,EAC9B,OACEnB,EAAAod,cAACrd,EAAgBwd,SAAQ,CAAC5hB,MAAOuB,GAC9B2e,kI4DRD,SAOJ1a,GAOA,MAAMC,EAAUjB,KACVI,QACJA,EAAUa,EAAQb,QAAO/D,KACzBA,EAAIghB,QACJA,EAAU,KAAI3Y,iBACdA,EAAgBM,MAChBA,GACEhE,GACGmI,EAAQmU,GAAazd,EAAMwB,SAASjB,EAAQqY,eAAepc,IAC5DkhB,EAAM1d,EAAM2B,OAChBpB,EAAQqY,eAAepc,GAAMwG,IAAIwW,KAE7BmE,EAAY3d,EAAM2B,OAAO2H,GACzBsU,EAAY5d,EAAM2B,QAAO,GAE/Bgc,EAAUtb,QAAUiH,EACpB/I,EAAQoC,OAAOoC,MAAMhC,IAAIvG,GAEzBwD,EAAMwC,QACJ,IACE2C,GACC5E,EAA2D2E,SAC1D1I,EACA2I,GAEJ,CAAC5E,EAAS4E,EAAO3I,IAGnBuE,EACE,IACER,EAAQoQ,UAAU5L,MAAM+C,UAAU,CAChCF,KAAM,EACJpD,SACAhI,KAAMqhB,MAKN,GAAIA,IAAmBrhB,IAASqhB,EAAgB,CAC9C,MAAM7G,EAAcnY,EAAI2F,EAAQhI,GAC5BR,MAAMC,QAAQ+a,KAChByG,EAAUzG,GACV0G,EAAIrb,QAAU2U,EAAYhU,IAAIwW,SAInCxR,YACL,CAACzH,EAAS/D,IAGZ,MAAMshB,EAAe9d,EAAM2F,YAMvBoY,IAEAH,EAAUvb,SAAU,EACpB9B,EAAQ4X,eAAe3b,EAAMuhB,IAE/B,CAACxd,EAAS/D,IAqRZ,OA5GAwD,EAAMiB,UAAU,KAQd,GAPAV,EAAQoG,OAAOC,QAAS,EAExBoF,GAAUxP,EAAM+D,EAAQoC,SACtBpC,EAAQoQ,UAAUC,MAAMhJ,KAAK,IACxBrH,EAAQkB,aAIbmc,EAAUvb,WACR8I,GAAmB5K,EAAQiG,SAAS4E,MAAMC,YAC1C9K,EAAQkB,WAAW2O,eACpBjF,GAAmB5K,EAAQiG,SAASsJ,gBAAgBzE,WAErD,GAAI9K,EAAQiG,SAASwK,SACnBzQ,EAAQ0Q,WAAW,CAACzU,IAAOsc,KAAM7Z,IAC/B,MAAMwG,EAAQ5G,EAAII,EAAOkD,OAAQ3F,GAC3BwhB,EAAgBnf,EAAI0B,EAAQkB,WAAWU,OAAQ3F,IAGnDwhB,GACMvY,GAASuY,EAAcviB,MACxBgK,IACEuY,EAAcviB,OAASgK,EAAMhK,MAC5BuiB,EAAc3X,UAAYZ,EAAMY,SACpCZ,GAASA,EAAMhK,QAEnBgK,EACIrG,EAAImB,EAAQkB,WAAWU,OAAQ3F,EAAMiJ,GACrCoD,GAAMtI,EAAQkB,WAAWU,OAAQ3F,GACrC+D,EAAQoQ,UAAUC,MAAMhJ,KAAK,CAC3BzF,OAAQ5B,EAAQkB,WAAWU,gBAI5B,CACL,MAAM4D,EAAelH,EAAI0B,EAAQyF,QAASxJ,IAExCuJ,IACAA,EAAME,IAEJkF,GAAmB5K,EAAQiG,SAASsJ,gBAAgBzE,YACpDF,GAAmB5K,EAAQiG,SAAS4E,MAAMC,YAG5CgC,GACEtH,EACAxF,EAAQoC,OAAOtB,SACfd,EAAQkE,YACRlE,EAAQiG,SAASsK,eAAiBjR,EAClCU,EAAQiG,SAASgH,2BACjB,GACAsL,KACCrT,IACEyC,EAAczC,IACflF,EAAQoQ,UAAUC,MAAMhJ,KAAK,CAC3BzF,OAAQ2K,GACNvM,EAAQkB,WAAWU,OACnBsD,EACAjJ,MAQd+D,EAAQoQ,UAAUC,MAAMhJ,KAAK,CAC3BpL,OACAgI,OAAQvH,EAAYsD,EAAQkE,eAG9BlE,EAAQoC,OAAOuD,OACbmG,GAAsB9L,EAAQyF,QAAS,CAACH,EAAK7H,KAC3C,GACEuC,EAAQoC,OAAOuD,OACflI,EAAIoO,WAAW7L,EAAQoC,OAAOuD,QAC9BL,EAAIK,MAGJ,OADAL,EAAIK,QACG,IAKb3F,EAAQoC,OAAOuD,MAAQ,GAEvB3F,EAAQgC,YACRqb,EAAUvb,SAAU,GACnB,CAACiH,EAAQ9M,EAAM+D,IAElBP,EAAMiB,UAAU,MACbpC,EAAI0B,EAAQkE,YAAajI,IAAS+D,EAAQ4X,eAAe3b,GAEnD,KAQL+D,EAAQiG,SAAS3B,kBAAoBA,EACjCtE,EAAQsG,WAAWrK,GARD,EAACA,EAAyBb,KAC9C,MAAMoK,EAAelH,EAAI0B,EAAQyF,QAASxJ,GACtCuJ,GAASA,EAAME,KACjBF,EAAME,GAAGS,MAAQ/K,IAMjB8K,CAAcjK,GAAM,KAEzB,CAACA,EAAM+D,EAASid,EAAS3Y,IAErB,CACLoZ,KAAMje,EAAM2F,YAlMD,CAAC2V,EAAgBC,KAC5B,MAAMwC,EAA0Bxd,EAAQqY,eAAepc,GACvD6e,GAAY0C,EAAyBzC,EAAQC,GAC7CF,GAAYqC,EAAIrb,QAASiZ,EAAQC,GACjCuC,EAAaC,GACbN,EAAUM,GACVxd,EAAQ4X,eACN3b,EACAuhB,EACA1C,GACA,CACE7C,KAAM8C,EACN7C,KAAM8C,IAER,IAoL4B,CAACuC,EAActhB,EAAM+D,IACnD2d,KAAMle,EAAM2F,YAjLD,CAACyL,EAAcsJ,KAC1B,MAAMqD,EAA0Bxd,EAAQqY,eAAepc,GACvDie,GAAYsD,EAAyB3M,EAAMsJ,GAC3CD,GAAYiD,EAAIrb,QAAS+O,EAAMsJ,GAC/BoD,EAAaC,GACbN,EAAUM,GACVxd,EAAQ4X,eACN3b,EACAuhB,EACAtD,GACA,CACEjC,KAAMpH,EACNqH,KAAMiC,IAER,IAmK4B,CAACoD,EAActhB,EAAM+D,IACnD4d,QAASne,EAAM2F,YA7PD,CACdhK,EAGAmO,KAEA,MAAMsU,EAAe5W,EAAsBvK,EAAYtB,IACjDoiB,EAA0BnD,GAC9Bra,EAAQqY,eAAepc,GACvB4hB,GAEF7d,EAAQoC,OAAOuD,MAAQiU,GAAkB3d,EAAM,EAAGsN,GAClD4T,EAAIrb,QAAUuY,GAAU8C,EAAIrb,QAAS+b,EAAapb,IAAIwW,KACtDsE,EAAaC,GACbN,EAAUM,GACVxd,EAAQ4X,eAAe3b,EAAMuhB,EAAyBnD,GAAW,CAC/DpC,KAAM+B,GAAe5e,MA6Oa,CAACmiB,EAActhB,EAAM+D,IACzDsc,OAAQ7c,EAAM2F,YAtRD,CACbhK,EAGAmO,KAEA,MAAMuU,EAAc7W,EAAsBvK,EAAYtB,IAChDoiB,EAA0BzD,GAC9B/Z,EAAQqY,eAAepc,GACvB6hB,GAEF9d,EAAQoC,OAAOuD,MAAQiU,GACrB3d,EACAuhB,EAAwBxe,OAAS,EACjCuK,GAEF4T,EAAIrb,QAAUiY,GAASoD,EAAIrb,QAASgc,EAAYrb,IAAIwW,KACpDsE,EAAaC,GACbN,EAAUM,GACVxd,EAAQ4X,eAAe3b,EAAMuhB,EAAyBzD,GAAU,CAC9D9B,KAAM+B,GAAe5e,MAkQW,CAACmiB,EAActhB,EAAM+D,IACvD+d,OAAQte,EAAM2F,YA3OAtG,IACd,MAAM0e,EAEAlD,GAActa,EAAQqY,eAAepc,GAAO6C,GAClDqe,EAAIrb,QAAUwY,GAAc6C,EAAIrb,QAAShD,GACzCye,EAAaC,GACbN,EAAUM,IACT/hB,MAAMC,QAAQ4C,EAAI0B,EAAQyF,QAASxJ,KAClC4C,EAAImB,EAAQyF,QAASxJ,OAAM6B,GAC7BkC,EAAQ4X,eAAe3b,EAAMuhB,EAAyBlD,GAAe,CACnErC,KAAMnZ,KAiO0B,CAACye,EAActhB,EAAM+D,IACvDia,OAAQxa,EAAM2F,YA9ND,CACbtG,EACA1D,EAGAmO,KAEA,MAAMyU,EAAc/W,EAAsBvK,EAAYtB,IAChDoiB,EAA0BS,GAC9Bje,EAAQqY,eAAepc,GACvB6C,EACAkf,GAEFhe,EAAQoC,OAAOuD,MAAQiU,GAAkB3d,EAAM6C,EAAOyK,GACtD4T,EAAIrb,QAAUmc,GAASd,EAAIrb,QAAShD,EAAOkf,EAAYvb,IAAIwW,KAC3DsE,EAAaC,GACbN,EAAUM,GACVxd,EAAQ4X,eAAe3b,EAAMuhB,EAAyBS,GAAU,CAC9DhG,KAAMnZ,EACNoZ,KAAM8B,GAAe5e,MA2MW,CAACmiB,EAActhB,EAAM+D,IACvDke,OAAQze,EAAM2F,YApKD,CACbtG,EACA1D,KAEA,MAAM4I,EAActH,EAAYtB,GAC1BoiB,EAA0BvC,GAC9Bjb,EAAQqY,eAENpc,GACF6C,EACAkF,GAEFmZ,EAAIrb,QAAU,IAAI0b,GAAyB/a,IAAI,CAAC0b,EAAM3D,IACnD2D,GAAQ3D,IAAM1b,EAAuBqe,EAAIrb,QAAQ0Y,GAA3BvB,MAEzBsE,EAAaC,GACbN,EAAU,IAAIM,IACdxd,EAAQ4X,eACN3b,EACAuhB,EACAvC,GACA,CACEhD,KAAMnZ,EACNoZ,KAAMlU,IAER,GACA,IA0IgC,CAACuZ,EAActhB,EAAM+D,IACvD5B,QAASqB,EAAM2F,YAtIfhK,IAIA,MAAMoiB,EAA0BvW,EAAsBvK,EAAYtB,IAClE+hB,EAAIrb,QAAU0b,EAAwB/a,IAAIwW,IAC1CsE,EAAa,IAAIC,IACjBN,EAAU,IAAIM,IACdxd,EAAQ4X,eACN3b,EACA,IAAIuhB,GACA7gB,GAAeA,EACnB,IACA,GACA,IAwHkC,CAAC4gB,EAActhB,EAAM+D,IACzD+I,OAAQtJ,EAAMwC,QACZ,IACE8G,EAAOtG,IAAI,CAAC+C,EAAO1G,KAAK,IACnB0G,EACHyX,CAACA,GAAUE,EAAIrb,QAAQhD,IAAUma,QAErC,CAAClQ,EAAQkU,IAGf,kBCvZM,SAKJrc,EAAkE,IAElE,MAAMwd,EAAe3e,EAAM2B,YAEzBtD,GACIugB,EAAU5e,EAAM2B,YAA4BtD,IAC3CiC,EAAWiB,GAAmBvB,EAAMwB,SAAkC,CAC3EI,SAAS,EACTK,cAAc,EACdJ,UAAWuG,EAAWjH,EAAMT,eAC5B0P,aAAa,EACbC,cAAc,EACdC,oBAAoB,EACpBpO,SAAS,EACTgO,YAAa,EACbpO,YAAa,CAAA,EACbC,cAAe,CAAA,EACfC,iBAAkB,CAAA,EAClBG,OAAQhB,EAAMgB,QAAU,CAAA,EACxBd,SAAUF,EAAME,WAAY,EAC5B8O,SAAS,EACTzP,cAAe0H,EAAWjH,EAAMT,oBAC5BrC,EACA8C,EAAMT,gBAGZ,IAAKie,EAAatc,QAChB,GAAIlB,EAAMoY,YACRoF,EAAatc,QAAU,IAClBlB,EAAMoY,YACTjZ,aAGEa,EAAMT,gBAAkB0H,EAAWjH,EAAMT,gBAC3CS,EAAMoY,YAAY1B,MAAM1W,EAAMT,cAAeS,EAAM4X,kBAEhD,CACL,MAAMQ,YAAEA,KAAgB4C,GAASnM,GAAkB7O,GAEnDwd,EAAatc,QAAU,IAClB8Z,EACH7b,aAKN,MAAMC,EAAUoe,EAAatc,QAAQ9B,QAwFrC,OAvFAA,EAAQiG,SAAWrF,EAEnBJ,EAA0B,KACxB,MAAM8d,EAAMte,EAAQ6B,WAAW,CAC7B9B,UAAWC,EAAQO,gBACnBwB,SAAU,IAAMf,EAAgB,IAAKhB,EAAQkB,aAC7CiU,cAAc,IAUhB,OAPAnU,EAAiBrE,IAAI,IAChBA,EACHiT,SAAS,KAGX5P,EAAQkB,WAAW0O,SAAU,EAEtB0O,GACN,CAACte,IAEJP,EAAMiB,UACJ,IAAMV,EAAQyY,aAAa7X,EAAME,UACjC,CAACd,EAASY,EAAME,WAGlBrB,EAAMiB,UAAU,KACVE,EAAMiK,OACR7K,EAAQiG,SAAS4E,KAAOjK,EAAMiK,MAE5BjK,EAAM2O,iBACRvP,EAAQiG,SAASsJ,eAAiB3O,EAAM2O,iBAEzC,CAACvP,EAASY,EAAMiK,KAAMjK,EAAM2O,iBAE/B9P,EAAMiB,UAAU,KACVE,EAAMgB,SACR5B,EAAQoY,WAAWxX,EAAMgB,QACzB5B,EAAQiW,gBAET,CAACjW,EAASY,EAAMgB,SAEnBnC,EAAMiB,UAAU,KACdE,EAAM0D,kBACJtE,EAAQoQ,UAAUC,MAAMhJ,KAAK,CAC3BpD,OAAQjE,EAAQ+D,eAEnB,CAAC/D,EAASY,EAAM0D,mBAEnB7E,EAAMiB,UAAU,KACd,GAAIV,EAAQO,gBAAgBc,QAAS,CACnC,MAAMA,EAAUrB,EAAQyR,YACpBpQ,IAAYtB,EAAUsB,SACxBrB,EAAQoQ,UAAUC,MAAMhJ,KAAK,CAC3BhG,cAIL,CAACrB,EAASD,EAAUsB,UAEvB5B,EAAMiB,UAAU,KACVE,EAAMqD,SAAWpB,EAAUjC,EAAMqD,OAAQoa,EAAQvc,UACnD9B,EAAQ2W,OAAO/V,EAAMqD,OAAQ,CAC3BsT,eAAe,KACZvX,EAAQiG,SAASuS,eAEtB6F,EAAQvc,QAAUlB,EAAMqD,OACxBjD,EAAiBqP,IAAK,IAAWA,MAEjCrQ,EAAQsY,uBAET,CAACtY,EAASY,EAAMqD,SAEnBxE,EAAMiB,UAAU,KACTV,EAAQoG,OAAOD,QAClBnG,EAAQgC,YACRhC,EAAQoG,OAAOD,OAAQ,GAGrBnG,EAAQoG,OAAO7D,QACjBvC,EAAQoG,OAAO7D,OAAQ,EACvBvC,EAAQoQ,UAAUC,MAAMhJ,KAAK,IAAKrH,EAAQkB,cAG5ClB,EAAQoE,qBAGVga,EAAatc,QAAQ/B,UAAYD,EAAkBC,EAAWC,GAEvDoe,EAAatc,OACtB"}