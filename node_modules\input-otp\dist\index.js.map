{"version": 3, "sources": ["../src/index.ts", "../src/input.tsx", "../src/sync-timeouts.ts", "../src/use-previous.ts", "../src/use-pwm-badge.tsx", "../src/regexp.ts"], "sourcesContent": ["export * from './input'\nexport * from './regexp'\nexport { OTPInputProps, SlotProps, RenderProps } from './types'\n", "'use client'\n\nimport * as React from 'react'\n\nimport { syncTimeouts } from './sync-timeouts'\nimport { OTPInputProps, RenderProps } from './types'\nimport { usePrevious } from './use-previous'\nimport { usePasswordManagerBadge } from './use-pwm-badge'\n\nexport const OTPInputContext = React.createContext<RenderProps>(\n  {} as RenderProps,\n)\n\nexport const OTPInput = React.forwardRef<HTMLInputElement, OTPInputProps>(\n  (\n    {\n      value: uncheckedValue,\n      onChange: uncheckedOnChange,\n      maxLength,\n      textAlign = 'left',\n      pattern,\n      placeholder,\n      inputMode = 'numeric',\n      onComplete,\n      pushPasswordManagerStrategy = 'increase-width',\n      pasteTransformer,\n      containerClassName,\n      noScriptCSSFallback = NOSCRIPT_CSS_FALLBACK,\n\n      render,\n      children,\n\n      ...props\n    },\n    ref,\n  ) => {\n    // Only used when `value` state is not provided\n    const [internalValue, setInternalValue] = React.useState(\n      typeof props.defaultValue === 'string' ? props.defaultValue : '',\n    )\n\n    // Definitions\n    const value = uncheckedValue ?? internalValue\n    const previousValue = usePrevious(value)\n    const onChange = React.useCallback(\n      (newValue: string) => {\n        uncheckedOnChange?.(newValue)\n        setInternalValue(newValue)\n      },\n      [uncheckedOnChange],\n    )\n    const regexp = React.useMemo(\n      () =>\n        pattern\n          ? typeof pattern === 'string'\n            ? new RegExp(pattern)\n            : pattern\n          : null,\n      [pattern],\n    )\n\n    /** useRef */\n    const inputRef = React.useRef<HTMLInputElement>(null)\n    const containerRef = React.useRef<HTMLDivElement>(null)\n    const initialLoadRef = React.useRef({\n      value,\n      onChange,\n      isIOS:\n        typeof window !== 'undefined' &&\n        window?.CSS?.supports?.('-webkit-touch-callout', 'none'),\n    })\n    const inputMetadataRef = React.useRef<{\n      prev: [number | null, number | null, 'none' | 'forward' | 'backward']\n    }>({\n      prev: [\n        inputRef.current?.selectionStart,\n        inputRef.current?.selectionEnd,\n        inputRef.current?.selectionDirection,\n      ],\n    })\n    React.useImperativeHandle(ref, () => inputRef.current, [])\n    React.useEffect(() => {\n      const input = inputRef.current\n      const container = containerRef.current\n\n      if (!input || !container) {\n        return\n      }\n\n      // Sync input value\n      if (initialLoadRef.current.value !== input.value) {\n        initialLoadRef.current.onChange(input.value)\n      }\n\n      // Previous selection\n      inputMetadataRef.current.prev = [\n        input.selectionStart,\n        input.selectionEnd,\n        input.selectionDirection,\n      ]\n      function onDocumentSelectionChange() {\n        if (document.activeElement !== input) {\n          setMirrorSelectionStart(null)\n          setMirrorSelectionEnd(null)\n          return\n        }\n\n        // Aliases\n        const _s = input.selectionStart\n        const _e = input.selectionEnd\n        const _dir = input.selectionDirection\n        const _ml = input.maxLength\n        const _val = input.value\n        const _prev = inputMetadataRef.current.prev\n\n        // Algorithm\n        let start = -1\n        let end = -1\n        let direction: 'forward' | 'backward' | 'none' = undefined\n        if (_val.length !== 0 && _s !== null && _e !== null) {\n          const isSingleCaret = _s === _e\n          const isInsertMode = _s === _val.length && _val.length < _ml\n\n          if (isSingleCaret && !isInsertMode) {\n            const c = _s\n            if (c === 0) {\n              start = 0\n              end = 1\n              direction = 'forward'\n            } else if (c === _ml) {\n              start = c - 1\n              end = c\n              direction = 'backward'\n            } else if (_ml > 1 && _val.length > 1) {\n              let offset = 0\n              if (_prev[0] !== null && _prev[1] !== null) {\n                direction = c < _prev[1] ? 'backward' : 'forward'\n                const wasPreviouslyInserting =\n                  _prev[0] === _prev[1] && _prev[0] < _ml\n                if (direction === 'backward' && !wasPreviouslyInserting) {\n                  offset = -1\n                }\n              }\n\n              start = offset + c\n              end = offset + c + 1\n            }\n          }\n\n          if (start !== -1 && end !== -1 && start !== end) {\n            inputRef.current.setSelectionRange(start, end, direction)\n          }\n        }\n\n        // Finally, update the state\n        const s = start !== -1 ? start : _s\n        const e = end !== -1 ? end : _e\n        const dir = direction ?? _dir\n        setMirrorSelectionStart(s)\n        setMirrorSelectionEnd(e)\n        // Store the previous selection value\n        inputMetadataRef.current.prev = [s, e, dir]\n      }\n      document.addEventListener('selectionchange', onDocumentSelectionChange, {\n        capture: true,\n      })\n\n      // Set initial mirror state\n      onDocumentSelectionChange()\n      document.activeElement === input && setIsFocused(true)\n\n      // Apply needed styles\n      if (!document.getElementById('input-otp-style')) {\n        const styleEl = document.createElement('style')\n        styleEl.id = 'input-otp-style'\n        document.head.appendChild(styleEl)\n\n        if (styleEl.sheet) {\n          const autofillStyles =\n            'background: transparent !important; color: transparent !important; border-color: transparent !important; opacity: 0 !important; box-shadow: none !important; -webkit-box-shadow: none !important; -webkit-text-fill-color: transparent !important;'\n\n          safeInsertRule(\n            styleEl.sheet,\n            '[data-input-otp]::selection { background: transparent !important; color: transparent !important; }',\n          )\n          safeInsertRule(\n            styleEl.sheet,\n            `[data-input-otp]:autofill { ${autofillStyles} }`,\n          )\n          safeInsertRule(\n            styleEl.sheet,\n            `[data-input-otp]:-webkit-autofill { ${autofillStyles} }`,\n          )\n          // iOS\n          safeInsertRule(\n            styleEl.sheet,\n            `@supports (-webkit-touch-callout: none) { [data-input-otp] { letter-spacing: -.6em !important; font-weight: 100 !important; font-stretch: ultra-condensed; font-optical-sizing: none !important; left: -1px !important; right: 1px !important; } }`,\n          )\n          // PWM badges\n          safeInsertRule(\n            styleEl.sheet,\n            `[data-input-otp] + * { pointer-events: all !important; }`,\n          )\n        }\n      }\n      // Track root height\n      const updateRootHeight = () => {\n        if (container) {\n          container.style.setProperty(\n            '--root-height',\n            `${input.clientHeight}px`,\n          )\n        }\n      }\n      updateRootHeight()\n      const resizeObserver = new ResizeObserver(updateRootHeight)\n      resizeObserver.observe(input)\n\n      return () => {\n        document.removeEventListener(\n          'selectionchange',\n          onDocumentSelectionChange,\n          { capture: true },\n        )\n        resizeObserver.disconnect()\n      }\n    }, [])\n\n    /** Mirrors for UI rendering purpose only */\n    const [isHoveringInput, setIsHoveringInput] = React.useState(false)\n    const [isFocused, setIsFocused] = React.useState(false)\n    const [mirrorSelectionStart, setMirrorSelectionStart] = React.useState<\n      number | null\n    >(null)\n    const [mirrorSelectionEnd, setMirrorSelectionEnd] = React.useState<\n      number | null\n    >(null)\n\n    /** Effects */\n    React.useEffect(() => {\n      syncTimeouts(() => {\n        // Forcefully remove :autofill state\n        inputRef.current?.dispatchEvent(new Event('input'))\n\n        // Update the selection state\n        const s = inputRef.current?.selectionStart\n        const e = inputRef.current?.selectionEnd\n        const dir = inputRef.current?.selectionDirection\n        if (s !== null && e !== null) {\n          setMirrorSelectionStart(s)\n          setMirrorSelectionEnd(e)\n          inputMetadataRef.current.prev = [s, e, dir]\n        }\n      })\n    }, [value, isFocused])\n\n    React.useEffect(() => {\n      if (previousValue === undefined) {\n        return\n      }\n\n      if (\n        value !== previousValue &&\n        previousValue.length < maxLength &&\n        value.length === maxLength\n      ) {\n        onComplete?.(value)\n      }\n    }, [maxLength, onComplete, previousValue, value])\n\n    const pwmb = usePasswordManagerBadge({\n      containerRef,\n      inputRef,\n      pushPasswordManagerStrategy,\n      isFocused,\n    })\n\n    /** Event handlers */\n    const _changeListener = React.useCallback(\n      (e: React.ChangeEvent<HTMLInputElement>) => {\n        const newValue = e.currentTarget.value.slice(0, maxLength)\n        if (newValue.length > 0 && regexp && !regexp.test(newValue)) {\n          e.preventDefault()\n          return\n        }\n        const maybeHasDeleted =\n          typeof previousValue === 'string' &&\n          newValue.length < previousValue.length\n        if (maybeHasDeleted) {\n          // Since cutting/deleting text doesn't trigger\n          // selectionchange event, we'll have to dispatch it manually.\n          // NOTE: The following line also triggers when cmd+A then pasting\n          // a value with smaller length, which is not ideal for performance.\n          document.dispatchEvent(new Event('selectionchange'))\n        }\n        onChange(newValue)\n      },\n      [maxLength, onChange, previousValue, regexp],\n    )\n    const _focusListener = React.useCallback(() => {\n      if (inputRef.current) {\n        const start = Math.min(inputRef.current.value.length, maxLength - 1)\n        const end = inputRef.current.value.length\n        inputRef.current?.setSelectionRange(start, end)\n        setMirrorSelectionStart(start)\n        setMirrorSelectionEnd(end)\n      }\n      setIsFocused(true)\n    }, [maxLength])\n    // Fix iOS pasting\n    const _pasteListener = React.useCallback(\n      (e: React.ClipboardEvent<HTMLInputElement>) => {\n        const input = inputRef.current\n        if (!pasteTransformer && (!initialLoadRef.current.isIOS || !e.clipboardData || !input)) {\n          return\n        }\n        \n        const _content = e.clipboardData.getData('text/plain')\n        const content = pasteTransformer\n          ? pasteTransformer(_content)\n          : _content\n        e.preventDefault()\n\n        const start = inputRef.current?.selectionStart\n        const end = inputRef.current?.selectionEnd\n\n        const isReplacing = start !== end\n\n        const newValueUncapped = isReplacing\n          ? value.slice(0, start) + content + value.slice(end) // Replacing\n          : value.slice(0, start) + content + value.slice(start) // Inserting\n        const newValue = newValueUncapped.slice(0, maxLength)\n\n        if (newValue.length > 0 && regexp && !regexp.test(newValue)) {\n          return\n        }\n\n        input.value = newValue\n        onChange(newValue)\n\n        const _start = Math.min(newValue.length, maxLength - 1)\n        const _end = newValue.length\n\n        input.setSelectionRange(_start, _end)\n        setMirrorSelectionStart(_start)\n        setMirrorSelectionEnd(_end)\n      },\n      [maxLength, onChange, regexp, value],\n    )\n\n    /** Styles */\n    const rootStyle = React.useMemo<React.CSSProperties>(\n      () => ({\n        position: 'relative',\n        cursor: props.disabled ? 'default' : 'text',\n        userSelect: 'none',\n        WebkitUserSelect: 'none',\n        pointerEvents: 'none',\n      }),\n      [props.disabled],\n    )\n\n    const inputStyle = React.useMemo<React.CSSProperties>(\n      () => ({\n        position: 'absolute',\n        inset: 0,\n        width: pwmb.willPushPWMBadge\n          ? `calc(100% + ${pwmb.PWM_BADGE_SPACE_WIDTH})`\n          : '100%',\n        clipPath: pwmb.willPushPWMBadge\n          ? `inset(0 ${pwmb.PWM_BADGE_SPACE_WIDTH} 0 0)`\n          : undefined,\n        height: '100%',\n        display: 'flex',\n        textAlign,\n        opacity: '1', // Mandatory for iOS hold-paste\n        color: 'transparent',\n        pointerEvents: 'all',\n        background: 'transparent',\n        caretColor: 'transparent',\n        border: '0 solid transparent',\n        outline: '0 solid transparent',\n        boxShadow: 'none',\n        lineHeight: '1',\n        letterSpacing: '-.5em',\n        fontSize: 'var(--root-height)',\n        fontFamily: 'monospace',\n        fontVariantNumeric: 'tabular-nums',\n        // letterSpacing: '-1em',\n        // transform: 'scale(1.5)',\n        // paddingRight: '100%',\n        // paddingBottom: '100%',\n        // debugging purposes\n        // inset: undefined,\n        // position: undefined,\n        // color: 'black',\n        // background: 'white',\n        // opacity: '1',\n        // caretColor: 'black',\n        // padding: '0',\n        // letterSpacing: 'unset',\n        // fontSize: 'unset',\n        // paddingInline: '.5rem',\n      }),\n      [pwmb.PWM_BADGE_SPACE_WIDTH, pwmb.willPushPWMBadge, textAlign],\n    )\n\n    /** Rendering */\n    const renderedInput = React.useMemo(\n      () => (\n        <input\n          autoComplete={props.autoComplete || 'one-time-code'}\n          {...props}\n          data-input-otp\n          data-input-otp-placeholder-shown={value.length === 0 || undefined}\n          data-input-otp-mss={mirrorSelectionStart}\n          data-input-otp-mse={mirrorSelectionEnd}\n          inputMode={inputMode}\n          pattern={regexp?.source}\n          aria-placeholder={placeholder}\n          style={inputStyle}\n          maxLength={maxLength}\n          value={value}\n          ref={inputRef}\n          onPaste={e => {\n            _pasteListener(e)\n            props.onPaste?.(e)\n          }}\n          onChange={_changeListener}\n          onMouseOver={e => {\n            setIsHoveringInput(true)\n            props.onMouseOver?.(e)\n          }}\n          onMouseLeave={e => {\n            setIsHoveringInput(false)\n            props.onMouseLeave?.(e)\n          }}\n          onFocus={e => {\n            _focusListener()\n            props.onFocus?.(e)\n          }}\n          onBlur={e => {\n            setIsFocused(false)\n            props.onBlur?.(e)\n          }}\n        />\n      ),\n      [\n        _changeListener,\n        _focusListener,\n        _pasteListener,\n        inputMode,\n        inputStyle,\n        maxLength,\n        mirrorSelectionEnd,\n        mirrorSelectionStart,\n        props,\n        regexp?.source,\n        value,\n      ],\n    )\n\n    const contextValue = React.useMemo<RenderProps>(() => {\n      return {\n        slots: Array.from({ length: maxLength }).map((_, slotIdx) => {\n          const isActive =\n            isFocused &&\n            mirrorSelectionStart !== null &&\n            mirrorSelectionEnd !== null &&\n            ((mirrorSelectionStart === mirrorSelectionEnd &&\n              slotIdx === mirrorSelectionStart) ||\n              (slotIdx >= mirrorSelectionStart && slotIdx < mirrorSelectionEnd))\n\n          const char = value[slotIdx] !== undefined ? value[slotIdx] : null\n          const placeholderChar = value[0] !== undefined ? null : placeholder?.[slotIdx] ?? null\n\n          return {\n            char,\n            placeholderChar,\n            isActive,\n            hasFakeCaret: isActive && char === null,\n          }\n        }),\n        isFocused,\n        isHovering: !props.disabled && isHoveringInput,\n      }\n    }, [\n      isFocused,\n      isHoveringInput,\n      maxLength,\n      mirrorSelectionEnd,\n      mirrorSelectionStart,\n      props.disabled,\n      value,\n    ])\n\n    const renderedChildren = React.useMemo(() => {\n      if (render) {\n        return render(contextValue)\n      }\n      return (\n        <OTPInputContext.Provider value={contextValue}>\n          {children}\n        </OTPInputContext.Provider>\n      )\n    }, [children, contextValue, render])\n\n    return (\n      <>\n        {noScriptCSSFallback !== null && (\n          <noscript>\n            <style>{noScriptCSSFallback}</style>\n          </noscript>\n        )}\n\n        <div\n          ref={containerRef}\n          data-input-otp-container\n          style={rootStyle}\n          className={containerClassName}\n        >\n          {renderedChildren}\n\n          <div\n            style={{\n              position: 'absolute',\n              inset: 0,\n              pointerEvents: 'none',\n            }}\n          >\n            {renderedInput}\n          </div>\n        </div>\n      </>\n    )\n  },\n)\nOTPInput.displayName = 'Input'\n\nfunction safeInsertRule(sheet: CSSStyleSheet, rule: string) {\n  try {\n    sheet.insertRule(rule)\n  } catch {\n    console.error('input-otp could not insert CSS rule:', rule)\n  }\n}\n\n// Decided to go with <noscript>\n// instead of `scripting` CSS media query\n// because it's a fallback for initial page load\n// and the <script> tag won't be loaded\n// unless the user has JS disabled.\nconst NOSCRIPT_CSS_FALLBACK = `\n[data-input-otp] {\n  --nojs-bg: white !important;\n  --nojs-fg: black !important;\n\n  background-color: var(--nojs-bg) !important;\n  color: var(--nojs-fg) !important;\n  caret-color: var(--nojs-fg) !important;\n  letter-spacing: .25em !important;\n  text-align: center !important;\n  border: 1px solid var(--nojs-fg) !important;\n  border-radius: 4px !important;\n  width: 100% !important;\n}\n@media (prefers-color-scheme: dark) {\n  [data-input-otp] {\n    --nojs-bg: black !important;\n    --nojs-fg: white !important;\n  }\n}`\n", "export function syncTimeouts(cb: (...args: any[]) => unknown): number[] {\n  const t1 = setTimeout(cb, 0) // For faster machines\n  const t2 = setTimeout(cb, 1_0)\n  const t3 = setTimeout(cb, 5_0)\n  return [t1, t2, t3]\n}\n", "import * as React from 'react'\n\nexport function usePrevious<T>(value: T) {\n  const ref = React.useRef<T>()\n  React.useEffect(() => {\n    ref.current = value\n  })\n  return ref.current\n}\n", "import * as React from 'react'\nimport { OTPInputProps } from './types'\n\nconst PWM_BADGE_MARGIN_RIGHT = 18\nconst PWM_BADGE_SPACE_WIDTH_PX = 40\nconst PWM_BADGE_SPACE_WIDTH = `${PWM_BADGE_SPACE_WIDTH_PX}px` as const\n\nconst PASSWORD_MANAGERS_SELECTORS = [\n  '[data-lastpass-icon-root]', // LastPass\n  'com-1password-button', // 1Password\n  '[data-dashlanecreated]', // Dashlane\n  '[style$=\"2147483647 !important;\"]', // Bitwarden\n].join(',')\n\nexport function usePasswordManagerBadge({\n  containerRef,\n  inputRef,\n  pushPasswordManagerStrategy,\n  isFocused,\n}: {\n  containerRef: React.RefObject<HTMLDivElement>\n  inputRef: React.RefObject<HTMLInputElement>\n  pushPasswordManagerStrategy: OTPInputProps['pushPasswordManagerStrategy']\n  isFocused: boolean\n}) {\n  /** Password managers have a badge\n   *  and I'll use this state to push them\n   *  outside the input */\n  const [hasPWMBadge, setHasPWMBadge] = React.useState(false)\n  const [hasPWMBadgeSpace, setHasPWMBadgeSpace] = React.useState(false)\n  const [done, setDone] = React.useState(false)\n\n  const willPushPWMBadge = React.useMemo(() => {\n    if (pushPasswordManagerStrategy === 'none') {\n      return false\n    }\n\n    const increaseWidthCase =\n      (pushPasswordManagerStrategy === 'increase-width' ||\n        // TODO: remove 'experimental-no-flickering' support in 2.0.0\n        pushPasswordManagerStrategy === 'experimental-no-flickering') &&\n      hasPWMBadge &&\n      hasPWMBadgeSpace\n\n    return increaseWidthCase\n  }, [hasPWMBadge, hasPWMBadgeSpace, pushPasswordManagerStrategy])\n\n  const trackPWMBadge = React.useCallback(() => {\n    const container = containerRef.current\n    const input = inputRef.current\n    if (\n      !container ||\n      !input ||\n      done ||\n      pushPasswordManagerStrategy === 'none'\n    ) {\n      return\n    }\n\n    const elementToCompare = container\n\n    // Get the top right-center point of the container.\n    // That is usually where most password managers place their badge.\n    const rightCornerX =\n      elementToCompare.getBoundingClientRect().left +\n      elementToCompare.offsetWidth\n    const centereredY =\n      elementToCompare.getBoundingClientRect().top +\n      elementToCompare.offsetHeight / 2\n    const x = rightCornerX - PWM_BADGE_MARGIN_RIGHT\n    const y = centereredY\n\n    // Do an extra search to check for famous password managers\n    const pmws = document.querySelectorAll(PASSWORD_MANAGERS_SELECTORS)\n\n    // If no password manager is automatically detect,\n    // we'll try to dispatch document.elementFromPoint\n    // to identify badges\n    if (pmws.length === 0) {\n      const maybeBadgeEl = document.elementFromPoint(x, y)\n\n      // If the found element is the input itself,\n      // then we assume it's not a password manager badge.\n      // We are not sure. Most times that means there isn't a badge.\n      if (maybeBadgeEl === container) {\n        return\n      }\n    }\n\n    setHasPWMBadge(true)\n    setDone(true)\n  }, [containerRef, inputRef, done, pushPasswordManagerStrategy])\n\n  React.useEffect(() => {\n    const container = containerRef.current\n    if (!container || pushPasswordManagerStrategy === 'none') {\n      return\n    }\n\n    // Check if the PWM area is 100% visible\n    function checkHasSpace() {\n      const viewportWidth = window.innerWidth\n      const distanceToRightEdge =\n        viewportWidth - container.getBoundingClientRect().right\n      setHasPWMBadgeSpace(distanceToRightEdge >= PWM_BADGE_SPACE_WIDTH_PX)\n    }\n\n    checkHasSpace()\n    const interval = setInterval(checkHasSpace, 1000)\n\n    return () => {\n      clearInterval(interval)\n    }\n  }, [containerRef, pushPasswordManagerStrategy])\n\n  React.useEffect(() => {\n    const _isFocused = isFocused || document.activeElement === inputRef.current\n\n    if (pushPasswordManagerStrategy === 'none' || !_isFocused) {\n      return\n    }\n    const t1 = setTimeout(trackPWMBadge, 0)\n    const t2 = setTimeout(trackPWMBadge, 2000)\n    const t3 = setTimeout(trackPWMBadge, 5000)\n    const t4 = setTimeout(() => {\n      setDone(true)\n    }, 6000)\n    return () => {\n      clearTimeout(t1)\n      clearTimeout(t2)\n      clearTimeout(t3)\n      clearTimeout(t4)\n    }\n  }, [inputRef, isFocused, pushPasswordManagerStrategy, trackPWMBadge])\n\n  return { hasPWMBadge, willPushPWMBadge, PWM_BADGE_SPACE_WIDTH }\n}\n", "export const REGEXP_ONLY_DIGITS = '^\\\\d+$'\nexport const REGEXP_ONLY_CHARS = '^[a-zA-Z]+$'\nexport const REGEXP_ONLY_DIGITS_AND_CHARS = '^[a-zA-Z0-9]+$'\n"], "mappings": "qkCAAA,IAAAA,GAAA,GAAAC,GAAAD,GAAA,cAAAE,GAAA,oBAAAC,GAAA,sBAAAC,GAAA,uBAAAC,GAAA,iCAAAC,KAAA,eAAAC,GAAAP,ICEA,IAAAQ,EAAuB,qBCFhB,SAASC,GAAaC,EAA2C,CACtE,IAAMC,EAAK,WAAWD,EAAI,CAAC,EACrBE,EAAK,WAAWF,EAAI,EAAG,EACvBG,EAAK,WAAWH,EAAI,EAAG,EAC7B,MAAO,CAACC,EAAIC,EAAIC,CAAE,CACpB,CCLA,IAAAC,EAAuB,qBAEhB,SAASC,GAAeC,EAAU,CACvC,IAAMC,EAAY,SAAU,EAC5B,OAAM,YAAU,IAAM,CACpBA,EAAI,QAAUD,CAChB,CAAC,EACMC,EAAI,OACb,CCRA,IAAAC,EAAuB,qBAGjBC,GAAyB,GACzBC,GAA2B,GAC3BC,GAAwB,GAAGD,EAAwB,KAEnDE,GAA8B,CAClC,4BACA,uBACA,yBACA,mCACF,EAAE,KAAK,GAAG,EAEH,SAASC,GAAwB,CACtC,aAAAC,EACA,SAAAC,EACA,4BAAAC,EACA,UAAAC,CACF,EAKG,CAID,GAAM,CAACC,EAAaC,CAAc,EAAU,WAAS,EAAK,EACpD,CAACC,EAAkBC,CAAmB,EAAU,WAAS,EAAK,EAC9D,CAACC,EAAMC,CAAO,EAAU,WAAS,EAAK,EAEtCC,EAAyB,UAAQ,IACjCR,IAAgC,OAC3B,IAINA,IAAgC,kBAE/BA,IAAgC,+BAClCE,GACAE,EAGD,CAACF,EAAaE,EAAkBJ,CAA2B,CAAC,EAEzDS,EAAsB,cAAY,IAAM,CAC5C,IAAMC,EAAYZ,EAAa,QACzBa,EAAQZ,EAAS,QACvB,GACE,CAACW,GACD,CAACC,GACDL,GACAN,IAAgC,OAEhC,OAGF,IAAMY,EAAmBF,EAInBG,EACJD,EAAiB,sBAAsB,EAAE,KACzCA,EAAiB,YACbE,EACJF,EAAiB,sBAAsB,EAAE,IACzCA,EAAiB,aAAe,EAC5BG,EAAIF,EAAepB,GACnBuB,EAAIF,EAGG,SAAS,iBAAiBlB,EAA2B,EAKzD,SAAW,GACG,SAAS,iBAAiBmB,EAAGC,CAAC,IAK9BN,IAKvBP,EAAe,EAAI,EACnBI,EAAQ,EAAI,EACd,EAAG,CAACT,EAAcC,EAAUO,EAAMN,CAA2B,CAAC,EAE9D,OAAM,YAAU,IAAM,CACpB,IAAMU,EAAYZ,EAAa,QAC/B,GAAI,CAACY,GAAaV,IAAgC,OAChD,OAIF,SAASiB,GAAgB,CAEvB,IAAMC,EADgB,OAAO,WAEXR,EAAU,sBAAsB,EAAE,MACpDL,EAAoBa,GAAuBxB,EAAwB,CACrE,CAEAuB,EAAc,EACd,IAAME,EAAW,YAAYF,EAAe,GAAI,EAEhD,MAAO,IAAM,CACX,cAAcE,CAAQ,CACxB,CACF,EAAG,CAACrB,EAAcE,CAA2B,CAAC,EAExC,YAAU,IAAM,CACpB,IAAMoB,EAAanB,GAAa,SAAS,gBAAkBF,EAAS,QAEpE,GAAIC,IAAgC,QAAU,CAACoB,EAC7C,OAEF,IAAMC,EAAK,WAAWZ,EAAe,CAAC,EAChCa,EAAK,WAAWb,EAAe,GAAI,EACnCc,EAAK,WAAWd,EAAe,GAAI,EACnCe,EAAK,WAAW,IAAM,CAC1BjB,EAAQ,EAAI,CACd,EAAG,GAAI,EACP,MAAO,IAAM,CACX,aAAac,CAAE,EACf,aAAaC,CAAE,EACf,aAAaC,CAAE,EACf,aAAaC,CAAE,CACjB,CACF,EAAG,CAACzB,EAAUE,EAAWD,EAA6BS,CAAa,CAAC,EAE7D,CAAE,YAAAP,EAAa,iBAAAM,EAAkB,sBAAAb,EAAsB,CAChE,CH/HO,IAAM8B,GAAwB,gBACnC,CAAC,CACH,EAEaC,GAAiB,aAC5B,CACEC,EAmBAC,IACG,CApBH,IAAAC,EAAAF,EACE,OAAOG,EACP,SAAUC,EACV,UAAAC,EACA,UAAAC,EAAY,OACZ,QAAAC,EACA,YAAAC,EACA,UAAAC,EAAY,UACZ,WAAAC,EACA,4BAAAC,EAA8B,iBAC9B,iBAAAC,EACA,mBAAAC,EACA,oBAAAC,EAAsBC,GAEtB,OAAAC,EACA,SAAAC,CA9BN,EAeIf,EAiBKgB,EAAAC,GAjBLjB,EAiBK,CAhBH,QACA,WACA,YACA,YACA,UACA,cACA,YACA,aACA,8BACA,mBACA,qBACA,sBAEA,SACA,aA9BN,IAAAF,EAAAE,GAAAkB,GAAAC,GAAAC,GAqCI,GAAM,CAACC,EAAeC,EAAgB,EAAU,WAC9C,OAAON,EAAM,cAAiB,SAAWA,EAAM,aAAe,EAChE,EAGMO,EAAQtB,GAAA,KAAAA,EAAkBoB,EAC1BG,EAAgBC,GAAYF,CAAK,EACjCG,EAAiB,cACpBC,GAAqB,CACpBzB,GAAA,MAAAA,EAAoByB,GACpBL,GAAiBK,CAAQ,CAC3B,EACA,CAACzB,CAAiB,CACpB,EACM0B,EAAe,UACnB,IACEvB,EACI,OAAOA,GAAY,SACjB,IAAI,OAAOA,CAAO,EAClBA,EACF,KACN,CAACA,CAAO,CACV,EAGMwB,EAAiB,SAAyB,IAAI,EAC9CC,EAAqB,SAAuB,IAAI,EAChDC,EAAuB,SAAO,CAClC,MAAAR,EACA,SAAAG,EACA,MACE,OAAO,QAAW,eAClB1B,IAAAF,EAAA,2BAAQ,MAAR,YAAAA,EAAa,WAAb,YAAAE,GAAA,KAAAF,EAAwB,wBAAyB,QACrD,CAAC,EACKkC,EAAyB,SAE5B,CACD,KAAM,EACJd,GAAAW,EAAS,UAAT,YAAAX,GAAkB,gBAClBC,GAAAU,EAAS,UAAT,YAAAV,GAAkB,cAClBC,GAAAS,EAAS,UAAT,YAAAT,GAAkB,kBACpB,CACF,CAAC,EACK,sBAAoBrB,EAAK,IAAM8B,EAAS,QAAS,CAAC,CAAC,EACnD,YAAU,IAAM,CACpB,IAAMI,EAAQJ,EAAS,QACjBK,EAAYJ,EAAa,QAE/B,GAAI,CAACG,GAAS,CAACC,EACb,OAIEH,EAAe,QAAQ,QAAUE,EAAM,OACzCF,EAAe,QAAQ,SAASE,EAAM,KAAK,EAI7CD,EAAiB,QAAQ,KAAO,CAC9BC,EAAM,eACNA,EAAM,aACNA,EAAM,kBACR,EACA,SAASE,GAA4B,CACnC,GAAI,SAAS,gBAAkBF,EAAO,CACpCG,EAAwB,IAAI,EAC5BC,EAAsB,IAAI,EAC1B,MACF,CAGA,IAAMC,EAAKL,EAAM,eACXb,EAAKa,EAAM,aACXM,GAAON,EAAM,mBACbO,EAAMP,EAAM,UACZQ,EAAOR,EAAM,MACbS,EAAQV,EAAiB,QAAQ,KAGnCW,EAAQ,GACRC,EAAM,GACNC,EACJ,GAAIJ,EAAK,SAAW,GAAKH,IAAO,MAAQlB,IAAO,KAAM,CACnD,IAAM0B,GAAgBR,IAAOlB,EACvB2B,GAAeT,IAAOG,EAAK,QAAUA,EAAK,OAASD,EAEzD,GAAIM,IAAiB,CAACC,GAAc,CAClC,IAAMC,EAAIV,EACV,GAAIU,IAAM,EACRL,EAAQ,EACRC,EAAM,EACNC,EAAY,kBACHG,IAAMR,EACfG,EAAQK,EAAI,EACZJ,EAAMI,EACNH,EAAY,mBACHL,EAAM,GAAKC,EAAK,OAAS,EAAG,CACrC,IAAIQ,GAAS,EACb,GAAIP,EAAM,CAAC,IAAM,MAAQA,EAAM,CAAC,IAAM,KAAM,CAC1CG,EAAYG,EAAIN,EAAM,CAAC,EAAI,WAAa,UACxC,IAAMQ,GACJR,EAAM,CAAC,IAAMA,EAAM,CAAC,GAAKA,EAAM,CAAC,EAAIF,EAClCK,IAAc,YAAc,CAACK,KAC/BD,GAAS,GAEb,CAEAN,EAAQM,GAASD,EACjBJ,EAAMK,GAASD,EAAI,CACrB,CACF,CAEIL,IAAU,IAAMC,IAAQ,IAAMD,IAAUC,GAC1Cf,EAAS,QAAQ,kBAAkBc,EAAOC,EAAKC,CAAS,CAE5D,CAGA,IAAMM,GAAIR,IAAU,GAAKA,EAAQL,EAC3Bc,GAAIR,IAAQ,GAAKA,EAAMxB,EACvBiC,GAAMR,GAAA,KAAAA,EAAaN,GACzBH,EAAwBe,EAAC,EACzBd,EAAsBe,EAAC,EAEvBpB,EAAiB,QAAQ,KAAO,CAACmB,GAAGC,GAAGC,EAAG,CAC5C,CAUA,GATA,SAAS,iBAAiB,kBAAmBlB,EAA2B,CACtE,QAAS,EACX,CAAC,EAGDA,EAA0B,EAC1B,SAAS,gBAAkBF,GAASqB,GAAa,EAAI,EAGjD,CAAC,SAAS,eAAe,iBAAiB,EAAG,CAC/C,IAAMC,EAAU,SAAS,cAAc,OAAO,EAI9C,GAHAA,EAAQ,GAAK,kBACb,SAAS,KAAK,YAAYA,CAAO,EAE7BA,EAAQ,MAAO,CACjB,IAAMC,EACJ,qPAEFC,EACEF,EAAQ,MACR,oGACF,EACAE,EACEF,EAAQ,MACR,+BAA+BC,CAAc,IAC/C,EACAC,EACEF,EAAQ,MACR,uCAAuCC,CAAc,IACvD,EAEAC,EACEF,EAAQ,MACR,oPACF,EAEAE,EACEF,EAAQ,MACR,0DACF,CACF,CACF,CAEA,IAAMG,EAAmB,IAAM,CACzBxB,GACFA,EAAU,MAAM,YACd,gBACA,GAAGD,EAAM,YAAY,IACvB,CAEJ,EACAyB,EAAiB,EACjB,IAAMC,EAAiB,IAAI,eAAeD,CAAgB,EAC1D,OAAAC,EAAe,QAAQ1B,CAAK,EAErB,IAAM,CACX,SAAS,oBACP,kBACAE,EACA,CAAE,QAAS,EAAK,CAClB,EACAwB,EAAe,WAAW,CAC5B,CACF,EAAG,CAAC,CAAC,EAGL,GAAM,CAACC,GAAiBC,EAAkB,EAAU,WAAS,EAAK,EAC5D,CAACC,EAAWR,EAAY,EAAU,WAAS,EAAK,EAChD,CAACS,EAAsB3B,CAAuB,EAAU,WAE5D,IAAI,EACA,CAAC4B,EAAoB3B,CAAqB,EAAU,WAExD,IAAI,EAGA,YAAU,IAAM,CACpB4B,GAAa,IAAM,CAhPzB,IAAAnE,EAAAE,EAAAkB,EAAAC,GAkPQrB,EAAA+B,EAAS,UAAT,MAAA/B,EAAkB,cAAc,IAAI,MAAM,OAAO,GAGjD,IAAMqD,GAAInD,EAAA6B,EAAS,UAAT,YAAA7B,EAAkB,eACtBoD,GAAIlC,EAAAW,EAAS,UAAT,YAAAX,EAAkB,aACtBmC,GAAMlC,EAAAU,EAAS,UAAT,YAAAV,EAAkB,mBAC1BgC,IAAM,MAAQC,IAAM,OACtBhB,EAAwBe,CAAC,EACzBd,EAAsBe,CAAC,EACvBpB,EAAiB,QAAQ,KAAO,CAACmB,EAAGC,EAAGC,CAAG,EAE9C,CAAC,CACH,EAAG,CAAC9B,EAAOuC,CAAS,CAAC,EAEf,YAAU,IAAM,CAChBtC,IAAkB,QAKpBD,IAAUC,GACVA,EAAc,OAASrB,GACvBoB,EAAM,SAAWpB,IAEjBK,GAAA,MAAAA,EAAae,GAEjB,EAAG,CAACpB,EAAWK,EAAYgB,EAAeD,CAAK,CAAC,EAEhD,IAAM2C,EAAOC,GAAwB,CACnC,aAAArC,EACA,SAAAD,EACA,4BAAApB,EACA,UAAAqD,CACF,CAAC,EAGKM,GAAwB,cAC3BhB,GAA2C,CAC1C,IAAMzB,EAAWyB,EAAE,cAAc,MAAM,MAAM,EAAGjD,CAAS,EACzD,GAAIwB,EAAS,OAAS,GAAKC,GAAU,CAACA,EAAO,KAAKD,CAAQ,EAAG,CAC3DyB,EAAE,eAAe,EACjB,MACF,CAEE,OAAO5B,GAAkB,UACzBG,EAAS,OAASH,EAAc,QAMhC,SAAS,cAAc,IAAI,MAAM,iBAAiB,CAAC,EAErDE,EAASC,CAAQ,CACnB,EACA,CAACxB,EAAWuB,EAAUF,EAAeI,CAAM,CAC7C,EACMyC,GAAuB,cAAY,IAAM,CA3SnD,IAAAvE,EA4SM,GAAI+B,EAAS,QAAS,CACpB,IAAMc,EAAQ,KAAK,IAAId,EAAS,QAAQ,MAAM,OAAQ1B,EAAY,CAAC,EAC7DyC,EAAMf,EAAS,QAAQ,MAAM,QACnC/B,EAAA+B,EAAS,UAAT,MAAA/B,EAAkB,kBAAkB6C,EAAOC,GAC3CR,EAAwBO,CAAK,EAC7BN,EAAsBO,CAAG,CAC3B,CACAU,GAAa,EAAI,CACnB,EAAG,CAACnD,CAAS,CAAC,EAERmE,GAAuB,cAC1BlB,GAA8C,CAvTrD,IAAAtD,EAAAE,EAwTQ,IAAMiC,EAAQJ,EAAS,QACvB,GAAI,CAACnB,IAAqB,CAACqB,EAAe,QAAQ,OAAS,CAACqB,EAAE,eAAiB,CAACnB,GAC9E,OAGF,IAAMsC,EAAWnB,EAAE,cAAc,QAAQ,YAAY,EAC/CoB,EAAU9D,EACZA,EAAiB6D,CAAQ,EACzBA,EACJnB,EAAE,eAAe,EAEjB,IAAMT,GAAQ7C,EAAA+B,EAAS,UAAT,YAAA/B,EAAkB,eAC1B8C,GAAM5C,EAAA6B,EAAS,UAAT,YAAA7B,EAAkB,aAOxB2B,GALcgB,IAAUC,EAG1BrB,EAAM,MAAM,EAAGoB,CAAK,EAAI6B,EAAUjD,EAAM,MAAMqB,CAAG,EACjDrB,EAAM,MAAM,EAAGoB,CAAK,EAAI6B,EAAUjD,EAAM,MAAMoB,CAAK,GACrB,MAAM,EAAGxC,CAAS,EAEpD,GAAIwB,EAAS,OAAS,GAAKC,GAAU,CAACA,EAAO,KAAKD,CAAQ,EACxD,OAGFM,EAAM,MAAQN,EACdD,EAASC,CAAQ,EAEjB,IAAM8C,EAAS,KAAK,IAAI9C,EAAS,OAAQxB,EAAY,CAAC,EAChDuE,EAAO/C,EAAS,OAEtBM,EAAM,kBAAkBwC,EAAQC,CAAI,EACpCtC,EAAwBqC,CAAM,EAC9BpC,EAAsBqC,CAAI,CAC5B,EACA,CAACvE,EAAWuB,EAAUE,EAAQL,CAAK,CACrC,EAGMoD,GAAkB,UACtB,KAAO,CACL,SAAU,WACV,OAAQ3D,EAAM,SAAW,UAAY,OACrC,WAAY,OACZ,iBAAkB,OAClB,cAAe,MACjB,GACA,CAACA,EAAM,QAAQ,CACjB,EAEM4D,GAAmB,UACvB,KAAO,CACL,SAAU,WACV,MAAO,EACP,MAAOV,EAAK,iBACR,eAAeA,EAAK,qBAAqB,IACzC,OACJ,SAAUA,EAAK,iBACX,WAAWA,EAAK,qBAAqB,QACrC,OACJ,OAAQ,OACR,QAAS,OACT,UAAA9D,EACA,QAAS,IACT,MAAO,cACP,cAAe,MACf,WAAY,cACZ,WAAY,cACZ,OAAQ,sBACR,QAAS,sBACT,UAAW,OACX,WAAY,IACZ,cAAe,QACf,SAAU,qBACV,WAAY,YACZ,mBAAoB,cAgBtB,GACA,CAAC8D,EAAK,sBAAuBA,EAAK,iBAAkB9D,CAAS,CAC/D,EAGMyE,GAAsB,UAC1B,IACE,gBAAC,QAAAC,GAAAC,GAAA,CACC,aAAc/D,EAAM,cAAgB,iBAChCA,GAFL,CAGC,iBAAc,GACd,mCAAkCO,EAAM,SAAW,GAAK,OACxD,qBAAoBwC,EACpB,qBAAoBC,EACpB,UAAWzD,EACX,QAASqB,GAAA,YAAAA,EAAQ,OACjB,mBAAkBtB,EAClB,MAAOsE,GACP,UAAWzE,EACX,MAAOoB,EACP,IAAKM,EACL,QAASuB,GAAK,CAxaxB,IAAAtD,EAyaYwE,GAAelB,CAAC,GAChBtD,EAAAkB,EAAM,UAAN,MAAAlB,EAAA,KAAAkB,EAAgBoC,EAClB,EACA,SAAUgB,GACV,YAAahB,GAAK,CA7a5B,IAAAtD,EA8aY+D,GAAmB,EAAI,GACvB/D,EAAAkB,EAAM,cAAN,MAAAlB,EAAA,KAAAkB,EAAoBoC,EACtB,EACA,aAAcA,GAAK,CAjb7B,IAAAtD,EAkbY+D,GAAmB,EAAK,GACxB/D,EAAAkB,EAAM,eAAN,MAAAlB,EAAA,KAAAkB,EAAqBoC,EACvB,EACA,QAASA,GAAK,CArbxB,IAAAtD,EAsbYuE,GAAe,GACfvE,EAAAkB,EAAM,UAAN,MAAAlB,EAAA,KAAAkB,EAAgBoC,EAClB,EACA,OAAQA,GAAK,CAzbvB,IAAAtD,EA0bYwD,GAAa,EAAK,GAClBxD,EAAAkB,EAAM,SAAN,MAAAlB,EAAA,KAAAkB,EAAeoC,EACjB,GACF,EAEF,CACEgB,GACAC,GACAC,GACA/D,EACAqE,GACAzE,EACA6D,EACAD,EACA/C,EACAY,GAAA,YAAAA,EAAQ,OACRL,CACF,CACF,EAEMyD,GAAqB,UAAqB,KACvC,CACL,MAAO,MAAM,KAAK,CAAE,OAAQ7E,CAAU,CAAC,EAAE,IAAI,CAAC8E,EAAGC,IAAY,CAhdrE,IAAApF,EAidU,IAAMqF,EACJrB,GACAC,IAAyB,MACzBC,IAAuB,OACrBD,IAAyBC,GACzBkB,IAAYnB,GACXmB,GAAWnB,GAAwBmB,EAAUlB,GAE5CoB,EAAO7D,EAAM2D,CAAO,IAAM,OAAY3D,EAAM2D,CAAO,EAAI,KACvDG,EAAkB9D,EAAM,CAAC,IAAM,OAAY,MAAOzB,EAAAQ,GAAA,YAAAA,EAAc4E,KAAd,KAAApF,EAA0B,KAElF,MAAO,CACL,KAAAsF,EACA,gBAAAC,EACA,SAAAF,EACA,aAAcA,GAAYC,IAAS,IACrC,CACF,CAAC,EACD,UAAAtB,EACA,WAAY,CAAC9C,EAAM,UAAY4C,EACjC,GACC,CACDE,EACAF,GACAzD,EACA6D,EACAD,EACA/C,EAAM,SACNO,CACF,CAAC,EAEK+D,GAAyB,UAAQ,IACjCxE,EACKA,EAAOkE,EAAY,EAG1B,gBAACpF,GAAgB,SAAhB,CAAyB,MAAOoF,IAC9BjE,CACH,EAED,CAACA,EAAUiE,GAAclE,CAAM,CAAC,EAEnC,OACE,gCACGF,IAAwB,MACvB,gBAAC,gBACC,gBAAC,aAAOA,CAAoB,CAC9B,EAGF,gBAAC,OACC,IAAKkB,EACL,2BAAwB,GACxB,MAAO6C,GACP,UAAWhE,GAEV2E,GAED,gBAAC,OACC,MAAO,CACL,SAAU,WACV,MAAO,EACP,cAAe,MACjB,GAECT,EACH,CACF,CACF,CAEJ,CACF,EACAhF,GAAS,YAAc,QAEvB,SAAS4D,EAAe8B,EAAsBC,EAAc,CAC1D,GAAI,CACFD,EAAM,WAAWC,CAAI,CACvB,OAAQpC,EAAA,CACN,QAAQ,MAAM,uCAAwCoC,CAAI,CAC5D,CACF,CAOA,IAAM3E,GAAwB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;GIxiBvB,IAAM4E,GAAqB,SACrBC,GAAoB,cACpBC,GAA+B", "names": ["src_exports", "__export", "OTPInput", "OTPInputContext", "REGEXP_ONLY_CHARS", "REGEXP_ONLY_DIGITS", "REGEXP_ONLY_DIGITS_AND_CHARS", "__toCommonJS", "React", "syncTimeouts", "cb", "t1", "t2", "t3", "React", "usePrevious", "value", "ref", "React", "PWM_BADGE_MARGIN_RIGHT", "PWM_BADGE_SPACE_WIDTH_PX", "PWM_BADGE_SPACE_WIDTH", "PASSWORD_MANAGERS_SELECTORS", "usePasswordManagerBadge", "containerRef", "inputRef", "pushPasswordManagerStrategy", "isFocused", "hasPWMBadge", "setHasPWMBadge", "hasPWMBadgeSpace", "setHasPWMBadgeSpace", "done", "setDone", "willPushPWMBadge", "trackPWMBadge", "container", "input", "elementToCompare", "rightCornerX", "centereredY", "x", "y", "checkHasSpace", "distanceToRightEdge", "interval", "_isFocused", "t1", "t2", "t3", "t4", "OTPInputContext", "OTPInput", "_a", "ref", "_b", "uncheckedValue", "uncheckedOnChange", "max<PERSON><PERSON><PERSON>", "textAlign", "pattern", "placeholder", "inputMode", "onComplete", "pushPasswordManagerStrategy", "pasteTransformer", "containerClassName", "noScriptCSSFallback", "NOSCRIPT_CSS_FALLBACK", "render", "children", "props", "__objRest", "_c", "_d", "_e", "internalValue", "setInternalValue", "value", "previousValue", "usePrevious", "onChange", "newValue", "regexp", "inputRef", "containerRef", "initialLoadRef", "inputMetadataRef", "input", "container", "onDocumentSelectionChange", "setMirrorSelectionStart", "setMirrorSelectionEnd", "_s", "_dir", "_ml", "_val", "_prev", "start", "end", "direction", "isSingleCaret", "isInsertMode", "c", "offset", "wasPreviouslyInserting", "s", "e", "dir", "setIsFocused", "styleEl", "autofillStyles", "safeInsertRule", "updateRootHeight", "resizeObserver", "isHoveringInput", "setIsHoveringInput", "isFocused", "mirrorSelectionStart", "mirrorSelectionEnd", "syncTimeouts", "pwmb", "usePasswordManagerBadge", "_changeListener", "_focusListener", "_pasteListener", "_content", "content", "_start", "_end", "rootStyle", "inputStyle", "renderedInput", "__spreadProps", "__spreadValues", "contextValue", "_", "slotIdx", "isActive", "char", "placeholder<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sheet", "rule", "REGEXP_ONLY_DIGITS", "REGEXP_ONLY_CHARS", "REGEXP_ONLY_DIGITS_AND_CHARS"]}