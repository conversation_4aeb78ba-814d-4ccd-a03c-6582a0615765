var x=function(J){return x=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(G){return typeof G}:function(G){return G&&typeof Symbol=="function"&&G.constructor===Symbol&&G!==Symbol.prototype?"symbol":typeof G},x(J)},D=function(J,G){var O=Object.keys(J);if(Object.getOwnPropertySymbols){var Z=Object.getOwnPropertySymbols(J);G&&(Z=Z.filter(function(K){return Object.getOwnPropertyDescriptor(J,K).enumerable})),O.push.apply(O,Z)}return O},N=function(J){for(var G=1;G<arguments.length;G++){var O=arguments[G]!=null?arguments[G]:{};G%2?D(Object(O),!0).forEach(function(Z){t(J,Z,O[Z])}):Object.getOwnPropertyDescriptors?Object.defineProperties(J,Object.getOwnPropertyDescriptors(O)):D(Object(O)).forEach(function(Z){Object.defineProperty(J,Z,Object.getOwnPropertyDescriptor(O,Z))})}return J},t=function(J,G,O){if(G=H5(G),G in J)Object.defineProperty(J,G,{value:O,enumerable:!0,configurable:!0,writable:!0});else J[G]=O;return J},H5=function(J){var G=U5(J,"string");return x(G)=="symbol"?G:String(G)},U5=function(J,G){if(x(J)!="object"||!J)return J;var O=J[Symbol.toPrimitive];if(O!==void 0){var Z=O.call(J,G||"default");if(x(Z)!="object")return Z;throw new TypeError("@@toPrimitive must return a primitive value.")}return(G==="string"?String:Number)(J)};(function(J){var G=Object.defineProperty,O=function H(B,U){for(var C in U)G(B,C,{get:U[C],enumerable:!0,configurable:!0,set:function X(Y){return U[C]=function(){return Y}}})},Z={lessThanXSeconds:{one:"mindre enn ett sekund",other:"mindre enn {{count}} sekunder"},xSeconds:{one:"ett sekund",other:"{{count}} sekunder"},halfAMinute:"et halvt minutt",lessThanXMinutes:{one:"mindre enn ett minutt",other:"mindre enn {{count}} minutter"},xMinutes:{one:"ett minutt",other:"{{count}} minutter"},aboutXHours:{one:"omtrent en time",other:"omtrent {{count}} timer"},xHours:{one:"en time",other:"{{count}} timer"},xDays:{one:"en dag",other:"{{count}} dager"},aboutXWeeks:{one:"omtrent en uke",other:"omtrent {{count}} uker"},xWeeks:{one:"en uke",other:"{{count}} uker"},aboutXMonths:{one:"omtrent en m\xE5ned",other:"omtrent {{count}} m\xE5neder"},xMonths:{one:"en m\xE5ned",other:"{{count}} m\xE5neder"},aboutXYears:{one:"omtrent ett \xE5r",other:"omtrent {{count}} \xE5r"},xYears:{one:"ett \xE5r",other:"{{count}} \xE5r"},overXYears:{one:"over ett \xE5r",other:"over {{count}} \xE5r"},almostXYears:{one:"nesten ett \xE5r",other:"nesten {{count}} \xE5r"}},K=function H(B,U,C){var X,Y=Z[B];if(typeof Y==="string")X=Y;else if(U===1)X=Y.one;else X=Y.other.replace("{{count}}",String(U));if(C!==null&&C!==void 0&&C.addSuffix)if(C.comparison&&C.comparison>0)return"om "+X;else return X+" siden";return X};function W(H){return function(){var B=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},U=B.width?String(B.width):H.defaultWidth,C=H.formats[U]||H.formats[H.defaultWidth];return C}}var S={full:"EEEE d. MMMM y",long:"d. MMMM y",medium:"d. MMM y",short:"dd.MM.y"},$={full:"'kl'. HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},M={full:"{{date}} 'kl.' {{time}}",long:"{{date}} 'kl.' {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},R={date:W({formats:S,defaultWidth:"full"}),time:W({formats:$,defaultWidth:"full"}),dateTime:W({formats:M,defaultWidth:"full"})},L={lastWeek:"'forrige' eeee 'kl.' p",yesterday:"'i g\xE5r kl.' p",today:"'i dag kl.' p",tomorrow:"'i morgen kl.' p",nextWeek:"EEEE 'kl.' p",other:"P"},f=function H(B,U,C,X){return L[B]};function Q(H){return function(B,U){var C=U!==null&&U!==void 0&&U.context?String(U.context):"standalone",X;if(C==="formatting"&&H.formattingValues){var Y=H.defaultFormattingWidth||H.defaultWidth,E=U!==null&&U!==void 0&&U.width?String(U.width):Y;X=H.formattingValues[E]||H.formattingValues[Y]}else{var I=H.defaultWidth,A=U!==null&&U!==void 0&&U.width?String(U.width):H.defaultWidth;X=H.values[A]||H.values[I]}var T=H.argumentCallback?H.argumentCallback(B):B;return X[T]}}var V={narrow:["f.Kr.","e.Kr."],abbreviated:["f.Kr.","e.Kr."],wide:["f\xF8r Kristus","etter Kristus"]},j={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1. kvartal","2. kvartal","3. kvartal","4. kvartal"]},v={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["jan.","feb.","mars","apr.","mai","juni","juli","aug.","sep.","okt.","nov.","des."],wide:["januar","februar","mars","april","mai","juni","juli","august","september","oktober","november","desember"]},P={narrow:["S","M","T","O","T","F","L"],short:["s\xF8","ma","ti","on","to","fr","l\xF8"],abbreviated:["s\xF8n","man","tir","ons","tor","fre","l\xF8r"],wide:["s\xF8ndag","mandag","tirsdag","onsdag","torsdag","fredag","l\xF8rdag"]},w={narrow:{am:"a",pm:"p",midnight:"midnatt",noon:"middag",morning:"p\xE5 morg.",afternoon:"p\xE5 etterm.",evening:"p\xE5 kvelden",night:"p\xE5 natten"},abbreviated:{am:"a.m.",pm:"p.m.",midnight:"midnatt",noon:"middag",morning:"p\xE5 morg.",afternoon:"p\xE5 etterm.",evening:"p\xE5 kvelden",night:"p\xE5 natten"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnatt",noon:"middag",morning:"p\xE5 morgenen",afternoon:"p\xE5 ettermiddagen",evening:"p\xE5 kvelden",night:"p\xE5 natten"}},F=function H(B,U){var C=Number(B);return C+"."},_={ordinalNumber:F,era:Q({values:V,defaultWidth:"wide"}),quarter:Q({values:j,defaultWidth:"wide",argumentCallback:function H(B){return B-1}}),month:Q({values:v,defaultWidth:"wide"}),day:Q({values:P,defaultWidth:"wide"}),dayPeriod:Q({values:w,defaultWidth:"wide"})};function q(H){return function(B){var U=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},C=U.width,X=C&&H.matchPatterns[C]||H.matchPatterns[H.defaultMatchWidth],Y=B.match(X);if(!Y)return null;var E=Y[0],I=C&&H.parsePatterns[C]||H.parsePatterns[H.defaultParseWidth],A=Array.isArray(I)?k(I,function(z){return z.test(E)}):b(I,function(z){return z.test(E)}),T;T=H.valueCallback?H.valueCallback(A):A,T=U.valueCallback?U.valueCallback(T):T;var e=B.slice(E.length);return{value:T,rest:e}}}var b=function H(B,U){for(var C in B)if(Object.prototype.hasOwnProperty.call(B,C)&&U(B[C]))return C;return},k=function H(B,U){for(var C=0;C<B.length;C++)if(U(B[C]))return C;return};function m(H){return function(B){var U=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},C=B.match(H.matchPattern);if(!C)return null;var X=C[0],Y=B.match(H.parsePattern);if(!Y)return null;var E=H.valueCallback?H.valueCallback(Y[0]):Y[0];E=U.valueCallback?U.valueCallback(E):E;var I=B.slice(X.length);return{value:E,rest:I}}}var h=/^(\d+)\.?/i,c=/\d+/i,y={narrow:/^(f\.? ?Kr\.?|fvt\.?|e\.? ?Kr\.?|evt\.?)/i,abbreviated:/^(f\.? ?Kr\.?|fvt\.?|e\.? ?Kr\.?|evt\.?)/i,wide:/^(før Kristus|før vår tid|etter Kristus|vår tid)/i},p={any:[/^f/i,/^e/i]},g={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](\.)? kvartal/i},d={any:[/1/i,/2/i,/3/i,/4/i]},u={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mars?|apr|mai|juni?|juli?|aug|sep|okt|nov|des)\.?/i,wide:/^(januar|februar|mars|april|mai|juni|juli|august|september|oktober|november|desember)/i},l={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^mai/i,/^jun/i,/^jul/i,/^aug/i,/^s/i,/^o/i,/^n/i,/^d/i]},i={narrow:/^[smtofl]/i,short:/^(sø|ma|ti|on|to|fr|lø)/i,abbreviated:/^(søn|man|tir|ons|tor|fre|lør)/i,wide:/^(søndag|mandag|tirsdag|onsdag|torsdag|fredag|lørdag)/i},n={any:[/^s/i,/^m/i,/^ti/i,/^o/i,/^to/i,/^f/i,/^l/i]},s={narrow:/^(midnatt|middag|(på) (morgenen|ettermiddagen|kvelden|natten)|[ap])/i,any:/^([ap]\.?\s?m\.?|midnatt|middag|(på) (morgenen|ettermiddagen|kvelden|natten))/i},o={any:{am:/^a(\.?\s?m\.?)?$/i,pm:/^p(\.?\s?m\.?)?$/i,midnight:/^midn/i,noon:/^midd/i,morning:/morgen/i,afternoon:/ettermiddag/i,evening:/kveld/i,night:/natt/i}},r={ordinalNumber:m({matchPattern:h,parsePattern:c,valueCallback:function H(B){return parseInt(B,10)}}),era:q({matchPatterns:y,defaultMatchWidth:"wide",parsePatterns:p,defaultParseWidth:"any"}),quarter:q({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:d,defaultParseWidth:"any",valueCallback:function H(B){return B+1}}),month:q({matchPatterns:u,defaultMatchWidth:"wide",parsePatterns:l,defaultParseWidth:"any"}),day:q({matchPatterns:i,defaultMatchWidth:"wide",parsePatterns:n,defaultParseWidth:"any"}),dayPeriod:q({matchPatterns:s,defaultMatchWidth:"any",parsePatterns:o,defaultParseWidth:"any"})},a={code:"nb",formatDistance:K,formatLong:R,formatRelative:f,localize:_,match:r,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=N(N({},window.dateFns),{},{locale:N(N({},(J=window.dateFns)===null||J===void 0?void 0:J.locale),{},{nb:a})})})();

//# debugId=0E6875D67EDCB05064756e2164756e21
