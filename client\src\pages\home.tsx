import { useQuery } from '@tanstack/react-query';
import { Link } from 'wouter';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ProductCard } from '@/components/product/product-card';
import { Hero } from '@/components/layout/Hero';
import { useLanguage } from '@/hooks/use-language';
import { Product, Category } from '@shared/schema';
import { ArrowRight } from 'lucide-react';

export default function Home() {
  const { t, language } = useLanguage();

  const { data: categories, isLoading: categoriesLoading } = useQuery({
    queryKey: ['/api/categories'],
    queryFn: async () => {
      const response = await fetch('/api/categories');
      return response.json() as Promise<Category[]>;
    },
  });

  const { data: productsData, isLoading: productsLoading } = useQuery({
    queryKey: ['/api/products', { isFeatured: true, limit: 6 }],
    queryFn: async () => {
      const response = await fetch('/api/products?isFeatured=true&limit=6');
      return response.json() as Promise<{ products: Product[]; total: number }>;
    },
  });

  const categoryImages = {
    marble:
      'https://images.unsplash.com/photo-1565043666747-69f6646db940?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=400&h=300',
    granite:
      'https://images.unsplash.com/photo-1634712282287-14ed57b9cc89?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=400&h=300',
    travertine:
      'https://images.unsplash.com/photo-1600298881974-6be191ceeda1?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=400&h=300',
    ceramic:
      'https://images.unsplash.com/photo-1584622650111-993a426fbf0a?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=400&h=300',
  };

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <Hero featuredProduct={productsData?.products?.[0]} isLoading={productsLoading} />

      {/* Categories Overview */}
      <section id="categories" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="font-playfair text-4xl lg:text-5xl font-bold text-charcoal mb-6">{t('categories.title')}</h2>
            <p className="text-xl text-medium-gray max-w-3xl mx-auto leading-relaxed">{t('categories.description')}</p>
          </div>

          {categoriesLoading ? (
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="bg-gray-200 h-48 rounded-xl mb-4"></div>
                  <div className="bg-gray-200 h-4 rounded mb-2"></div>
                  <div className="bg-gray-200 h-3 rounded"></div>
                </div>
              ))}
            </div>
          ) : (
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {categories?.map(category => {
                const categoryName = language === 'ar' && category.name_ar ? category.name_ar : category.name;
                const categoryDesc =
                  language === 'ar' && category.description_ar ? category.description_ar : category.description;
                const imageKey = category.slug as keyof typeof categoryImages;
                const categoryImage = category.image || categoryImages[imageKey] || categoryImages.marble;

                return (
                  <Link key={category.id} href={`/products?category=${category.id}`}>
                    <Card className="product-card group cursor-pointer overflow-hidden">
                      <div className="bg-white elegant-shadow ">
                        <img
                          src={categoryImage}
                          alt={categoryName}
                          className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-500"
                        />
                        <CardContent className="p-6">
                          <h3 className="font-playfair text-xl font-semibold text-charcoal mb-2">{categoryName}</h3>
                          <p className="text-medium-gray mb-4 h-10">{categoryDesc}</p>
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-gold font-medium">
                              {/* Product count would be added here */}
                              View Collection
                            </span>
                            <ArrowRight className="h-5 w-5 text-gold group-hover:translate-x-1 transition-transform duration-300" />
                          </div>
                        </CardContent>
                      </div>
                    </Card>
                  </Link>
                );
              })}
            </div>
          )}
        </div>
      </section>

      {/* Featured Products */}
      {productsData?.products && productsData.products.length > 0 && (
        <section className="py-20 luxury-gradient">
          <div className="max-w-7xl mx-auto px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="font-playfair text-4xl lg:text-5xl font-bold text-charcoal mb-6">Featured Products</h2>
              <p className="text-xl text-medium-gray max-w-3xl mx-auto leading-relaxed">
                Discover our handpicked selection of premium stones and ceramics
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {productsData.products.map(product => (
                <ProductCard
                  key={product.id}
                  product={product}
                  category={categories?.find(c => c.id === product.category_id)}
                />
              ))}
            </div>

            <div className="text-center mt-12">
              <Link href="/products">
                <Button
                  size="lg"
                  className="px-8 py-4 gold-gradient text-white font-semibold hover:shadow-lg transform hover:scale-105 transition-all duration-300">
                  View All Products
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
            </div>
          </div>
        </section>
      )}
    </div>
  );
}
