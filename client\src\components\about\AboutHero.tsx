import { useLanguage } from '@/hooks/use-language';

export function AboutHero() {
  const { t, language } = useLanguage();

  return (
    <section className="relative min-h-screen flex items-center justify-center pt-20 overflow-hidden">
      <div className="absolute inset-0 z-0">
        <div className="w-full h-full">
          <img
            src="https://images.unsplash.com/photo-1637649228998-6c78a67dfa6c?auto=format&fit=crop&w=1920&q=80"
            alt="Modern luxury stone showroom interior - <PERSON> on Unsplash"
            className="w-full h-full object-cover opacity-20 transform scale-105"
            width={1920}
            height={1080}
            loading="eager"
            decoding="async"
            style={{
              transform: 'translateZ(0)',
              backfaceVisibility: 'hidden',
              WebkitBackfaceVisibility: 'hidden',
              willChange: 'transform',
              transformStyle: 'preserve-3d',
              imageRendering: '-webkit-optimize-contrast',
            }}
          />
        </div>
        <div className="absolute inset-0 luxury-gradient opacity-95" style={{
          transform: 'translateZ(0)',
          backfaceVisibility: 'hidden',
          WebkitBackfaceVisibility: 'hidden',
        }}></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-6 lg:px-8">
        <div className="text-center">
          <h1 className="font-playfair text-5xl lg:text-7xl font-bold text-charcoal leading-tight mb-6">
            {t('about.title')}
          </h1>
          <p className="text-2xl lg:text-3xl text-gold font-medium mb-8">{t('about.subtitle')}</p>
          <p className="text-xl text-medium-gray leading-relaxed max-w-4xl mx-auto">{t('about.heroDescription')}</p>
        </div>
      </div>
    </section>
  );
}
