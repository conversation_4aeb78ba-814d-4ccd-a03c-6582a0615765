// server/index.ts
import "dotenv/config";
import express3 from "express";

// server/routes.ts
import express from "express";
import { createServer } from "http";

// server/supabase.ts
import { createClient } from "@supabase/supabase-js";
var supabaseUrl = process.env.SUPABASE_URL;
var supabaseAnonKey = process.env.SUPABASE_ANON_KEY;
if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error("Missing Supabase environment variables. Please check SUPABASE_URL and SUPABASE_ANON_KEY.");
}
var supabase = createClient(supabaseUrl, supabaseAnonKey);
var typedSupabase = createClient(supabaseUrl, supabaseAnonKey);

// shared/schema.ts
import { z } from "zod";
var userSchema = z.object({
  id: z.string().uuid().optional(),
  username: z.string().min(1),
  password: z.string().min(6),
  role: z.string().default("admin"),
  created_at: z.string().optional()
});
var categorySchema = z.object({
  id: z.string().uuid().optional(),
  name: z.string().min(1),
  name_ar: z.string().optional(),
  slug: z.string().min(1),
  description: z.string().optional(),
  description_ar: z.string().optional(),
  image: z.string().optional(),
  sort_order: z.number().default(0),
  is_active: z.boolean().default(true),
  created_at: z.string().optional(),
  updated_at: z.string().optional()
});
var productSchema = z.object({
  id: z.string().uuid().optional(),
  code: z.string().min(1),
  name: z.string().min(1),
  name_ar: z.string().optional(),
  category_id: z.string().uuid(),
  description: z.string().optional(),
  description_ar: z.string().optional(),
  full_description: z.string().optional(),
  full_description_ar: z.string().optional(),
  images: z.array(z.string()).default([]),
  specifications: z.record(z.string()).default({}),
  specifications_ar: z.record(z.string()).default({}),
  applications: z.array(z.string()).default([]),
  applications_ar: z.array(z.string()).default([]),
  material: z.string().optional(),
  origin: z.string().optional(),
  finish: z.string().optional(),
  thickness: z.string().optional(),
  availability: z.string().optional(),
  is_active: z.boolean().default(true),
  is_featured: z.boolean().default(false),
  sort_order: z.number().default(0),
  created_at: z.string().optional(),
  updated_at: z.string().optional()
});
var siteSettingSchema = z.object({
  id: z.string().uuid().optional(),
  key: z.string().min(1),
  value: z.any(),
  created_at: z.string().optional(),
  updated_at: z.string().optional()
});
var insertUserSchema = userSchema.omit({
  id: true,
  created_at: true
});
var insertCategorySchema = categorySchema.omit({
  id: true,
  created_at: true,
  updated_at: true
});
var insertProductSchema = productSchema.omit({
  id: true,
  created_at: true,
  updated_at: true
});
var insertSiteSettingSchema = siteSettingSchema.omit({
  id: true,
  created_at: true,
  updated_at: true
});

// server/routes.ts
import bcrypt from "bcrypt";
import jwt from "jsonwebtoken";
import multer from "multer";
import path from "path";
import fs from "fs";
var JWT_SECRET = process.env.JWT_SECRET || "your-secret-key";
var uploadDir = path.join(process.cwd(), "uploads");
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}
var storage_multer = multer.diskStorage({
  destination: function(req, file, cb) {
    cb(null, uploadDir);
  },
  filename: function(req, file, cb) {
    const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
    cb(null, file.fieldname + "-" + uniqueSuffix + path.extname(file.originalname));
  }
});
var upload = multer({
  storage: storage_multer,
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|webp|gif/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);
    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error("Only image files are allowed"));
    }
  },
  limits: { fileSize: 5 * 1024 * 1024 }
  // 5MB limit
});
var authenticateToken = (req, res, next) => {
  const authHeader = req.headers["authorization"];
  const token = authHeader && authHeader.split(" ")[1];
  if (!token) {
    return res.status(401).json({ message: "Access token required" });
  }
  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ message: "Invalid token" });
    }
    req.user = user;
    next();
  });
};
async function registerRoutes(app2) {
  app2.use("/uploads", express.static(uploadDir));
  app2.post("/api/auth/login", async (req, res) => {
    try {
      const { username, password } = req.body;
      if (!username || !password) {
        return res.status(400).json({ message: "Username and password required" });
      }
      const { data: user, error } = await supabase.from("users").select("*").eq("username", username).single();
      if (error || !user) {
        return res.status(401).json({ message: "Invalid credentials" });
      }
      const validPassword = await bcrypt.compare(password, user.password);
      if (!validPassword) {
        return res.status(401).json({ message: "Invalid credentials" });
      }
      const token = jwt.sign(
        { id: user.id, username: user.username, role: user.role },
        JWT_SECRET,
        { expiresIn: "24h" }
      );
      res.json({
        token,
        user: {
          id: user.id,
          username: user.username,
          role: user.role
        }
      });
    } catch (error) {
      console.error("Login error:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  });
  app2.post("/api/auth/register", async (req, res) => {
    try {
      const { username, password } = req.body;
      if (!username || !password) {
        return res.status(400).json({ message: "Username and password required" });
      }
      const { data: existingUser } = await supabase.from("users").select("id").eq("username", username).single();
      if (existingUser) {
        return res.status(400).json({ message: "Username already exists" });
      }
      const hashedPassword = await bcrypt.hash(password, 10);
      const { data: user, error } = await supabase.from("users").insert({
        username,
        password: hashedPassword,
        role: "admin"
      }).select().single();
      if (error) {
        throw error;
      }
      const token = jwt.sign(
        { id: user.id, username: user.username, role: user.role },
        JWT_SECRET,
        { expiresIn: "24h" }
      );
      res.status(201).json({
        token,
        user: {
          id: user.id,
          username: user.username,
          role: user.role
        }
      });
    } catch (error) {
      console.error("Registration error:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  });
  app2.get("/api/categories", async (req, res) => {
    try {
      const { data: categories, error } = await supabase.from("categories").select("*").eq("is_active", true).order("sort_order", { ascending: true });
      if (error) throw error;
      res.json(categories || []);
    } catch (error) {
      console.error("Get categories error:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  });
  app2.get("/api/categories/:id", async (req, res) => {
    try {
      const { data: category, error } = await supabase.from("categories").select("*").eq("id", req.params.id).single();
      if (error || !category) {
        return res.status(404).json({ message: "Category not found" });
      }
      res.json(category);
    } catch (error) {
      console.error("Get category error:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  });
  app2.post("/api/categories", authenticateToken, upload.single("image"), async (req, res) => {
    try {
      const categoryData = insertCategorySchema.parse({
        ...req.body,
        image: req.file ? `/uploads/${req.file.filename}` : void 0
      });
      const { data: category, error } = await supabase.from("categories").insert(categoryData).select().single();
      if (error) throw error;
      res.status(201).json(category);
    } catch (error) {
      console.error("Create category error:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  });
  app2.put("/api/categories/:id", authenticateToken, upload.single("image"), async (req, res) => {
    try {
      const categoryData = {
        ...req.body,
        image: req.file ? `/uploads/${req.file.filename}` : req.body.image,
        updated_at: (/* @__PURE__ */ new Date()).toISOString()
      };
      const { data: category, error } = await supabase.from("categories").update(categoryData).eq("id", req.params.id).select().single();
      if (error) throw error;
      res.json(category);
    } catch (error) {
      console.error("Update category error:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  });
  app2.delete("/api/categories/:id", authenticateToken, async (req, res) => {
    try {
      const { error } = await supabase.from("categories").delete().eq("id", req.params.id);
      if (error) throw error;
      res.status(204).send();
    } catch (error) {
      console.error("Delete category error:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  });
  app2.get("/api/products", async (req, res) => {
    try {
      let query = supabase.from("products").select("*, categories(name, name_ar, slug)", { count: "exact" }).eq("is_active", true);
      if (req.query.categoryId) {
        query = query.eq("category_id", req.query.categoryId);
      }
      if (req.query.search) {
        const searchTerm = req.query.search;
        query = query.or(`name.ilike.%${searchTerm}%,name_ar.ilike.%${searchTerm}%,code.ilike.%${searchTerm}%`);
      }
      if (req.query.isFeatured === "true") {
        query = query.eq("is_featured", true);
      }
      if (req.query.limit) {
        query = query.limit(parseInt(req.query.limit));
      }
      if (req.query.offset) {
        query = query.range(
          parseInt(req.query.offset),
          parseInt(req.query.offset) + (parseInt(req.query.limit) || 10) - 1
        );
      }
      query = query.order("sort_order", { ascending: true }).order("created_at", { ascending: false });
      const { data: products, error, count } = await query;
      if (error) throw error;
      res.json({ products: products || [], total: count || 0 });
    } catch (error) {
      console.error("Get products error:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  });
  app2.get("/api/products/:id", async (req, res) => {
    try {
      const { data: product, error } = await supabase.from("products").select("*, categories(name, name_ar, slug)").eq("id", req.params.id).single();
      if (error || !product) {
        return res.status(404).json({ message: "Product not found" });
      }
      res.json(product);
    } catch (error) {
      console.error("Get product error:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  });
  app2.post("/api/products", authenticateToken, upload.array("images", 10), async (req, res) => {
    try {
      const images = req.files ? req.files.map((file) => `/uploads/${file.filename}`) : [];
      const productData = insertProductSchema.parse({
        ...req.body,
        images,
        specifications: req.body.specifications ? JSON.parse(req.body.specifications) : {},
        specifications_ar: req.body.specificationsAr ? JSON.parse(req.body.specificationsAr) : {},
        applications: req.body.applications ? JSON.parse(req.body.applications) : [],
        applications_ar: req.body.applicationsAr ? JSON.parse(req.body.applicationsAr) : []
      });
      const { data: product, error } = await supabase.from("products").insert(productData).select().single();
      if (error) throw error;
      res.status(201).json(product);
    } catch (error) {
      console.error("Create product error:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  });
  app2.put("/api/products/:id", authenticateToken, upload.array("images", 10), async (req, res) => {
    try {
      const newImages = req.files ? req.files.map((file) => `/uploads/${file.filename}`) : [];
      const existingImages = req.body.existingImages ? JSON.parse(req.body.existingImages) : [];
      const images = [...existingImages, ...newImages];
      const productData = {
        ...req.body,
        images,
        specifications: req.body.specifications ? JSON.parse(req.body.specifications) : void 0,
        specifications_ar: req.body.specificationsAr ? JSON.parse(req.body.specificationsAr) : void 0,
        applications: req.body.applications ? JSON.parse(req.body.applications) : void 0,
        applications_ar: req.body.applicationsAr ? JSON.parse(req.body.applicationsAr) : void 0,
        updated_at: (/* @__PURE__ */ new Date()).toISOString()
      };
      const { data: product, error } = await supabase.from("products").update(productData).eq("id", req.params.id).select().single();
      if (error) throw error;
      res.json(product);
    } catch (error) {
      console.error("Update product error:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  });
  app2.delete("/api/products/:id", authenticateToken, async (req, res) => {
    try {
      const { error } = await supabase.from("products").delete().eq("id", req.params.id);
      if (error) throw error;
      res.status(204).send();
    } catch (error) {
      console.error("Delete product error:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  });
  app2.get("/api/settings", async (req, res) => {
    try {
      const { data: settings, error } = await supabase.from("site_settings").select("*");
      if (error) throw error;
      const settingsObj = (settings || []).reduce((acc, setting) => {
        acc[setting.key] = setting.value;
        return acc;
      }, {});
      res.json(settingsObj);
    } catch (error) {
      console.error("Get settings error:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  });
  app2.post("/api/settings", authenticateToken, async (req, res) => {
    try {
      const settingData = insertSiteSettingSchema.parse(req.body);
      const { data: setting, error } = await supabase.from("site_settings").upsert(settingData, { onConflict: "key" }).select().single();
      if (error) throw error;
      res.status(201).json(setting);
    } catch (error) {
      console.error("Create setting error:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  });
  app2.put("/api/settings/:key", authenticateToken, async (req, res) => {
    try {
      const { data: setting, error } = await supabase.from("site_settings").update({
        value: req.body.value,
        updated_at: (/* @__PURE__ */ new Date()).toISOString()
      }).eq("key", req.params.key).select().single();
      if (error) throw error;
      res.json(setting);
    } catch (error) {
      console.error("Update setting error:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  });
  const httpServer = createServer(app2);
  return httpServer;
}

// server/vite.ts
import express2 from "express";
import fs2 from "fs";
import path3 from "path";
import { createServer as createViteServer, createLogger } from "vite";

// vite.config.ts
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path2 from "path";
var vite_config_default = defineConfig({
  plugins: [
    react()
  ],
  resolve: {
    alias: {
      "@": path2.resolve(import.meta.dirname, "client", "src"),
      "@shared": path2.resolve(import.meta.dirname, "shared")
    }
  },
  root: path2.resolve(import.meta.dirname, "client"),
  build: {
    outDir: path2.resolve(import.meta.dirname, "dist/public"),
    emptyOutDir: true
  },
  server: {
    fs: {
      strict: true,
      deny: ["**/.*"]
    }
  }
});

// server/vite.ts
import { nanoid } from "nanoid";
var viteLogger = createLogger();
function log(message, source = "express") {
  const formattedTime = (/* @__PURE__ */ new Date()).toLocaleTimeString("en-US", {
    hour: "numeric",
    minute: "2-digit",
    second: "2-digit",
    hour12: true
  });
  console.log(`${formattedTime} [${source}] ${message}`);
}
async function setupVite(app2, server) {
  const serverOptions = {
    middlewareMode: true,
    hmr: { server },
    allowedHosts: true
  };
  const vite = await createViteServer({
    ...vite_config_default,
    configFile: false,
    customLogger: {
      ...viteLogger,
      error: (msg, options) => {
        viteLogger.error(msg, options);
        process.exit(1);
      }
    },
    server: serverOptions,
    appType: "custom"
  });
  app2.use(vite.middlewares);
  app2.use("*", async (req, res, next) => {
    const url = req.originalUrl;
    try {
      const clientTemplate = path3.resolve(
        import.meta.dirname,
        "..",
        "client",
        "index.html"
      );
      let template = await fs2.promises.readFile(clientTemplate, "utf-8");
      template = template.replace(
        `src="/src/main.tsx"`,
        `src="/src/main.tsx?v=${nanoid()}"`
      );
      const page = await vite.transformIndexHtml(url, template);
      res.status(200).set({ "Content-Type": "text/html" }).end(page);
    } catch (e) {
      vite.ssrFixStacktrace(e);
      next(e);
    }
  });
}
function serveStatic(app2) {
  const distPath = path3.resolve(import.meta.dirname, "public");
  if (!fs2.existsSync(distPath)) {
    throw new Error(
      `Could not find the build directory: ${distPath}, make sure to build the client first`
    );
  }
  app2.use(express2.static(distPath));
  app2.use("*", (_req, res) => {
    res.sendFile(path3.resolve(distPath, "index.html"));
  });
}

// server/index.ts
var app = express3();
app.use(express3.json());
app.use(express3.urlencoded({ extended: false }));
app.use((req, res, next) => {
  const start = Date.now();
  const path4 = req.path;
  let capturedJsonResponse = void 0;
  const originalResJson = res.json;
  res.json = function(bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };
  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path4.startsWith("/api")) {
      let logLine = `${req.method} ${path4} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }
      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "\u2026";
      }
      log(logLine);
    }
  });
  next();
});
(async () => {
  const server = await registerRoutes(app);
  app.use((err, _req, res, _next) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";
    res.status(status).json({ message });
    throw err;
  });
  if (app.get("env") === "development") {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }
  const port = parseInt(process.env.PORT || "3000", 10);
  server.listen(port, () => {
    log(`serving on port ${port}`);
  });
})();
