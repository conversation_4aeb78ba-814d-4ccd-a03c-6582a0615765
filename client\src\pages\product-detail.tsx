import { useEffect, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { useRoute, <PERSON> } from "wouter";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Separator } from "@/components/ui/separator";
import { useLanguage } from "@/hooks/use-language";
import { useToast } from "@/hooks/use-toast";
import { Product, Category } from "@shared/schema";
import { 
  ArrowLeft, 
  ChevronLeft, 
  ChevronRight, 
  ZoomIn, 
  Download, 
  Share2, 
  FileText,
  ArrowRight
} from "lucide-react";

export default function ProductDetail() {
  const [match, params] = useRoute("/products/:id");
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const { t, language } = useLanguage();
  const { toast } = useToast();

  const { data: product, isLoading: productLoading, error } = useQuery({
    queryKey: ['/api/products', params?.id],
    queryFn: async () => {
      if (!params?.id) throw new Error('No product ID provided');
      const response = await fetch(`/api/products/${params.id}`);
      if (!response.ok) throw new Error('Product not found');
      return response.json() as Promise<Product>;
    },
    enabled: !!params?.id,
  });

  const { data: category } = useQuery({
    queryKey: ['/api/categories', product?.categoryId],
    queryFn: async () => {
      const response = await fetch(`/api/categories/${product!.categoryId}`);
      return response.json() as Promise<Category>;
    },
    enabled: !!product?.categoryId,
  });

  const { data: relatedProducts } = useQuery({
    queryKey: ['/api/products', { categoryId: product?.categoryId, limit: 4 }],
    queryFn: async () => {
      const response = await fetch(`/api/products?categoryId=${product!.categoryId}&limit=4&isActive=true`);
      return response.json() as Promise<{ products: Product[]; total: number }>;
    },
    enabled: !!product?.categoryId,
  });

  useEffect(() => {
    if (product) {
      document.title = `${language === 'ar' && product.nameAr ? product.nameAr : product.name} | Luxe Stone`;
    }
  }, [product, language]);

  if (!match) {
    return <div>Product not found</div>;
  }

  if (productLoading) {
    return (
      <div className="min-h-screen pt-20">
        <div className="max-w-7xl mx-auto px-6 lg:px-8 py-12">
          <div className="grid lg:grid-cols-2 gap-12">
            <div>
              <Skeleton className="w-full h-96 rounded-xl mb-4" />
              <div className="flex space-x-2">
                {[...Array(4)].map((_, i) => (
                  <Skeleton key={i} className="w-20 h-20 rounded-lg" />
                ))}
              </div>
            </div>
            <div className="space-y-6">
              <Skeleton className="h-8 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
              <Skeleton className="h-32 w-full" />
              <Skeleton className="h-48 w-full" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="min-h-screen pt-20 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-playfair text-charcoal mb-4">Product Not Found</h1>
          <p className="text-medium-gray mb-8">The product you're looking for doesn't exist or has been removed.</p>
          <Link href="/products">
            <Button className="gold-gradient text-white">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Products
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  const productName = language === 'ar' && product.nameAr ? product.nameAr : product.name;
  const productDescription = language === 'ar' && product.descriptionAr ? product.descriptionAr : product.description;
  const fullDescription = language === 'ar' && product.fullDescriptionAr ? product.fullDescriptionAr : product.fullDescription;
  const specifications = language === 'ar' && product.specificationsAr ? product.specificationsAr : product.specifications;
  const applications = language === 'ar' && product.applicationsAr ? product.applicationsAr : product.applications;

  const images = product.images && product.images.length > 0 
    ? product.images 
    : ['https://images.unsplash.com/photo-1565043666747-69f6646db940?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&h=600'];

  const categoryName = category 
    ? (language === 'ar' && category.nameAr ? category.nameAr : category.name)
    : '';

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % images.length);
  };

  const previousImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + images.length) % images.length);
  };

  const handleRequestQuote = () => {
    toast({
      title: t('common.success'),
      description: "Quote request sent successfully!",
    });
  };

  const handleDownloadBrochure = () => {
    toast({
      title: t('common.success'),
      description: "Brochure download started!",
    });
  };

  const handleShareProduct = () => {
    if (navigator.share) {
      navigator.share({
        title: productName,
        text: productDescription || '',
        url: window.location.href,
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
      toast({
        title: t('common.success'),
        description: "Product link copied to clipboard!",
      });
    }
  };

  return (
    <div className="min-h-screen pt-20">
      {/* Breadcrumb */}
      <div className="bg-warm-white py-4 border-b border-beige">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="flex items-center space-x-2 text-sm">
            <Link href="/" className="text-medium-gray hover:text-gold transition-colors">
              {t('nav.home')}
            </Link>
            <ArrowRight className="h-4 w-4 text-medium-gray" />
            <Link href="/products" className="text-medium-gray hover:text-gold transition-colors">
              {t('nav.products')}
            </Link>
            {categoryName && (
              <>
                <ArrowRight className="h-4 w-4 text-medium-gray" />
                <span className="text-medium-gray">{categoryName}</span>
              </>
            )}
            <ArrowRight className="h-4 w-4 text-medium-gray" />
            <span className="text-charcoal font-medium">{productName}</span>
          </div>
        </div>
      </div>

      {/* Product Detail */}
      <div className="max-w-7xl mx-auto px-6 lg:px-8 py-12">
        <div className="grid lg:grid-cols-2 gap-12">
          {/* Product Images */}
          <div>
            <div className="relative">
              <img 
                src={images[currentImageIndex]} 
                alt={`${productName} - Image ${currentImageIndex + 1}`}
                className="w-full h-96 lg:h-[500px] object-cover rounded-xl"
              />
              
              {images.length > 1 && (
                <>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={previousImage}
                    className="absolute left-4 top-1/2 transform -translate-y-1/2 w-10 h-10 bg-white bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all duration-300"
                  >
                    <ChevronLeft className="h-5 w-5" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={nextImage}
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 w-10 h-10 bg-white bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all duration-300"
                  >
                    <ChevronRight className="h-5 w-5" />
                  </Button>
                </>
              )}

              <Button
                variant="ghost"
                size="sm"
                className="absolute top-4 right-4 w-10 h-10 bg-white bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all duration-300"
              >
                <ZoomIn className="h-5 w-5" />
              </Button>
            </div>

            {images.length > 1 && (
              <div className="flex space-x-3 mt-4 overflow-x-auto">
                {images.map((image, index) => (
                  <img
                    key={index}
                    src={image}
                    alt={`${productName} thumbnail ${index + 1}`}
                    className={`w-20 h-20 object-cover rounded-lg cursor-pointer border-2 transition-colors duration-300 flex-shrink-0 ${
                      index === currentImageIndex 
                        ? 'border-gold' 
                        : 'border-transparent hover:border-gold'
                    }`}
                    onClick={() => setCurrentImageIndex(index)}
                  />
                ))}
              </div>
            )}
          </div>

          {/* Product Information */}
          <div>
            <div className="flex justify-between items-start mb-6">
              <div>
                <h1 className="font-playfair text-4xl font-bold text-charcoal mb-2">
                  {productName}
                </h1>
                {categoryName && (
                  <p className="text-medium-gray text-lg">{categoryName}</p>
                )}
              </div>
              <Badge className="px-4 py-2 bg-gold text-white rounded-full font-semibold text-lg">
                {product.code}
              </Badge>
            </div>

            {/* Description */}
            <div className="mb-8">
              <h3 className="font-playfair text-xl font-semibold text-charcoal mb-4">
                {t('productDetail.description')}
              </h3>
              <p className="text-medium-gray leading-relaxed text-lg">
                {fullDescription || productDescription}
              </p>
            </div>

            {/* Specifications */}
            <div className="mb-8">
              <h3 className="font-playfair text-xl font-semibold text-charcoal mb-4">
                {t('productDetail.specifications')}
              </h3>
              <Card className="bg-warm-white border-beige">
                <CardContent className="p-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div className="flex justify-between">
                        <span className="text-medium-gray font-medium">{t('productDetail.material')}:</span>
                        <span className="font-semibold text-charcoal">
                          {specifications?.material || product.material || 'Natural Stone'}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-medium-gray font-medium">{t('productDetail.origin')}:</span>
                        <span className="font-semibold text-charcoal">
                          {specifications?.origin || product.origin || 'Italy'}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-medium-gray font-medium">{t('productDetail.finish')}:</span>
                        <span className="font-semibold text-charcoal">
                          {specifications?.finish || product.finish || 'Polished'}
                        </span>
                      </div>
                    </div>
                    <div className="space-y-4">
                      <div className="flex justify-between">
                        <span className="text-medium-gray font-medium">{t('productDetail.thickness')}:</span>
                        <span className="font-semibold text-charcoal">
                          {specifications?.thickness || product.thickness || '20mm / 30mm'}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-medium-gray font-medium">{t('productDetail.application')}:</span>
                        <span className="font-semibold text-charcoal">Interior</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-medium-gray font-medium">{t('productDetail.availability')}:</span>
                        <span className="font-semibold text-green-600">
                          {product.availability || 'In Stock'}
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Applications */}
            {applications && applications.length > 0 && (
              <div className="mb-8">
                <h3 className="font-playfair text-xl font-semibold text-charcoal mb-4">
                  {t('productDetail.applications')}
                </h3>
                <div className="flex flex-wrap gap-2">
                  {applications.map((app, index) => (
                    <Badge 
                      key={index}
                      variant="secondary"
                      className="px-3 py-1 bg-beige text-charcoal"
                    >
                      {app}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="space-y-4">
              <Button 
                onClick={handleRequestQuote}
                size="lg"
                className="w-full py-4 gold-gradient text-white font-semibold hover:shadow-lg transform hover:scale-105 transition-all duration-300"
              >
                <FileText className="h-5 w-5 mr-2" />
                {t('productDetail.requestQuote')}
              </Button>
              <div className="grid grid-cols-2 gap-4">
                <Button 
                  variant="outline"
                  onClick={handleDownloadBrochure}
                  className="py-3 border-2 border-charcoal text-charcoal hover:bg-charcoal hover:text-white transition-all duration-300"
                >
                  <Download className="h-4 w-4 mr-2" />
                  {t('productDetail.downloadBrochure')}
                </Button>
                <Button 
                  variant="outline"
                  onClick={handleShareProduct}
                  className="py-3 border-2 border-gold text-gold hover:bg-gold hover:text-white transition-all duration-300"
                >
                  <Share2 className="h-4 w-4 mr-2" />
                  {t('productDetail.shareProduct')}
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Related Products */}
        {relatedProducts?.products && relatedProducts.products.length > 1 && (
          <div className="mt-20">
            <Separator className="mb-12" />
            <div className="text-center mb-12">
              <h2 className="font-playfair text-3xl font-bold text-charcoal mb-4">
                Related Products
              </h2>
              <p className="text-medium-gray">
                Discover more products from the same collection
              </p>
            </div>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {relatedProducts.products
                .filter(p => p.id !== product.id)
                .slice(0, 3)
                .map((relatedProduct) => (
                  <Link key={relatedProduct.id} href={`/products/${relatedProduct.id}`}>
                    <Card className="product-card bg-white rounded-2xl elegant-shadow overflow-hidden cursor-pointer">
                      <img 
                        src={relatedProduct.images?.[0] || 'https://images.unsplash.com/photo-1565043666747-69f6646db940?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=400&h=300'} 
                        alt={language === 'ar' && relatedProduct.nameAr ? relatedProduct.nameAr : relatedProduct.name}
                        className="w-full h-48 object-cover"
                      />
                      <CardContent className="p-4">
                        <div className="flex justify-between items-start mb-2">
                          <h3 className="font-playfair text-lg font-semibold text-charcoal line-clamp-1">
                            {language === 'ar' && relatedProduct.nameAr ? relatedProduct.nameAr : relatedProduct.name}
                          </h3>
                          <Badge variant="outline" className="text-xs bg-gold text-white border-gold">
                            {relatedProduct.code}
                          </Badge>
                        </div>
                        <p className="text-medium-gray text-sm line-clamp-2">
                          {language === 'ar' && relatedProduct.descriptionAr ? relatedProduct.descriptionAr : relatedProduct.description}
                        </p>
                      </CardContent>
                    </Card>
                  </Link>
                ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
