import { Link } from 'wouter';
import { LayoutGrid } from 'lucide-react';
import { useLanguage } from '@/hooks/use-language';
import { cn, rtlSpace } from '@/lib/utils';

export function NavbarLogo() {
  const { t, isRTL } = useLanguage();

  return (
    <Link href="/" className={cn('flex items-center', rtlSpace(isRTL, 'space-x-4'))}>
      <div className="w-12 h-12 gold-gradient rounded-lg flex items-center justify-center">
        <LayoutGrid className="text-white text-xl" />
      </div>
      <div>
        <h1 className="font-playfair font-bold text-xl text-charcoal">
          {isRTL ? 'آفاق' : 'Afaq'}
        </h1>
        <p className="text-xs text-medium-gray font-light">{t('footer.companyTagline')}</p>
      </div>
    </Link>
  );
}
