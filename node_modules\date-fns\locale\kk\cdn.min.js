var V=function(J,<PERSON>){var Z=Object.keys(J);if(Object.getOwnPropertySymbols){var X=Object.getOwnPropertySymbols(J);I&&(X=X.filter(function(M){return Object.getOwnPropertyDescriptor(J,M).enumerable})),Z.push.apply(Z,X)}return Z},x=function(J){for(var I=1;I<arguments.length;I++){var Z=arguments[I]!=null?arguments[I]:{};I%2?V(Object(Z),!0).forEach(function(X){Q0(J,X,Z[X])}):Object.getOwnPropertyDescriptors?Object.defineProperties(J,Object.getOwnPropertyDescriptors(Z)):V(Object(Z)).forEach(function(X){Object.defineProperty(J,X,Object.getOwnPropertyDescriptor(Z,X))})}return J},Q0=function(J,<PERSON>,Z){if(I=q0(I),I in J)Object.defineProperty(J,I,{value:Z,enumerable:!0,configurable:!0,writable:!0});else J[I]=Z;return J},q0=function(J){var I=K0(J,"string");return N(I)=="symbol"?I:String(I)},K0=function(J,I){if(N(J)!="object"||!J)return J;var Z=J[Symbol.toPrimitive];if(Z!==void 0){var X=Z.call(J,I||"default");if(N(X)!="object")return X;throw new TypeError("@@toPrimitive must return a primitive value.")}return(I==="string"?String:Number)(J)},N=function(J){return N=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(I){return typeof I}:function(I){return I&&typeof Symbol=="function"&&I.constructor===Symbol&&I!==Symbol.prototype?"symbol":typeof I},N(J)};(function(J){var I=Object.defineProperty,Z=function G(C,B){for(var H in B)I(C,H,{get:B[H],enumerable:!0,configurable:!0,set:function Y(T){return B[H]=function(){return T}}})},X=function G(C,B){if(C.one&&B===1)return C.one;var H=B%10,Y=B%100;if(H===1&&Y!==11)return C.singularNominative.replace("{{count}}",String(B));else if(H>=2&&H<=4&&(Y<10||Y>20))return C.singularGenitive.replace("{{count}}",String(B));else return C.pluralGenitive.replace("{{count}}",String(B))},M={lessThanXSeconds:{regular:{one:"1 \u0441\u0435\u043A\u0443\u043D\u0434\u0442\u0430\u043D \u0430\u0437",singularNominative:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0442\u0430\u043D \u0430\u0437",singularGenitive:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0442\u0430\u043D \u0430\u0437",pluralGenitive:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0442\u0430\u043D \u0430\u0437"},future:{one:"\u0431\u0456\u0440 \u0441\u0435\u043A\u0443\u043D\u0434\u0442\u0430\u043D \u043A\u0435\u0439\u0456\u043D",singularNominative:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0442\u0430\u043D \u043A\u0435\u0439\u0456\u043D",singularGenitive:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0442\u0430\u043D \u043A\u0435\u0439\u0456\u043D",pluralGenitive:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0442\u0430\u043D \u043A\u0435\u0439\u0456\u043D"}},xSeconds:{regular:{singularNominative:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434",singularGenitive:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434",pluralGenitive:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434"},past:{singularNominative:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434 \u0431\u04B1\u0440\u044B\u043D",singularGenitive:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434 \u0431\u04B1\u0440\u044B\u043D",pluralGenitive:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434 \u0431\u04B1\u0440\u044B\u043D"},future:{singularNominative:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0442\u0430\u043D \u043A\u0435\u0439\u0456\u043D",singularGenitive:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0442\u0430\u043D \u043A\u0435\u0439\u0456\u043D",pluralGenitive:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0442\u0430\u043D \u043A\u0435\u0439\u0456\u043D"}},halfAMinute:function G(C){if(C!==null&&C!==void 0&&C.addSuffix)if(C.comparison&&C.comparison>0)return"\u0436\u0430\u0440\u0442\u044B \u043C\u0438\u043D\u0443\u0442 \u0456\u0448\u0456\u043D\u0434\u0435";else return"\u0436\u0430\u0440\u0442\u044B \u043C\u0438\u043D\u0443\u0442 \u0431\u04B1\u0440\u044B\u043D";return"\u0436\u0430\u0440\u0442\u044B \u043C\u0438\u043D\u0443\u0442"},lessThanXMinutes:{regular:{one:"1 \u043C\u0438\u043D\u0443\u0442\u0442\u0430\u043D \u0430\u0437",singularNominative:"{{count}} \u043C\u0438\u043D\u0443\u0442\u0442\u0430\u043D \u0430\u0437",singularGenitive:"{{count}} \u043C\u0438\u043D\u0443\u0442\u0442\u0430\u043D \u0430\u0437",pluralGenitive:"{{count}} \u043C\u0438\u043D\u0443\u0442\u0442\u0430\u043D \u0430\u0437"},future:{one:"\u043C\u0438\u043D\u0443\u0442\u0442\u0430\u043D \u043A\u0435\u043C ",singularNominative:"{{count}} \u043C\u0438\u043D\u0443\u0442\u0442\u0430\u043D \u043A\u0435\u043C",singularGenitive:"{{count}} \u043C\u0438\u043D\u0443\u0442\u0442\u0430\u043D \u043A\u0435\u043C",pluralGenitive:"{{count}} \u043C\u0438\u043D\u0443\u0442\u0442\u0430\u043D \u043A\u0435\u043C"}},xMinutes:{regular:{singularNominative:"{{count}} \u043C\u0438\u043D\u0443\u0442",singularGenitive:"{{count}} \u043C\u0438\u043D\u0443\u0442",pluralGenitive:"{{count}} \u043C\u0438\u043D\u0443\u0442"},past:{singularNominative:"{{count}} \u043C\u0438\u043D\u0443\u0442 \u0431\u04B1\u0440\u044B\u043D",singularGenitive:"{{count}} \u043C\u0438\u043D\u0443\u0442 \u0431\u04B1\u0440\u044B\u043D",pluralGenitive:"{{count}} \u043C\u0438\u043D\u0443\u0442 \u0431\u04B1\u0440\u044B\u043D"},future:{singularNominative:"{{count}} \u043C\u0438\u043D\u0443\u0442\u0442\u0430\u043D \u043A\u0435\u0439\u0456\u043D",singularGenitive:"{{count}} \u043C\u0438\u043D\u0443\u0442\u0442\u0430\u043D \u043A\u0435\u0439\u0456\u043D",pluralGenitive:"{{count}} \u043C\u0438\u043D\u0443\u0442\u0442\u0430\u043D \u043A\u0435\u0439\u0456\u043D"}},aboutXHours:{regular:{singularNominative:"\u0448\u0430\u043C\u0430\u043C\u0435\u043D {{count}} \u0441\u0430\u0493\u0430\u0442",singularGenitive:"\u0448\u0430\u043C\u0430\u043C\u0435\u043D {{count}} \u0441\u0430\u0493\u0430\u0442",pluralGenitive:"\u0448\u0430\u043C\u0430\u043C\u0435\u043D {{count}} \u0441\u0430\u0493\u0430\u0442"},future:{singularNominative:"\u0448\u0430\u043C\u0430\u043C\u0435\u043D {{count}} \u0441\u0430\u0493\u0430\u0442\u0442\u0430\u043D \u043A\u0435\u0439\u0456\u043D",singularGenitive:"\u0448\u0430\u043C\u0430\u043C\u0435\u043D {{count}} \u0441\u0430\u0493\u0430\u0442\u0442\u0430\u043D \u043A\u0435\u0439\u0456\u043D",pluralGenitive:"\u0448\u0430\u043C\u0430\u043C\u0435\u043D {{count}} \u0441\u0430\u0493\u0430\u0442\u0442\u0430\u043D \u043A\u0435\u0439\u0456\u043D"}},xHours:{regular:{singularNominative:"{{count}} \u0441\u0430\u0493\u0430\u0442",singularGenitive:"{{count}} \u0441\u0430\u0493\u0430\u0442",pluralGenitive:"{{count}} \u0441\u0430\u0493\u0430\u0442"}},xDays:{regular:{singularNominative:"{{count}} \u043A\u04AF\u043D",singularGenitive:"{{count}} \u043A\u04AF\u043D",pluralGenitive:"{{count}} \u043A\u04AF\u043D"},future:{singularNominative:"{{count}} \u043A\u04AF\u043D\u043D\u0435\u043D \u043A\u0435\u0439\u0456\u043D",singularGenitive:"{{count}} \u043A\u04AF\u043D\u043D\u0435\u043D \u043A\u0435\u0439\u0456\u043D",pluralGenitive:"{{count}} \u043A\u04AF\u043D\u043D\u0435\u043D \u043A\u0435\u0439\u0456\u043D"}},aboutXWeeks:{type:"weeks",one:"\u0448\u0430\u043C\u0430\u043C\u0435\u043D 1 \u0430\u043F\u0442\u0430",other:"\u0448\u0430\u043C\u0430\u043C\u0435\u043D {{count}} \u0430\u043F\u0442\u0430"},xWeeks:{type:"weeks",one:"1 \u0430\u043F\u0442\u0430",other:"{{count}} \u0430\u043F\u0442\u0430"},aboutXMonths:{regular:{singularNominative:"\u0448\u0430\u043C\u0430\u043C\u0435\u043D {{count}} \u0430\u0439",singularGenitive:"\u0448\u0430\u043C\u0430\u043C\u0435\u043D {{count}} \u0430\u0439",pluralGenitive:"\u0448\u0430\u043C\u0430\u043C\u0435\u043D {{count}} \u0430\u0439"},future:{singularNominative:"\u0448\u0430\u043C\u0430\u043C\u0435\u043D {{count}} \u0430\u0439\u0434\u0430\u043D \u043A\u0435\u0439\u0456\u043D",singularGenitive:"\u0448\u0430\u043C\u0430\u043C\u0435\u043D {{count}} \u0430\u0439\u0434\u0430\u043D \u043A\u0435\u0439\u0456\u043D",pluralGenitive:"\u0448\u0430\u043C\u0430\u043C\u0435\u043D {{count}} \u0430\u0439\u0434\u0430\u043D \u043A\u0435\u0439\u0456\u043D"}},xMonths:{regular:{singularNominative:"{{count}} \u0430\u0439",singularGenitive:"{{count}} \u0430\u0439",pluralGenitive:"{{count}} \u0430\u0439"}},aboutXYears:{regular:{singularNominative:"\u0448\u0430\u043C\u0430\u043C\u0435\u043D {{count}} \u0436\u044B\u043B",singularGenitive:"\u0448\u0430\u043C\u0430\u043C\u0435\u043D {{count}} \u0436\u044B\u043B",pluralGenitive:"\u0448\u0430\u043C\u0430\u043C\u0435\u043D {{count}} \u0436\u044B\u043B"},future:{singularNominative:"\u0448\u0430\u043C\u0430\u043C\u0435\u043D {{count}} \u0436\u044B\u043B\u0434\u0430\u043D \u043A\u0435\u0439\u0456\u043D",singularGenitive:"\u0448\u0430\u043C\u0430\u043C\u0435\u043D {{count}} \u0436\u044B\u043B\u0434\u0430\u043D \u043A\u0435\u0439\u0456\u043D",pluralGenitive:"\u0448\u0430\u043C\u0430\u043C\u0435\u043D {{count}} \u0436\u044B\u043B\u0434\u0430\u043D \u043A\u0435\u0439\u0456\u043D"}},xYears:{regular:{singularNominative:"{{count}} \u0436\u044B\u043B",singularGenitive:"{{count}} \u0436\u044B\u043B",pluralGenitive:"{{count}} \u0436\u044B\u043B"},future:{singularNominative:"{{count}} \u0436\u044B\u043B\u0434\u0430\u043D \u043A\u0435\u0439\u0456\u043D",singularGenitive:"{{count}} \u0436\u044B\u043B\u0434\u0430\u043D \u043A\u0435\u0439\u0456\u043D",pluralGenitive:"{{count}} \u0436\u044B\u043B\u0434\u0430\u043D \u043A\u0435\u0439\u0456\u043D"}},overXYears:{regular:{singularNominative:"{{count}} \u0436\u044B\u043B\u0434\u0430\u043D \u0430\u0441\u0442\u0430\u043C",singularGenitive:"{{count}} \u0436\u044B\u043B\u0434\u0430\u043D \u0430\u0441\u0442\u0430\u043C",pluralGenitive:"{{count}} \u0436\u044B\u043B\u0434\u0430\u043D \u0430\u0441\u0442\u0430\u043C"},future:{singularNominative:"{{count}} \u0436\u044B\u043B\u0434\u0430\u043D \u0430\u0441\u0442\u0430\u043C",singularGenitive:"{{count}} \u0436\u044B\u043B\u0434\u0430\u043D \u0430\u0441\u0442\u0430\u043C",pluralGenitive:"{{count}} \u0436\u044B\u043B\u0434\u0430\u043D \u0430\u0441\u0442\u0430\u043C"}},almostXYears:{regular:{singularNominative:"{{count}} \u0436\u044B\u043B\u0493\u0430 \u0436\u0430\u049B\u044B\u043D",singularGenitive:"{{count}} \u0436\u044B\u043B\u0493\u0430 \u0436\u0430\u049B\u044B\u043D",pluralGenitive:"{{count}} \u0436\u044B\u043B\u0493\u0430 \u0436\u0430\u049B\u044B\u043D"},future:{singularNominative:"{{count}} \u0436\u044B\u043B\u0434\u0430\u043D \u043A\u0435\u0439\u0456\u043D",singularGenitive:"{{count}} \u0436\u044B\u043B\u0434\u0430\u043D \u043A\u0435\u0439\u0456\u043D",pluralGenitive:"{{count}} \u0436\u044B\u043B\u0434\u0430\u043D \u043A\u0435\u0439\u0456\u043D"}}},O=function G(C,B,H){var Y=M[C];if(typeof Y==="function")return Y(H);if(Y.type==="weeks")return B===1?Y.one:Y.other.replace("{{count}}",String(B));if(H!==null&&H!==void 0&&H.addSuffix)if(H.comparison&&H.comparison>0)if(Y.future)return X(Y.future,B);else return X(Y.regular,B)+" \u043A\u0435\u0439\u0456\u043D";else if(Y.past)return X(Y.past,B);else return X(Y.regular,B)+" \u0431\u04B1\u0440\u044B\u043D";else return X(Y.regular,B)};function R(G){return function(){var C=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},B=C.width?String(C.width):G.defaultWidth,H=G.formats[B]||G.formats[G.defaultWidth];return H}}var P={full:"EEEE, do MMMM y '\u0436.'",long:"do MMMM y '\u0436.'",medium:"d MMM y '\u0436.'",short:"dd.MM.yyyy"},F={full:"H:mm:ss zzzz",long:"H:mm:ss z",medium:"H:mm:ss",short:"H:mm"},w={any:"{{date}}, {{time}}"},b={date:R({formats:P,defaultWidth:"full"}),time:R({formats:F,defaultWidth:"full"}),dateTime:R({formats:w,defaultWidth:"any"})};function h(G){var C=Object.prototype.toString.call(G);if(G instanceof Date||N(G)==="object"&&C==="[object Date]")return new G.constructor(+G);else if(typeof G==="number"||C==="[object Number]"||typeof G==="string"||C==="[object String]")return new Date(G);else return new Date(NaN)}function _(){return v}function N0(G){v=G}var v={};function W(G,C){var B,H,Y,T,U,E,Q=_(),A=(B=(H=(Y=(T=C===null||C===void 0?void 0:C.weekStartsOn)!==null&&T!==void 0?T:C===null||C===void 0||(U=C.locale)===null||U===void 0||(U=U.options)===null||U===void 0?void 0:U.weekStartsOn)!==null&&Y!==void 0?Y:Q.weekStartsOn)!==null&&H!==void 0?H:(E=Q.locale)===null||E===void 0||(E=E.options)===null||E===void 0?void 0:E.weekStartsOn)!==null&&B!==void 0?B:0,q=h(G),K=q.getDay(),A0=(K<A?7:0)+K-A;return q.setDate(q.getDate()-A0),q.setHours(0,0,0,0),q}function S(G,C,B){var H=W(G,B),Y=W(C,B);return+H===+Y}var f=function G(C){var B=L[C];return"'\u04E9\u0442\u043A\u0435\u043D "+B+" \u0441\u0430\u0493\u0430\u0442' p'-\u0434\u0435'"},$=function G(C){var B=L[C];return"'"+B+" \u0441\u0430\u0493\u0430\u0442' p'-\u0434\u0435'"},k=function G(C){var B=L[C];return"'\u043A\u0435\u043B\u0435\u0441\u0456 "+B+" \u0441\u0430\u0493\u0430\u0442' p'-\u0434\u0435'"},L=["\u0436\u0435\u043A\u0441\u0435\u043D\u0431\u0456\u0434\u0435","\u0434\u04AF\u0439\u0441\u0435\u043D\u0431\u0456\u0434\u0435","\u0441\u0435\u0439\u0441\u0435\u043D\u0431\u0456\u0434\u0435","\u0441\u04D9\u0440\u0441\u0435\u043D\u0431\u0456\u0434\u0435","\u0431\u0435\u0439\u0441\u0435\u043D\u0431\u0456\u0434\u0435","\u0436\u04B1\u043C\u0430\u0434\u0430","\u0441\u0435\u043D\u0431\u0456\u0434\u0435"],y={lastWeek:function G(C,B,H){var Y=C.getDay();if(S(C,B,H))return $(Y);else return f(Y)},yesterday:"'\u043A\u0435\u0448\u0435 \u0441\u0430\u0493\u0430\u0442' p'-\u0434\u0435'",today:"'\u0431\u04AF\u0433\u0456\u043D \u0441\u0430\u0493\u0430\u0442' p'-\u0434\u0435'",tomorrow:"'\u0435\u0440\u0442\u0435\u04A3 \u0441\u0430\u0493\u0430\u0442' p'-\u0434\u0435'",nextWeek:function G(C,B,H){var Y=C.getDay();if(S(C,B,H))return $(Y);else return k(Y)},other:"P"},u=function G(C,B,H,Y){var T=y[C];if(typeof T==="function")return T(B,H,Y);return T};function z(G){return function(C,B){var H=B!==null&&B!==void 0&&B.context?String(B.context):"standalone",Y;if(H==="formatting"&&G.formattingValues){var T=G.defaultFormattingWidth||G.defaultWidth,U=B!==null&&B!==void 0&&B.width?String(B.width):T;Y=G.formattingValues[U]||G.formattingValues[T]}else{var E=G.defaultWidth,Q=B!==null&&B!==void 0&&B.width?String(B.width):G.defaultWidth;Y=G.values[Q]||G.values[E]}var A=G.argumentCallback?G.argumentCallback(C):C;return Y[A]}}var m={narrow:["\u0431.\u0437.\u0434.","\u0431.\u0437."],abbreviated:["\u0431.\u0437.\u0434.","\u0431.\u0437."],wide:["\u0431\u0456\u0437\u0434\u0456\u04A3 \u0437\u0430\u043C\u0430\u043D\u044B\u043C\u044B\u0437\u0493\u0430 \u0434\u0435\u0439\u0456\u043D","\u0431\u0456\u0437\u0434\u0456\u04A3 \u0437\u0430\u043C\u0430\u043D\u044B\u043C\u044B\u0437"]},g={narrow:["1","2","3","4"],abbreviated:["1-\u0448\u0456 \u0442\u043E\u049B.","2-\u0448\u0456 \u0442\u043E\u049B.","3-\u0448\u0456 \u0442\u043E\u049B.","4-\u0448\u0456 \u0442\u043E\u049B."],wide:["1-\u0448\u0456 \u0442\u043E\u049B\u0441\u0430\u043D","2-\u0448\u0456 \u0442\u043E\u049B\u0441\u0430\u043D","3-\u0448\u0456 \u0442\u043E\u049B\u0441\u0430\u043D","4-\u0448\u0456 \u0442\u043E\u049B\u0441\u0430\u043D"]},c={narrow:["\u049A","\u0410","\u041D","\u0421","\u041C","\u041C","\u0428","\u0422","\u049A","\u049A","\u049A","\u0416"],abbreviated:["\u049B\u0430\u04A3","\u0430\u049B\u043F","\u043D\u0430\u0443","\u0441\u04D9\u0443","\u043C\u0430\u043C","\u043C\u0430\u0443","\u0448\u0456\u043B","\u0442\u0430\u043C","\u049B\u044B\u0440","\u049B\u0430\u0437","\u049B\u0430\u0440","\u0436\u0435\u043B"],wide:["\u049B\u0430\u04A3\u0442\u0430\u0440","\u0430\u049B\u043F\u0430\u043D","\u043D\u0430\u0443\u0440\u044B\u0437","\u0441\u04D9\u0443\u0456\u0440","\u043C\u0430\u043C\u044B\u0440","\u043C\u0430\u0443\u0441\u044B\u043C","\u0448\u0456\u043B\u0434\u0435","\u0442\u0430\u043C\u044B\u0437","\u049B\u044B\u0440\u043A\u04AF\u0439\u0435\u043A","\u049B\u0430\u0437\u0430\u043D","\u049B\u0430\u0440\u0430\u0448\u0430","\u0436\u0435\u043B\u0442\u043E\u049B\u0441\u0430\u043D"]},l={narrow:["\u049A","\u0410","\u041D","\u0421","\u041C","\u041C","\u0428","\u0422","\u049A","\u049A","\u049A","\u0416"],abbreviated:["\u049B\u0430\u04A3","\u0430\u049B\u043F","\u043D\u0430\u0443","\u0441\u04D9\u0443","\u043C\u0430\u043C","\u043C\u0430\u0443","\u0448\u0456\u043B","\u0442\u0430\u043C","\u049B\u044B\u0440","\u049B\u0430\u0437","\u049B\u0430\u0440","\u0436\u0435\u043B"],wide:["\u049B\u0430\u04A3\u0442\u0430\u0440","\u0430\u049B\u043F\u0430\u043D","\u043D\u0430\u0443\u0440\u044B\u0437","\u0441\u04D9\u0443\u0456\u0440","\u043C\u0430\u043C\u044B\u0440","\u043C\u0430\u0443\u0441\u044B\u043C","\u0448\u0456\u043B\u0434\u0435","\u0442\u0430\u043C\u044B\u0437","\u049B\u044B\u0440\u043A\u04AF\u0439\u0435\u043A","\u049B\u0430\u0437\u0430\u043D","\u049B\u0430\u0440\u0430\u0448\u0430","\u0436\u0435\u043B\u0442\u043E\u049B\u0441\u0430\u043D"]},d={narrow:["\u0416","\u0414","\u0421","\u0421","\u0411","\u0416","\u0421"],short:["\u0436\u0441","\u0434\u0441","\u0441\u0441","\u0441\u0440","\u0431\u0441","\u0436\u043C","\u0441\u0431"],abbreviated:["\u0436\u0441","\u0434\u0441","\u0441\u0441","\u0441\u0440","\u0431\u0441","\u0436\u043C","\u0441\u0431"],wide:["\u0436\u0435\u043A\u0441\u0435\u043D\u0431\u0456","\u0434\u04AF\u0439\u0441\u0435\u043D\u0431\u0456","\u0441\u0435\u0439\u0441\u0435\u043D\u0431\u0456","\u0441\u04D9\u0440\u0441\u0435\u043D\u0431\u0456","\u0431\u0435\u0439\u0441\u0435\u043D\u0431\u0456","\u0436\u04B1\u043C\u0430","\u0441\u0435\u043D\u0431\u0456"]},p={narrow:{am:"\u0422\u0414",pm:"\u0422\u041A",midnight:"\u0442\u04AF\u043D \u043E\u0440\u0442\u0430\u0441\u044B",noon:"\u0442\u04AF\u0441",morning:"\u0442\u0430\u04A3",afternoon:"\u043A\u04AF\u043D\u0434\u0456\u0437",evening:"\u043A\u0435\u0448",night:"\u0442\u04AF\u043D"},wide:{am:"\u0422\u0414",pm:"\u0422\u041A",midnight:"\u0442\u04AF\u043D \u043E\u0440\u0442\u0430\u0441\u044B",noon:"\u0442\u04AF\u0441",morning:"\u0442\u0430\u04A3",afternoon:"\u043A\u04AF\u043D\u0434\u0456\u0437",evening:"\u043A\u0435\u0448",night:"\u0442\u04AF\u043D"}},i={narrow:{am:"\u0422\u0414",pm:"\u0422\u041A",midnight:"\u0442\u04AF\u043D \u043E\u0440\u0442\u0430\u0441\u044B\u043D\u0434\u0430",noon:"\u0442\u04AF\u0441",morning:"\u0442\u0430\u04A3",afternoon:"\u043A\u04AF\u043D",evening:"\u043A\u0435\u0448",night:"\u0442\u04AF\u043D"},wide:{am:"\u0422\u0414",pm:"\u0422\u041A",midnight:"\u0442\u04AF\u043D \u043E\u0440\u0442\u0430\u0441\u044B\u043D\u0434\u0430",noon:"\u0442\u04AF\u0441\u0442\u0435",morning:"\u0442\u0430\u04A3\u0435\u0440\u0442\u0435\u04A3",afternoon:"\u043A\u04AF\u043D\u0434\u0456\u0437",evening:"\u043A\u0435\u0448\u0442\u0435",night:"\u0442\u04AF\u043D\u0434\u0435"}},j={0:"-\u0448\u0456",1:"-\u0448\u0456",2:"-\u0448\u0456",3:"-\u0448\u0456",4:"-\u0448\u0456",5:"-\u0448\u0456",6:"-\u0448\u044B",7:"-\u0448\u0456",8:"-\u0448\u0456",9:"-\u0448\u044B",10:"-\u0448\u044B",20:"-\u0448\u044B",30:"-\u0448\u044B",40:"-\u0448\u044B",50:"-\u0448\u0456",60:"-\u0448\u044B",70:"-\u0448\u0456",80:"-\u0448\u0456",90:"-\u0448\u044B",100:"-\u0448\u0456"},r=function G(C,B){var H=Number(C),Y=H%10,T=H>=100?100:null,U=j[H]||j[Y]||T&&j[T]||"";return H+U},n={ordinalNumber:r,era:z({values:m,defaultWidth:"wide"}),quarter:z({values:g,defaultWidth:"wide",argumentCallback:function G(C){return C-1}}),month:z({values:c,defaultWidth:"wide",formattingValues:l,defaultFormattingWidth:"wide"}),day:z({values:d,defaultWidth:"wide"}),dayPeriod:z({values:p,defaultWidth:"any",formattingValues:i,defaultFormattingWidth:"wide"})};function D(G){return function(C){var B=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},H=B.width,Y=H&&G.matchPatterns[H]||G.matchPatterns[G.defaultMatchWidth],T=C.match(Y);if(!T)return null;var U=T[0],E=H&&G.parsePatterns[H]||G.parsePatterns[G.defaultParseWidth],Q=Array.isArray(E)?o(E,function(K){return K.test(U)}):s(E,function(K){return K.test(U)}),A;A=G.valueCallback?G.valueCallback(Q):Q,A=B.valueCallback?B.valueCallback(A):A;var q=C.slice(U.length);return{value:A,rest:q}}}var s=function G(C,B){for(var H in C)if(Object.prototype.hasOwnProperty.call(C,H)&&B(C[H]))return H;return},o=function G(C,B){for(var H=0;H<C.length;H++)if(B(C[H]))return H;return};function a(G){return function(C){var B=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},H=C.match(G.matchPattern);if(!H)return null;var Y=H[0],T=C.match(G.parsePattern);if(!T)return null;var U=G.valueCallback?G.valueCallback(T[0]):T[0];U=B.valueCallback?B.valueCallback(U):U;var E=C.slice(Y.length);return{value:U,rest:E}}}var e=/^(\d+)(-?(ші|шы))?/i,t=/\d+/i,C0={narrow:/^((б )?з\.?\s?д\.?)/i,abbreviated:/^((б )?з\.?\s?д\.?)/i,wide:/^(біздің заманымызға дейін|біздің заманымыз|біздің заманымыздан)/i},B0={any:[/^б/i,/^з/i]},G0={narrow:/^[1234]/i,abbreviated:/^[1234](-?ші)? тоқ.?/i,wide:/^[1234](-?ші)? тоқсан/i},H0={any:[/1/i,/2/i,/3/i,/4/i]},Y0={narrow:/^(қ|а|н|с|м|мау|ш|т|қыр|қаз|қар|ж)/i,abbreviated:/^(қаң|ақп|нау|сәу|мам|мау|шіл|там|қыр|қаз|қар|жел)/i,wide:/^(қаңтар|ақпан|наурыз|сәуір|мамыр|маусым|шілде|тамыз|қыркүйек|қазан|қараша|желтоқсан)/i},I0={narrow:[/^қ/i,/^а/i,/^н/i,/^с/i,/^м/i,/^м/i,/^ш/i,/^т/i,/^қ/i,/^қ/i,/^қ/i,/^ж/i],abbreviated:[/^қаң/i,/^ақп/i,/^нау/i,/^сәу/i,/^мам/i,/^мау/i,/^шіл/i,/^там/i,/^қыр/i,/^қаз/i,/^қар/i,/^жел/i],any:[/^қ/i,/^а/i,/^н/i,/^с/i,/^м/i,/^м/i,/^ш/i,/^т/i,/^қ/i,/^қ/i,/^қ/i,/^ж/i]},J0={narrow:/^(ж|д|с|с|б|ж|с)/i,short:/^(жс|дс|сс|ср|бс|жм|сб)/i,wide:/^(жексенбі|дүйсенбі|сейсенбі|сәрсенбі|бейсенбі|жұма|сенбі)/i},T0={narrow:[/^ж/i,/^д/i,/^с/i,/^с/i,/^б/i,/^ж/i,/^с/i],short:[/^жс/i,/^дс/i,/^сс/i,/^ср/i,/^бс/i,/^жм/i,/^сб/i],any:[/^ж[ек]/i,/^д[үй]/i,/^сe[й]/i,/^сә[р]/i,/^б[ей]/i,/^ж[ұм]/i,/^се[н]/i]},U0={narrow:/^Т\.?\s?[ДК]\.?|түн ортасында|((түсте|таңертең|таңда|таңертең|таңмен|таң|күндіз|күн|кеште|кеш|түнде|түн)\.?)/i,wide:/^Т\.?\s?[ДК]\.?|түн ортасында|((түсте|таңертең|таңда|таңертең|таңмен|таң|күндіз|күн|кеште|кеш|түнде|түн)\.?)/i,any:/^Т\.?\s?[ДК]\.?|түн ортасында|((түсте|таңертең|таңда|таңертең|таңмен|таң|күндіз|күн|кеште|кеш|түнде|түн)\.?)/i},X0={any:{am:/^ТД/i,pm:/^ТК/i,midnight:/^түн орта/i,noon:/^күндіз/i,morning:/таң/i,afternoon:/түс/i,evening:/кеш/i,night:/түн/i}},Z0={ordinalNumber:a({matchPattern:e,parsePattern:t,valueCallback:function G(C){return parseInt(C,10)}}),era:D({matchPatterns:C0,defaultMatchWidth:"wide",parsePatterns:B0,defaultParseWidth:"any"}),quarter:D({matchPatterns:G0,defaultMatchWidth:"wide",parsePatterns:H0,defaultParseWidth:"any",valueCallback:function G(C){return C+1}}),month:D({matchPatterns:Y0,defaultMatchWidth:"wide",parsePatterns:I0,defaultParseWidth:"any"}),day:D({matchPatterns:J0,defaultMatchWidth:"wide",parsePatterns:T0,defaultParseWidth:"any"}),dayPeriod:D({matchPatterns:U0,defaultMatchWidth:"wide",parsePatterns:X0,defaultParseWidth:"any"})},E0={code:"kk",formatDistance:O,formatLong:b,formatRelative:u,localize:n,match:Z0,options:{weekStartsOn:1,firstWeekContainsDate:1}};window.dateFns=x(x({},window.dateFns),{},{locale:x(x({},(J=window.dateFns)===null||J===void 0?void 0:J.locale),{},{kk:E0})})})();

//# debugId=84DB5A7A4F3EE5B364756e2164756e21
