import { useLanguage } from '@/hooks/use-language';
import { Card, CardContent } from '@/components/ui/card';

export function AboutTeam() {
  const { t, language } = useLanguage();

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          <div className={`${language === 'ar' ? 'lg:order-2' : 'lg:order-1'}`}>
            <Card
              className="elegant-shadow overflow-hidden transform translateZ(0) will-change-transform"
              style={{
                backfaceVisibility: 'hidden',
                WebkitBackfaceVisibility: 'hidden',
                transformStyle: 'preserve-3d',
              }}>
              <div className="relative w-full h-96">
                <img
                  src="https://images.unsplash.com/photo-1578988247876-ce2647da8195?auto=format&fit=crop&w=1200&q=80"
                  alt="Professional team examining and working with natural stone materials - Museums Victoria on Unsplash"
                  className="w-full h-full object-cover"
                  width={600}
                  height={400}
                  loading="lazy"
                  decoding="async"
                  style={{
                    transform: 'translateZ(0)',
                    backfaceVisibility: 'hidden',
                    WebkitBackfaceVisibility: 'hidden',
                    willChange: 'transform',
                    transformStyle: 'preserve-3d',
                    imageRendering: '-webkit-optimize-contrast',
                  }}
                />
              </div>
            </Card>
          </div>

          <div className={`${language === 'ar' ? 'lg:order-1' : 'lg:order-2'}`}>
            <h2 className="font-playfair text-4xl lg:text-5xl font-bold text-charcoal mb-6">{t('about.ourTeam')}</h2>
            <p className="text-lg text-medium-gray leading-relaxed mb-8">{t('about.teamDescription')}</p>

            <div className="space-y-6">
              <div className="flex items-start space-x-4 rtl:space-x-reverse">
                <div className="w-3 h-3 rounded-full bg-gold mt-2 flex-shrink-0"></div>
                <div>
                  <h4 className="font-semibold text-charcoal mb-2">Expert Consultation</h4>
                  <p className="text-medium-gray">
                    Our specialists help you choose the perfect materials for your vision.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4 rtl:space-x-reverse">
                <div className="w-3 h-3 rounded-full bg-bronze mt-2 flex-shrink-0"></div>
                <div>
                  <h4 className="font-semibold text-charcoal mb-2">Quality Assurance</h4>
                  <p className="text-medium-gray">Every piece is carefully inspected to meet our exacting standards.</p>
                </div>
              </div>

              <div className="flex items-start space-x-4 rtl:space-x-reverse">
                <div className="w-3 h-3 rounded-full bg-gold mt-2 flex-shrink-0"></div>
                <div>
                  <h4 className="font-semibold text-charcoal mb-2">Project Support</h4>
                  <p className="text-medium-gray">From design to installation, we're with you every step of the way.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
