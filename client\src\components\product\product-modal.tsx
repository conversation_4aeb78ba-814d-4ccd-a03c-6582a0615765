import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Header } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Product } from "@shared/schema";
import { useLanguage } from "@/hooks/use-language";
import { ChevronLeft, ChevronRight, ZoomIn, X, Download, Share2, FileText } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface ProductModalProps {
  product: Product | null;
  isOpen: boolean;
  onClose: () => void;
}

export function ProductModal({ product, isOpen, onClose }: ProductModalProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const { t, language } = useLanguage();
  const { toast } = useToast();

  if (!product) return null;

  const productName = language === 'ar' && product.nameAr ? product.nameAr : product.name;
  const productDescription = language === 'ar' && product.descriptionAr ? product.descriptionAr : product.description;
  const fullDescription = language === 'ar' && product.fullDescriptionAr ? product.fullDescriptionAr : product.fullDescription;
  const specifications = language === 'ar' && product.specificationsAr ? product.specificationsAr : product.specifications;
  const applications = language === 'ar' && product.applicationsAr ? product.applicationsAr : product.applications;

  const images = product.images && product.images.length > 0 
    ? product.images 
    : ['https://images.unsplash.com/photo-1565043666747-69f6646db940?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&h=600'];

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % images.length);
  };

  const previousImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + images.length) % images.length);
  };

  const handleRequestQuote = () => {
    toast({
      title: t('common.success'),
      description: "Quote request sent successfully!",
    });
  };

  const handleDownloadBrochure = () => {
    toast({
      title: t('common.success'),
      description: "Brochure download started!",
    });
  };

  const handleShareProduct = () => {
    if (navigator.share) {
      navigator.share({
        title: productName,
        text: productDescription || '',
        url: window.location.href,
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
      toast({
        title: t('common.success'),
        description: "Product link copied to clipboard!",
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl w-full max-h-[90vh] overflow-y-auto p-0">
        <div className="grid lg:grid-cols-2 gap-0">
          {/* Product Images */}
          <div className="p-6">
            <div className="relative">
              <img 
                src={images[currentImageIndex]} 
                alt={`${productName} - Image ${currentImageIndex + 1}`}
                className="w-full h-88 object-cover rounded-xl"
              />
              
              {images.length > 1 && (
                <>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={previousImage}
                    className="absolute left-4 top-1/2 transform -translate-y-1/2 w-10 h-10 bg-white bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all duration-300"
                  >
                    <ChevronLeft className="h-5 w-5" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={nextImage}
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 w-10 h-10 bg-white bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all duration-300"
                  >
                    <ChevronRight className="h-5 w-5" />
                  </Button>
                </>
              )}

              <Button
                variant="ghost"
                size="sm"
                className="absolute top-4 right-4 w-10 h-10 bg-white bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all duration-300"
              >
                <ZoomIn className="h-5 w-5" />
              </Button>
            </div>

            {images.length > 1 && (
              <div className="flex space-x-3 mt-4 overflow-x-auto">
                {images.map((image, index) => (
                  <img
                    key={index}
                    src={image}
                    alt={`${productName} thumbnail ${index + 1}`}
                    className={`w-20 h-20 object-cover rounded-lg cursor-pointer border-2 transition-colors duration-300 ${
                      index === currentImageIndex 
                        ? 'border-gold' 
                        : 'border-transparent hover:border-gold'
                    }`}
                    onClick={() => setCurrentImageIndex(index)}
                  />
                ))}
              </div>
            )}
          </div>

          {/* Product Details */}
          <div className="p-6 lg:p-8">
            <DialogHeader className="flex flex-row justify-between items-start mb-6">
              <div>
                <h1 className="font-playfair text-3xl font-bold text-charcoal mb-2">
                  {productName}
                </h1>
                <p className="text-medium-gray">{t('categories.marble')}</p>
              </div>
              <div className="flex items-center space-x-4">
                <Badge className="px-4 py-2 bg-gold text-white rounded-full font-semibold">
                  {product.code}
                </Badge>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onClose}
                  className="w-10 h-10 text-medium-gray hover:text-charcoal transition-colors duration-300"
                >
                  <X className="h-5 w-5" />
                </Button>
              </div>
            </DialogHeader>

            {/* Description */}
            <div className="mb-8">
              <h3 className="font-playfair text-xl font-semibold text-charcoal mb-4">
                {t('productDetail.description')}
              </h3>
              <p className="text-medium-gray leading-relaxed">
                {fullDescription || productDescription}
              </p>
            </div>

            {/* Specifications */}
            <div className="mb-8">
              <h3 className="font-playfair text-xl font-semibold text-charcoal mb-4">
                {t('productDetail.specifications')}
              </h3>
              <div className="grid md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-medium-gray">{t('productDetail.material')}:</span>
                    <span className="font-medium text-charcoal">
                      {specifications?.material || product.material || 'Natural Stone'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-medium-gray">{t('productDetail.origin')}:</span>
                    <span className="font-medium text-charcoal">
                      {specifications?.origin || product.origin || 'Italy'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-medium-gray">{t('productDetail.finish')}:</span>
                    <span className="font-medium text-charcoal">
                      {specifications?.finish || product.finish || 'Polished'}
                    </span>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-medium-gray">{t('productDetail.thickness')}:</span>
                    <span className="font-medium text-charcoal">
                      {specifications?.thickness || product.thickness || '20mm / 30mm'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-medium-gray">{t('productDetail.application')}:</span>
                    <span className="font-medium text-charcoal">Interior</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-medium-gray">{t('productDetail.availability')}:</span>
                    <span className="font-medium text-green-600">
                      {product.availability || 'In Stock'}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Applications */}
            {applications && applications.length > 0 && (
              <div className="mb-8">
                <h3 className="font-playfair text-xl font-semibold text-charcoal mb-4">
                  {t('productDetail.applications')}
                </h3>
                <div className="flex flex-wrap gap-2">
                  {applications.map((app, index) => (
                    <Badge 
                      key={index}
                      variant="secondary"
                      className="px-3 py-1 bg-beige text-charcoal"
                    >
                      {app}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="space-y-4">
              <Button 
                onClick={handleRequestQuote}
                className="w-full py-4 gold-gradient text-white font-semibold hover:shadow-lg transform hover:scale-105 transition-all duration-300"
              >
                <FileText className="h-5 w-5 mr-2" />
                {t('productDetail.requestQuote')}
              </Button>
              <div className="grid grid-cols-2 gap-4">
                <Button 
                  variant="outline"
                  onClick={handleDownloadBrochure}
                  className="py-3 border-2 border-charcoal text-charcoal hover:bg-charcoal hover:text-white transition-all duration-300"
                >
                  <Download className="h-4 w-4 mr-2" />
                  {t('productDetail.downloadBrochure')}
                </Button>
                <Button 
                  variant="outline"
                  onClick={handleShareProduct}
                  className="py-3 border-2 border-gold text-gold hover:bg-gold hover:text-white transition-all duration-300"
                >
                  <Share2 className="h-4 w-4 mr-2" />
                  {t('productDetail.shareProduct')}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
