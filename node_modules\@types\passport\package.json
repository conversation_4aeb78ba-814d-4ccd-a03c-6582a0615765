{"name": "@types/passport", "version": "1.0.17", "description": "TypeScript definitions for passport", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/passport", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>_<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/horiuchi"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/enaeseth"}, {"name": "<PERSON>", "githubUsername": "theigor", "url": "https://github.com/theigor"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/tlaziuk"}, {"name": "<PERSON>", "githubUsername": "unindented", "url": "https://github.com/unindented"}, {"name": "<PERSON>", "githubUsername": "k<PERSON><PERSON><PERSON>", "url": "https://github.com/kstiehl"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/vaskevich"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/passport"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "d4f27803c3ddfa544699c75815b9e8497176003fae77b77c1ff4876e306ac3f5", "typeScriptVersion": "4.8"}