import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { useLocation } from "wouter";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { ProductCard } from "@/components/product/product-card";
import { ProductModal } from "@/components/product/product-modal";
import { useLanguage } from "@/hooks/use-language";
import { Product, Category } from "@shared/schema";
import { Search, Filter, Grid, List, ChevronLeft, ChevronRight } from "lucide-react";

export default function Products() {
  const [location, navigate] = useLocation();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState("name");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [isProductModalOpen, setIsProductModalOpen] = useState(false);
  const { t, language } = useLanguage();

  const pageSize = 12;

  // Parse URL parameters
  useEffect(() => {
    const params = new URLSearchParams(location.split('?')[1] || '');
    const categoryParam = params.get('category');
    if (categoryParam) {
      setSelectedCategories([categoryParam]);
    }
  }, [location]);

  const { data: categories, isLoading: categoriesLoading } = useQuery({
    queryKey: ['/api/categories'],
    queryFn: async () => {
      const response = await fetch('/api/categories');
      return response.json() as Promise<Category[]>;
    },
  });

  const { data: productsData, isLoading: productsLoading } = useQuery({
    queryKey: ['/api/products', {
      search: searchQuery,
      category_id: selectedCategories[0] || undefined,
      limit: pageSize,
      offset: (currentPage - 1) * pageSize,
      isActive: true
    }],
    queryFn: async () => {
      const params = new URLSearchParams();
      if (searchQuery) params.append('search', searchQuery);
      if (selectedCategories[0]) params.append('categoryId', selectedCategories[0]);
      params.append('limit', pageSize.toString());
      params.append('offset', ((currentPage - 1) * pageSize).toString());
      params.append('isActive', 'true');

      const response = await fetch(`/api/products?${params.toString()}`);
      return response.json() as Promise<{ products: Product[]; total: number }>;
    },
  });

  const totalPages = productsData ? Math.ceil(productsData.total / pageSize) : 1;

  const handleCategoryChange = (categoryId: string, checked: boolean) => {
    if (checked) {
      setSelectedCategories([categoryId]);
    } else {
      setSelectedCategories(selectedCategories.filter(id => id !== categoryId));
    }
    setCurrentPage(1);
  };

  const handleClearFilters = () => {
    setSearchQuery("");
    setSelectedCategories([]);
    setSortBy("name");
    setCurrentPage(1);
  };

  const handleProductClick = (product: Product) => {
    setSelectedProduct(product);
    setIsProductModalOpen(true);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <div className="min-h-screen pt-20">
      <div className="luxury-gradient py-20">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Filters Sidebar */}
            <div className="lg:w-80">
              <Card className="bg-white rounded-2xl elegant-shadow p-6 sticky top-28">
                <h3 className="font-playfair text-2xl font-semibold text-charcoal mb-6">
                  Filters
                </h3>
                
                {/* Search */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-charcoal mb-2">
                    {t('products.search')}
                  </label>
                  <div className="relative">
                    <Input
                      type="text"
                      placeholder={t('products.searchPlaceholder')}
                      value={searchQuery}
                      onChange={(e) => {
                        setSearchQuery(e.target.value);
                        setCurrentPage(1);
                      }}
                      className="w-full pl-10 pr-4 py-3 border-beige focus:border-gold focus:ring-gold"
                    />
                    <Search className="absolute left-3 top-4 h-4 w-4 text-medium-gray" />
                  </div>
                </div>

                {/* Category Filter */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-charcoal mb-3">
                    {t('products.category')}
                  </label>
                  <div className="space-y-2">
                    {categoriesLoading ? (
                      [...Array(4)].map((_, i) => (
                        <Skeleton key={i} className="h-10 w-full" />
                      ))
                    ) : (
                      categories?.map((category) => {
                        const categoryName = language === 'ar' && category.name_ar ? category.name_ar : category.name;
                        return (
                          <label
                            key={category.id}
                            className="flex items-center space-x-3 cursor-pointer hover:bg-beige p-2 rounded-lg transition-colors duration-200"
                          >
                            <Checkbox
                              checked={category.id ? selectedCategories.includes(category.id) : false}
                              onCheckedChange={(checked) => category.id && handleCategoryChange(category.id, !!checked)}
                              className="border-beige data-[state=checked]:bg-gold data-[state=checked]:border-gold"
                            />
                            <span className="text-charcoal">{categoryName}</span>
                          </label>
                        );
                      })
                    )}
                  </div>
                </div>

                {/* Sort Options */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-charcoal mb-2">
                    {t('products.sortBy')}
                  </label>
                  <Select value={sortBy} onValueChange={setSortBy}>
                    <SelectTrigger className="w-full border-beige focus:border-gold">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="name">{t('products.sortOptions.name')}</SelectItem>
                      <SelectItem value="code">{t('products.sortOptions.code')}</SelectItem>
                      <SelectItem value="category">{t('products.sortOptions.category')}</SelectItem>
                      <SelectItem value="newest">{t('products.sortOptions.newest')}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Clear Filters */}
                <Button
                  variant="outline"
                  onClick={handleClearFilters}
                  className="w-full py-3 border-medium-gray text-medium-gray hover:bg-medium-gray hover:text-white transition-all duration-300"
                >
                  {t('products.clearFilters')}
                </Button>
              </Card>
            </div>

            {/* Products Grid */}
            <div className="flex-1">
              <div className="flex justify-between items-center mb-8">
                <div>
                  <h2 className="font-playfair text-3xl font-bold text-charcoal">
                    {t('products.title')}
                  </h2>
                  <p className="text-medium-gray mt-2">
                    {productsData ? t('products.showing', productsData.total) : t('common.loading')}
                  </p>
                </div>
                <div className="flex items-center space-x-4">
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('grid')}
                    className={viewMode === 'grid' ? 'bg-gold text-white' : 'text-medium-gray hover:text-gold'}
                  >
                    <Grid className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('list')}
                    className={viewMode === 'list' ? 'bg-gold text-white' : 'text-medium-gray hover:text-gold'}
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {productsLoading ? (
                <div className={`grid gap-8 ${viewMode === 'grid' ? 'md:grid-cols-2 xl:grid-cols-3' : 'grid-cols-1'}`}>
                  {[...Array(6)].map((_, i) => (
                    <div key={i} className="animate-pulse">
                      <Skeleton className="h-56 w-full rounded-xl mb-4" />
                      <Skeleton className="h-4 w-3/4 mb-2" />
                      <Skeleton className="h-3 w-1/2" />
                    </div>
                  ))}
                </div>
              ) : productsData?.products.length === 0 ? (
                <div className="text-center py-16">
                  <div className="w-24 h-24 mx-auto mb-6 bg-beige rounded-full flex items-center justify-center">
                    <Filter className="h-12 w-12 text-medium-gray" />
                  </div>
                  <h3 className="font-playfair text-2xl font-semibold text-charcoal mb-4">
                    {t('common.noResults')}
                  </h3>
                  <p className="text-medium-gray mb-6">
                    No products match your current filters. Try adjusting your search criteria.
                  </p>
                  <Button
                    onClick={handleClearFilters}
                    className="gold-gradient text-white hover:shadow-lg transition-all duration-300"
                  >
                    {t('products.clearFilters')}
                  </Button>
                </div>
              ) : (
                <>
                  <div className={`grid gap-8 ${viewMode === 'grid' ? 'md:grid-cols-2 xl:grid-cols-3' : 'grid-cols-1'}`}>
                    {productsData?.products.map((product) => (
                      <div key={product.id} onClick={() => handleProductClick(product)}>
                        <ProductCard 
                          product={product}
                          category={categories?.find(c => c.id === product.category_id)}
                        />
                      </div>
                    ))}
                  </div>

                  {/* Pagination */}
                  {totalPages > 1 && (
                    <div className="flex justify-center items-center mt-12 space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                        className="border-beige hover:border-gold hover:text-gold"
                      >
                        <ChevronLeft className="h-4 w-4" />
                      </Button>

                      {[...Array(Math.min(totalPages, 5))].map((_, i) => {
                        const page = i + 1;
                        const isCurrentPage = page === currentPage;
                        return (
                          <Button
                            key={page}
                            variant={isCurrentPage ? "default" : "outline"}
                            size="sm"
                            onClick={() => handlePageChange(page)}
                            className={
                              isCurrentPage 
                                ? "bg-gold text-white" 
                                : "border-beige text-charcoal hover:border-gold hover:text-gold"
                            }
                          >
                            {page}
                          </Button>
                        );
                      })}

                      {totalPages > 5 && (
                        <>
                          <span className="text-medium-gray">...</span>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handlePageChange(totalPages)}
                            className="border-beige text-charcoal hover:border-gold hover:text-gold"
                          >
                            {totalPages}
                          </Button>
                        </>
                      )}

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage === totalPages}
                        className="border-beige hover:border-gold hover:text-gold"
                      >
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Product Detail Modal */}
      <ProductModal
        product={selectedProduct}
        isOpen={isProductModalOpen}
        onClose={() => {
          setIsProductModalOpen(false);
          setSelectedProduct(null);
        }}
      />
    </div>
  );
}
