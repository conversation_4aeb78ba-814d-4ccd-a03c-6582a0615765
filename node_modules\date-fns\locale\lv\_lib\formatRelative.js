"use strict";
exports.formatRelative = void 0;
var _index = require("../../../isSameWeek.js");

const weekdays = [
  "svētdienā",
  "pirmdien<PERSON>",
  "otrdien<PERSON>",
  "trešdien<PERSON>",
  "ceturtdien<PERSON>",
  "piektdien<PERSON>",
  "sestdienā",
];

const formatRelativeLocale = {
  lastWeek: (date, baseDate, options) => {
    if ((0, _index.isSameWeek)(date, baseDate, options)) {
      return "eeee 'plkst.' p";
    }

    const weekday = weekdays[date.getDay()];
    return "'Pagāju<PERSON>ā " + weekday + " plkst.' p";
  },
  yesterday: "'Vakar plkst.' p",
  today: "'<PERSON><PERSON><PERSON> plkst.' p",
  tomorrow: "'Rīt plkst.' p",
  nextWeek: (date, baseDate, options) => {
    if ((0, _index.isSameWeek)(date, baseDate, options)) {
      return "eeee 'plkst.' p";
    }

    const weekday = weekdays[date.getDay()];
    return "'<PERSON><PERSON>ka<PERSON>jā " + weekday + " plkst.' p";
  },
  other: "P",
};

const formatRelative = (token, date, baseDate, options) => {
  const format = formatRelativeLocale[token];

  if (typeof format === "function") {
    return format(date, baseDate, options);
  }

  return format;
};
exports.formatRelative = formatRelative;
