var A=function(I){return A=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(H){return typeof H}:function(H){return H&&typeof Symbol=="function"&&H.constructor===Symbol&&H!==Symbol.prototype?"symbol":typeof H},A(I)},M=function(I,H){var Y=Object.keys(I);if(Object.getOwnPropertySymbols){var Z=Object.getOwnPropertySymbols(I);H&&(Z=Z.filter(function(x){return Object.getOwnPropertyDescriptor(I,x).enumerable})),Y.push.apply(Y,Z)}return Y},N=function(I){for(var H=1;H<arguments.length;H++){var Y=arguments[H]!=null?arguments[H]:{};H%2?M(Object(Y),!0).forEach(function(Z){CC(I,Z,Y[Z])}):Object.getOwnPropertyDescriptors?Object.defineProperties(I,Object.getOwnPropertyDescriptors(Y)):M(Object(Y)).forEach(function(Z){Object.defineProperty(I,Z,Object.getOwnPropertyDescriptor(Y,Z))})}return I},CC=function(I,H,Y){if(H=GC(H),H in I)Object.defineProperty(I,H,{value:Y,enumerable:!0,configurable:!0,writable:!0});else I[H]=Y;return I},GC=function(I){var H=XC(I,"string");return A(H)=="symbol"?H:String(H)},XC=function(I,H){if(A(I)!="object"||!I)return I;var Y=I[Symbol.toPrimitive];if(Y!==void 0){var Z=Y.call(I,H||"default");if(A(Z)!="object")return Z;throw new TypeError("@@toPrimitive must return a primitive value.")}return(H==="string"?String:Number)(I)};(function(I){var H=Object.defineProperty,Y=function G(B,X){for(var C in X)H(B,C,{get:X[C],enumerable:!0,configurable:!0,set:function U(J){return X[C]=function(){return J}}})},Z={lessThanXSeconds:{one:"llai na eiliad",other:"llai na {{count}} eiliad"},xSeconds:{one:"1 eiliad",other:"{{count}} eiliad"},halfAMinute:"hanner munud",lessThanXMinutes:{one:"llai na munud",two:"llai na 2 funud",other:"llai na {{count}} munud"},xMinutes:{one:"1 munud",two:"2 funud",other:"{{count}} munud"},aboutXHours:{one:"tua 1 awr",other:"tua {{count}} awr"},xHours:{one:"1 awr",other:"{{count}} awr"},xDays:{one:"1 diwrnod",two:"2 ddiwrnod",other:"{{count}} diwrnod"},aboutXWeeks:{one:"tua 1 wythnos",two:"tua pythefnos",other:"tua {{count}} wythnos"},xWeeks:{one:"1 wythnos",two:"pythefnos",other:"{{count}} wythnos"},aboutXMonths:{one:"tua 1 mis",two:"tua 2 fis",other:"tua {{count}} mis"},xMonths:{one:"1 mis",two:"2 fis",other:"{{count}} mis"},aboutXYears:{one:"tua 1 flwyddyn",two:"tua 2 flynedd",other:"tua {{count}} mlynedd"},xYears:{one:"1 flwyddyn",two:"2 flynedd",other:"{{count}} mlynedd"},overXYears:{one:"dros 1 flwyddyn",two:"dros 2 flynedd",other:"dros {{count}} mlynedd"},almostXYears:{one:"bron 1 flwyddyn",two:"bron 2 flynedd",other:"bron {{count}} mlynedd"}},x=function G(B,X,C){var U,J=Z[B];if(typeof J==="string")U=J;else if(X===1)U=J.one;else if(X===2&&!!J.two)U=J.two;else U=J.other.replace("{{count}}",String(X));if(C!==null&&C!==void 0&&C.addSuffix)if(C.comparison&&C.comparison>0)return"mewn "+U;else return U+" yn \xF4l";return U};function z(G){return function(){var B=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},X=B.width?String(B.width):G.defaultWidth,C=G.formats[X]||G.formats[G.defaultWidth];return C}}var W={full:"EEEE, d MMMM yyyy",long:"d MMMM yyyy",medium:"d MMM yyyy",short:"dd/MM/yyyy"},$={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},S={full:"{{date}} 'am' {{time}}",long:"{{date}} 'am' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},L={date:z({formats:W,defaultWidth:"full"}),time:z({formats:$,defaultWidth:"full"}),dateTime:z({formats:S,defaultWidth:"full"})},R={lastWeek:"eeee 'diwethaf am' p",yesterday:"'ddoe am' p",today:"'heddiw am' p",tomorrow:"'yfory am' p",nextWeek:"eeee 'am' p",other:"P"},f=function G(B,X,C,U){return R[B]};function Q(G){return function(B,X){var C=X!==null&&X!==void 0&&X.context?String(X.context):"standalone",U;if(C==="formatting"&&G.formattingValues){var J=G.defaultFormattingWidth||G.defaultWidth,T=X!==null&&X!==void 0&&X.width?String(X.width):J;U=G.formattingValues[T]||G.formattingValues[J]}else{var E=G.defaultWidth,K=X!==null&&X!==void 0&&X.width?String(X.width):G.defaultWidth;U=G.values[K]||G.values[E]}var O=G.argumentCallback?G.argumentCallback(B):B;return U[O]}}var V={narrow:["C","O"],abbreviated:["CC","OC"],wide:["Cyn Crist","Ar \xF4l Crist"]},j={narrow:["1","2","3","4"],abbreviated:["Ch1","Ch2","Ch3","Ch4"],wide:["Chwarter 1af","2ail chwarter","3ydd chwarter","4ydd chwarter"]},w={narrow:["I","Ch","Ma","E","Mi","Me","G","A","Md","H","T","Rh"],abbreviated:["Ion","Chwe","Maw","Ebr","Mai","Meh","Gor","Aws","Med","Hyd","Tach","Rhag"],wide:["Ionawr","Chwefror","Mawrth","Ebrill","Mai","Mehefin","Gorffennaf","Awst","Medi","Hydref","Tachwedd","Rhagfyr"]},P={narrow:["S","Ll","M","M","I","G","S"],short:["Su","Ll","Ma","Me","Ia","Gw","Sa"],abbreviated:["Sul","Llun","Maw","Mer","Iau","Gwe","Sad"],wide:["dydd Sul","dydd Llun","dydd Mawrth","dydd Mercher","dydd Iau","dydd Gwener","dydd Sadwrn"]},_={narrow:{am:"b",pm:"h",midnight:"hn",noon:"hd",morning:"bore",afternoon:"prynhawn",evening:"gyda'r nos",night:"nos"},abbreviated:{am:"yb",pm:"yh",midnight:"hanner nos",noon:"hanner dydd",morning:"bore",afternoon:"prynhawn",evening:"gyda'r nos",night:"nos"},wide:{am:"y.b.",pm:"y.h.",midnight:"hanner nos",noon:"hanner dydd",morning:"bore",afternoon:"prynhawn",evening:"gyda'r nos",night:"nos"}},v={narrow:{am:"b",pm:"h",midnight:"hn",noon:"hd",morning:"yn y bore",afternoon:"yn y prynhawn",evening:"gyda'r nos",night:"yn y nos"},abbreviated:{am:"yb",pm:"yh",midnight:"hanner nos",noon:"hanner dydd",morning:"yn y bore",afternoon:"yn y prynhawn",evening:"gyda'r nos",night:"yn y nos"},wide:{am:"y.b.",pm:"y.h.",midnight:"hanner nos",noon:"hanner dydd",morning:"yn y bore",afternoon:"yn y prynhawn",evening:"gyda'r nos",night:"yn y nos"}},F=function G(B,X){var C=Number(B);if(C<20)switch(C){case 0:return C+"fed";case 1:return C+"af";case 2:return C+"ail";case 3:case 4:return C+"ydd";case 5:case 6:return C+"ed";case 7:case 8:case 9:case 10:case 12:case 15:case 18:return C+"fed";case 11:case 13:case 14:case 16:case 17:case 19:return C+"eg"}else if(C>=50&&C<=60||C===80||C>=100)return C+"fed";return C+"ain"},h={ordinalNumber:F,era:Q({values:V,defaultWidth:"wide"}),quarter:Q({values:j,defaultWidth:"wide",argumentCallback:function G(B){return B-1}}),month:Q({values:w,defaultWidth:"wide"}),day:Q({values:P,defaultWidth:"wide"}),dayPeriod:Q({values:_,defaultWidth:"wide",formattingValues:v,defaultFormattingWidth:"wide"})};function q(G){return function(B){var X=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},C=X.width,U=C&&G.matchPatterns[C]||G.matchPatterns[G.defaultMatchWidth],J=B.match(U);if(!J)return null;var T=J[0],E=C&&G.parsePatterns[C]||G.parsePatterns[G.defaultParseWidth],K=Array.isArray(E)?y(E,function(D){return D.test(T)}):k(E,function(D){return D.test(T)}),O;O=G.valueCallback?G.valueCallback(K):K,O=X.valueCallback?X.valueCallback(O):O;var t=B.slice(T.length);return{value:O,rest:t}}}var k=function G(B,X){for(var C in B)if(Object.prototype.hasOwnProperty.call(B,C)&&X(B[C]))return C;return},y=function G(B,X){for(var C=0;C<B.length;C++)if(X(B[C]))return C;return};function m(G){return function(B){var X=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},C=B.match(G.matchPattern);if(!C)return null;var U=C[0],J=B.match(G.parsePattern);if(!J)return null;var T=G.valueCallback?G.valueCallback(J[0]):J[0];T=X.valueCallback?X.valueCallback(T):T;var E=B.slice(U.length);return{value:T,rest:E}}}var c=/^(\d+)(af|ail|ydd|ed|fed|eg|ain)?/i,b=/\d+/i,p={narrow:/^(c|o)/i,abbreviated:/^(c\.?\s?c\.?|o\.?\s?c\.?)/i,wide:/^(cyn christ|ar ôl crist|ar ol crist)/i},g={wide:[/^c/i,/^(ar ôl crist|ar ol crist)/i],any:[/^c/i,/^o/i]},d={narrow:/^[1234]/i,abbreviated:/^ch[1234]/i,wide:/^(chwarter 1af)|([234](ail|ydd)? chwarter)/i},u={any:[/1/i,/2/i,/3/i,/4/i]},l={narrow:/^(i|ch|m|e|g|a|h|t|rh)/i,abbreviated:/^(ion|chwe|maw|ebr|mai|meh|gor|aws|med|hyd|tach|rhag)/i,wide:/^(ionawr|chwefror|mawrth|ebrill|mai|mehefin|gorffennaf|awst|medi|hydref|tachwedd|rhagfyr)/i},i={narrow:[/^i/i,/^ch/i,/^m/i,/^e/i,/^m/i,/^m/i,/^g/i,/^a/i,/^m/i,/^h/i,/^t/i,/^rh/i],any:[/^io/i,/^ch/i,/^maw/i,/^e/i,/^mai/i,/^meh/i,/^g/i,/^a/i,/^med/i,/^h/i,/^t/i,/^rh/i]},n={narrow:/^(s|ll|m|i|g)/i,short:/^(su|ll|ma|me|ia|gw|sa)/i,abbreviated:/^(sul|llun|maw|mer|iau|gwe|sad)/i,wide:/^dydd (sul|llun|mawrth|mercher|iau|gwener|sadwrn)/i},s={narrow:[/^s/i,/^ll/i,/^m/i,/^m/i,/^i/i,/^g/i,/^s/i],wide:[/^dydd su/i,/^dydd ll/i,/^dydd ma/i,/^dydd me/i,/^dydd i/i,/^dydd g/i,/^dydd sa/i],any:[/^su/i,/^ll/i,/^ma/i,/^me/i,/^i/i,/^g/i,/^sa/i]},o={narrow:/^(b|h|hn|hd|(yn y|y|yr|gyda'r) (bore|prynhawn|nos|hwyr))/i,any:/^(y\.?\s?[bh]\.?|hanner nos|hanner dydd|(yn y|y|yr|gyda'r) (bore|prynhawn|nos|hwyr))/i},r={any:{am:/^b|(y\.?\s?b\.?)/i,pm:/^h|(y\.?\s?h\.?)|(yr hwyr)/i,midnight:/^hn|hanner nos/i,noon:/^hd|hanner dydd/i,morning:/bore/i,afternoon:/prynhawn/i,evening:/^gyda'r nos$/i,night:/blah/i}},a={ordinalNumber:m({matchPattern:c,parsePattern:b,valueCallback:function G(B){return parseInt(B,10)}}),era:q({matchPatterns:p,defaultMatchWidth:"wide",parsePatterns:g,defaultParseWidth:"any"}),quarter:q({matchPatterns:d,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any",valueCallback:function G(B){return B+1}}),month:q({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any"}),day:q({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),dayPeriod:q({matchPatterns:o,defaultMatchWidth:"any",parsePatterns:r,defaultParseWidth:"any"})},e={code:"cy",formatDistance:x,formatLong:L,formatRelative:f,localize:h,match:a,options:{weekStartsOn:0,firstWeekContainsDate:1}};window.dateFns=N(N({},window.dateFns),{},{locale:N(N({},(I=window.dateFns)===null||I===void 0?void 0:I.locale),{},{cy:e})})})();

//# debugId=352FF4826F2BAA0664756e2164756e21
