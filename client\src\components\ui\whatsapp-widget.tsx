import React, { useState, useEffect } from 'react';
import './whatsapp-widget.css';
import { FaWhatsapp } from 'react-icons/fa';

const WhatsAppWidget = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (window.location.pathname.includes('/admin')) {
      return;
    }
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 500);
    return () => clearTimeout(timer);
  }, []);

  const togglePopup = () => {
    setIsOpen(!isOpen);
  };

  const handleStartChat = () => {
    const phoneNumber = '1234567890'; // Replace with your phone number
    const message = 'Hi! How can we help you today?'; // Customize your pre-filled message
    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div className="whatsapp-widget-container">
      {!isOpen ? (
        <button className="whatsapp-fab" onClick={togglePopup} aria-label="Open WhatsApp Chat">
          <FaWhatsapp size={40} />
        </button>
      ) : (
        <div className="whatsapp-popup">
          <div className="whatsapp-popup-header">
            <span className="header-title">Chat with Us</span>
            <button className="close-btn" onClick={togglePopup} aria-label="Close Chat Window">
              &times;
            </button>
          </div>
          <div className="whatsapp-popup-body">
            <p>Hi! How can we help you today?</p>
          </div>
          <div className="whatsapp-popup-footer">
            <button className="start-chat-btn" onClick={handleStartChat}>
              Start Chat
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default WhatsAppWidget;
