/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const MapPinPlus = createLucideIcon("MapPinPlus", [
  [
    "path",
    {
      d: "M19.914 11.105A7.298 7.298 0 0 0 20 10a8 8 0 0 0-16 0c0 4.993 5.539 10.193 7.399 11.799a1 1 0 0 0 1.202 0 32 32 0 0 0 .824-.738",
      key: "fcdtly"
    }
  ],
  ["circle", { cx: "12", cy: "10", r: "3", key: "ilqhr7" }],
  ["path", { d: "M16 18h6", key: "987eiv" }],
  ["path", { d: "M19 15v6", key: "10aioa" }]
]);

export { MapPinPlus as default };
//# sourceMappingURL=map-pin-plus.js.map
