import { <PERSON> } from 'wouter';
import { Shield } from 'lucide-react';
import { useLanguage } from '@/hooks/use-language';
import { LanguageSwitcher } from '@/components/ui/language-switcher';
import { Button } from '@/components/ui/button';
import { cn, rtlSpace, rtlMargin } from '@/lib/utils';

export function NavbarControls() {
  const { t, isRTL } = useLanguage();

  return (
    <div className={cn('flex items-center', rtlSpace(isRTL, 'space-x-4'))}>
      <LanguageSwitcher />

      {/* Admin Button */}
      <Link href="/admin" className="hidden lg:block">
        <Button
          variant="default"
          size="sm"
          className="bg-charcoal text-white hover:bg-gold transition-all duration-300">
          <Shield className={cn('h-4 w-4', rtlMargin(isRTL, 'mr-2', 'ml-2'))} />
          {t('nav.admin')}
        </Button>
      </Link>
    </div>
  );
}
