{"name": "memorystore", "version": "1.6.7", "description": "express-session full featured MemoryStore layer without leaks!", "main": "index.js", "types": "index.d.ts", "scripts": {"test": "mocha --check-leaks --bail --no-exit test/"}, "repository": {"type": "git", "url": "git+https://github.com/roccomuso/memorystore.git"}, "engines": {"node": ">=0.10"}, "keywords": ["express-session", "session", "memory", "store", "memorystore", "noleak", "ram"], "author": "<PERSON><PERSON> (roccomuso)", "license": "MIT", "bugs": {"url": "https://github.com/roccomuso/memorystore/issues"}, "homepage": "https://github.com/roccomuso/memorystore#readme", "dependencies": {"debug": "^4.3.0", "lru-cache": "^4.0.3"}, "devDependencies": {"mocha": "9.2.0"}, "standard": {"env": ["mocha"]}}