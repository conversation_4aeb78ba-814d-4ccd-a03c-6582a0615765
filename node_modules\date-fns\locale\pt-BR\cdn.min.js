var O=function(T){return O=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(J){return typeof J}:function(J){return J&&typeof Symbol=="function"&&J.constructor===Symbol&&J!==Symbol.prototype?"symbol":typeof J},O(T)},D=function(T,J){var Y=Object.keys(T);if(Object.getOwnPropertySymbols){var x=Object.getOwnPropertySymbols(T);J&&(x=x.filter(function(z){return Object.getOwnPropertyDescriptor(T,z).enumerable})),Y.push.apply(Y,x)}return Y},K=function(T){for(var J=1;J<arguments.length;J++){var Y=arguments[J]!=null?arguments[J]:{};J%2?D(Object(Y),!0).forEach(function(x){C1(T,x,Y[x])}):Object.getOwnPropertyDescriptors?Object.defineProperties(T,Object.getOwnPropertyDescriptors(Y)):D(Object(Y)).forEach(function(x){Object.defineProperty(T,x,Object.getOwnPropertyDescriptor(Y,x))})}return T},C1=function(T,J,Y){if(J=U1(J),J in T)Object.defineProperty(T,J,{value:Y,enumerable:!0,configurable:!0,writable:!0});else T[J]=Y;return T},U1=function(T){var J=G1(T,"string");return O(J)=="symbol"?J:String(J)},G1=function(T,J){if(O(T)!="object"||!T)return T;var Y=T[Symbol.toPrimitive];if(Y!==void 0){var x=Y.call(T,J||"default");if(O(x)!="object")return x;throw new TypeError("@@toPrimitive must return a primitive value.")}return(J==="string"?String:Number)(T)};(function(T){var J=Object.defineProperty,Y=function U(H,C){for(var G in C)J(H,G,{get:C[G],enumerable:!0,configurable:!0,set:function Z(X){return C[G]=function(){return X}}})},x={lessThanXSeconds:{one:"menos de um segundo",other:"menos de {{count}} segundos"},xSeconds:{one:"1 segundo",other:"{{count}} segundos"},halfAMinute:"meio minuto",lessThanXMinutes:{one:"menos de um minuto",other:"menos de {{count}} minutos"},xMinutes:{one:"1 minuto",other:"{{count}} minutos"},aboutXHours:{one:"cerca de 1 hora",other:"cerca de {{count}} horas"},xHours:{one:"1 hora",other:"{{count}} horas"},xDays:{one:"1 dia",other:"{{count}} dias"},aboutXWeeks:{one:"cerca de 1 semana",other:"cerca de {{count}} semanas"},xWeeks:{one:"1 semana",other:"{{count}} semanas"},aboutXMonths:{one:"cerca de 1 m\xEAs",other:"cerca de {{count}} meses"},xMonths:{one:"1 m\xEAs",other:"{{count}} meses"},aboutXYears:{one:"cerca de 1 ano",other:"cerca de {{count}} anos"},xYears:{one:"1 ano",other:"{{count}} anos"},overXYears:{one:"mais de 1 ano",other:"mais de {{count}} anos"},almostXYears:{one:"quase 1 ano",other:"quase {{count}} anos"}},z=function U(H,C,G){var Z,X=x[H];if(typeof X==="string")Z=X;else if(C===1)Z=X.one;else Z=X.other.replace("{{count}}",String(C));if(G!==null&&G!==void 0&&G.addSuffix)if(G.comparison&&G.comparison>0)return"em "+Z;else return"h\xE1 "+Z;return Z};function N(U){return function(){var H=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},C=H.width?String(H.width):U.defaultWidth,G=U.formats[C]||U.formats[U.defaultWidth];return G}}var R={full:"EEEE, d 'de' MMMM 'de' y",long:"d 'de' MMMM 'de' y",medium:"d MMM y",short:"dd/MM/yyyy"},$={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},S={full:"{{date}} '\xE0s' {{time}}",long:"{{date}} '\xE0s' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},M={date:N({formats:R,defaultWidth:"full"}),time:N({formats:$,defaultWidth:"full"}),dateTime:N({formats:S,defaultWidth:"full"})},L={lastWeek:function U(H){var C=H.getDay(),G=C===0||C===6?"\xFAltimo":"\xFAltima";return"'"+G+"' eeee '\xE0s' p"},yesterday:"'ontem \xE0s' p",today:"'hoje \xE0s' p",tomorrow:"'amanh\xE3 \xE0s' p",nextWeek:"eeee '\xE0s' p",other:"P"},V=function U(H,C,G,Z){var X=L[H];if(typeof X==="function")return X(C);return X};function q(U){return function(H,C){var G=C!==null&&C!==void 0&&C.context?String(C.context):"standalone",Z;if(G==="formatting"&&U.formattingValues){var X=U.defaultFormattingWidth||U.defaultWidth,B=C!==null&&C!==void 0&&C.width?String(C.width):X;Z=U.formattingValues[B]||U.formattingValues[X]}else{var E=U.defaultWidth,Q=C!==null&&C!==void 0&&C.width?String(C.width):U.defaultWidth;Z=U.values[Q]||U.values[E]}var I=U.argumentCallback?U.argumentCallback(H):H;return Z[I]}}var j={narrow:["AC","DC"],abbreviated:["AC","DC"],wide:["antes de cristo","depois de cristo"]},f={narrow:["1","2","3","4"],abbreviated:["T1","T2","T3","T4"],wide:["1\xBA trimestre","2\xBA trimestre","3\xBA trimestre","4\xBA trimestre"]},v={narrow:["j","f","m","a","m","j","j","a","s","o","n","d"],abbreviated:["jan","fev","mar","abr","mai","jun","jul","ago","set","out","nov","dez"],wide:["janeiro","fevereiro","mar\xE7o","abril","maio","junho","julho","agosto","setembro","outubro","novembro","dezembro"]},P={narrow:["D","S","T","Q","Q","S","S"],short:["dom","seg","ter","qua","qui","sex","sab"],abbreviated:["domingo","segunda","ter\xE7a","quarta","quinta","sexta","s\xE1bado"],wide:["domingo","segunda-feira","ter\xE7a-feira","quarta-feira","quinta-feira","sexta-feira","s\xE1bado"]},w={narrow:{am:"a",pm:"p",midnight:"mn",noon:"md",morning:"manh\xE3",afternoon:"tarde",evening:"tarde",night:"noite"},abbreviated:{am:"AM",pm:"PM",midnight:"meia-noite",noon:"meio-dia",morning:"manh\xE3",afternoon:"tarde",evening:"tarde",night:"noite"},wide:{am:"a.m.",pm:"p.m.",midnight:"meia-noite",noon:"meio-dia",morning:"manh\xE3",afternoon:"tarde",evening:"tarde",night:"noite"}},_={narrow:{am:"a",pm:"p",midnight:"mn",noon:"md",morning:"da manh\xE3",afternoon:"da tarde",evening:"da tarde",night:"da noite"},abbreviated:{am:"AM",pm:"PM",midnight:"meia-noite",noon:"meio-dia",morning:"da manh\xE3",afternoon:"da tarde",evening:"da tarde",night:"da noite"},wide:{am:"a.m.",pm:"p.m.",midnight:"meia-noite",noon:"meio-dia",morning:"da manh\xE3",afternoon:"da tarde",evening:"da tarde",night:"da noite"}},F=function U(H,C){var G=Number(H);if((C===null||C===void 0?void 0:C.unit)==="week")return G+"\xAA";return G+"\xBA"},h={ordinalNumber:F,era:q({values:j,defaultWidth:"wide"}),quarter:q({values:f,defaultWidth:"wide",argumentCallback:function U(H){return H-1}}),month:q({values:v,defaultWidth:"wide"}),day:q({values:P,defaultWidth:"wide"}),dayPeriod:q({values:w,defaultWidth:"wide",formattingValues:_,defaultFormattingWidth:"wide"})};function A(U){return function(H){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},G=C.width,Z=G&&U.matchPatterns[G]||U.matchPatterns[U.defaultMatchWidth],X=H.match(Z);if(!X)return null;var B=X[0],E=G&&U.parsePatterns[G]||U.parsePatterns[U.defaultParseWidth],Q=Array.isArray(E)?k(E,function(W){return W.test(B)}):b(E,function(W){return W.test(B)}),I;I=U.valueCallback?U.valueCallback(Q):Q,I=C.valueCallback?C.valueCallback(I):I;var t=H.slice(B.length);return{value:I,rest:t}}}var b=function U(H,C){for(var G in H)if(Object.prototype.hasOwnProperty.call(H,G)&&C(H[G]))return G;return},k=function U(H,C){for(var G=0;G<H.length;G++)if(C(H[G]))return G;return};function m(U){return function(H){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},G=H.match(U.matchPattern);if(!G)return null;var Z=G[0],X=H.match(U.parsePattern);if(!X)return null;var B=U.valueCallback?U.valueCallback(X[0]):X[0];B=C.valueCallback?C.valueCallback(B):B;var E=H.slice(Z.length);return{value:B,rest:E}}}var c=/^(\d+)[ºªo]?/i,y=/\d+/i,u={narrow:/^(ac|dc|a|d)/i,abbreviated:/^(a\.?\s?c\.?|d\.?\s?c\.?)/i,wide:/^(antes de cristo|depois de cristo)/i},g={any:[/^ac/i,/^dc/i],wide:[/^antes de cristo/i,/^depois de cristo/i]},p={narrow:/^[1234]/i,abbreviated:/^T[1234]/i,wide:/^[1234](º)? trimestre/i},d={any:[/1/i,/2/i,/3/i,/4/i]},l={narrow:/^[jfmajsond]/i,abbreviated:/^(jan|fev|mar|abr|mai|jun|jul|ago|set|out|nov|dez)/i,wide:/^(janeiro|fevereiro|março|abril|maio|junho|julho|agosto|setembro|outubro|novembro|dezembro)/i},i={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^fev/i,/^mar/i,/^abr/i,/^mai/i,/^jun/i,/^jul/i,/^ago/i,/^set/i,/^out/i,/^nov/i,/^dez/i]},n={narrow:/^(dom|[23456]ª?|s[aá]b)/i,short:/^(dom|[23456]ª?|s[aá]b)/i,abbreviated:/^(dom|seg|ter|qua|qui|sex|s[aá]b)/i,wide:/^(domingo|(segunda|ter[cç]a|quarta|quinta|sexta)([- ]feira)?|s[aá]bado)/i},s={short:[/^d/i,/^2/i,/^3/i,/^4/i,/^5/i,/^6/i,/^s[aá]/i],narrow:[/^d/i,/^2/i,/^3/i,/^4/i,/^5/i,/^6/i,/^s[aá]/i],any:[/^d/i,/^seg/i,/^t/i,/^qua/i,/^qui/i,/^sex/i,/^s[aá]b/i]},o={narrow:/^(a|p|mn|md|(da) (manhã|tarde|noite))/i,any:/^([ap]\.?\s?m\.?|meia[-\s]noite|meio[-\s]dia|(da) (manhã|tarde|noite))/i},r={any:{am:/^a/i,pm:/^p/i,midnight:/^mn|^meia[-\s]noite/i,noon:/^md|^meio[-\s]dia/i,morning:/manhã/i,afternoon:/tarde/i,evening:/tarde/i,night:/noite/i}},a={ordinalNumber:m({matchPattern:c,parsePattern:y,valueCallback:function U(H){return parseInt(H,10)}}),era:A({matchPatterns:u,defaultMatchWidth:"wide",parsePatterns:g,defaultParseWidth:"any"}),quarter:A({matchPatterns:p,defaultMatchWidth:"wide",parsePatterns:d,defaultParseWidth:"any",valueCallback:function U(H){return H+1}}),month:A({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any"}),day:A({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),dayPeriod:A({matchPatterns:o,defaultMatchWidth:"any",parsePatterns:r,defaultParseWidth:"any"})},e={code:"pt-BR",formatDistance:z,formatLong:M,formatRelative:V,localize:h,match:a,options:{weekStartsOn:0,firstWeekContainsDate:1}};window.dateFns=K(K({},window.dateFns),{},{locale:K(K({},(T=window.dateFns)===null||T===void 0?void 0:T.locale),{},{ptBR:e})})})();

//# debugId=3CDFAC1EBFB2B95A64756e2164756e21
