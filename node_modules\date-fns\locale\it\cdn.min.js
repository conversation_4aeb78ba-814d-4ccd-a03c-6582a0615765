var O=function(J,G){var Y=Object.keys(J);if(Object.getOwnPropertySymbols){var Z=Object.getOwnPropertySymbols(J);G&&(Z=Z.filter(function(R){return Object.getOwnPropertyDescriptor(J,R).enumerable})),Y.push.apply(Y,Z)}return Y},M=function(J){for(var G=1;G<arguments.length;G++){var Y=arguments[G]!=null?arguments[G]:{};G%2?O(Object(Y),!0).forEach(function(Z){ZH(J,Z,Y[Z])}):Object.getOwnPropertyDescriptors?Object.defineProperties(J,Object.getOwnPropertyDescriptors(Y)):O(Object(Y)).forEach(function(Z){Object.defineProperty(J,Z,Object.getOwnPropertyDescriptor(Y,Z))})}return J},ZH=function(J,G,Y){if(G=TH(G),G in J)Object.defineProperty(J,G,{value:Y,enumerable:!0,configurable:!0,writable:!0});else J[G]=Y;return J},TH=function(J){var G=QH(J,"string");return K(G)=="symbol"?G:String(G)},QH=function(J,G){if(K(J)!="object"||!J)return J;var Y=J[Symbol.toPrimitive];if(Y!==void 0){var Z=Y.call(J,G||"default");if(K(Z)!="object")return Z;throw new TypeError("@@toPrimitive must return a primitive value.")}return(G==="string"?String:Number)(J)},K=function(J){return K=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(G){return typeof G}:function(G){return G&&typeof Symbol=="function"&&G.constructor===Symbol&&G!==Symbol.prototype?"symbol":typeof G},K(J)};(function(J){var G=Object.defineProperty,Y=function B(H,C){for(var E in C)G(H,E,{get:C[E],enumerable:!0,configurable:!0,set:function I(U){return C[E]=function(){return U}}})},Z={lessThanXSeconds:{one:"meno di un secondo",other:"meno di {{count}} secondi"},xSeconds:{one:"un secondo",other:"{{count}} secondi"},halfAMinute:"alcuni secondi",lessThanXMinutes:{one:"meno di un minuto",other:"meno di {{count}} minuti"},xMinutes:{one:"un minuto",other:"{{count}} minuti"},aboutXHours:{one:"circa un'ora",other:"circa {{count}} ore"},xHours:{one:"un'ora",other:"{{count}} ore"},xDays:{one:"un giorno",other:"{{count}} giorni"},aboutXWeeks:{one:"circa una settimana",other:"circa {{count}} settimane"},xWeeks:{one:"una settimana",other:"{{count}} settimane"},aboutXMonths:{one:"circa un mese",other:"circa {{count}} mesi"},xMonths:{one:"un mese",other:"{{count}} mesi"},aboutXYears:{one:"circa un anno",other:"circa {{count}} anni"},xYears:{one:"un anno",other:"{{count}} anni"},overXYears:{one:"pi\xF9 di un anno",other:"pi\xF9 di {{count}} anni"},almostXYears:{one:"quasi un anno",other:"quasi {{count}} anni"}},R=function B(H,C,E){var I,U=Z[H];if(typeof U==="string")I=U;else if(C===1)I=U.one;else I=U.other.replace("{{count}}",C.toString());if(E!==null&&E!==void 0&&E.addSuffix)if(E.comparison&&E.comparison>0)return"tra "+I;else return I+" fa";return I};function V(B){return function(){var H=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},C=H.width?String(H.width):B.defaultWidth,E=B.formats[C]||B.formats[B.defaultWidth];return E}}var P={full:"EEEE d MMMM y",long:"d MMMM y",medium:"d MMM y",short:"dd/MM/y"},$={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},v={full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},F={date:V({formats:P,defaultWidth:"full"}),time:V({formats:$,defaultWidth:"full"}),dateTime:V({formats:v,defaultWidth:"full"})};function w(B){var H=Object.prototype.toString.call(B);if(B instanceof Date||K(B)==="object"&&H==="[object Date]")return new B.constructor(+B);else if(typeof B==="number"||H==="[object Number]"||typeof B==="string"||H==="[object String]")return new Date(B);else return new Date(NaN)}function h(){return W}function qH(B){W=B}var W={};function D(B,H){var C,E,I,U,X,T,q=h(),Q=(C=(E=(I=(U=H===null||H===void 0?void 0:H.weekStartsOn)!==null&&U!==void 0?U:H===null||H===void 0||(X=H.locale)===null||X===void 0||(X=X.options)===null||X===void 0?void 0:X.weekStartsOn)!==null&&I!==void 0?I:q.weekStartsOn)!==null&&E!==void 0?E:(T=q.locale)===null||T===void 0||(T=T.options)===null||T===void 0?void 0:T.weekStartsOn)!==null&&C!==void 0?C:0,A=w(B),z=A.getDay(),YH=(z<Q?7:0)+z-Q;return A.setDate(A.getDate()-YH),A.setHours(0,0,0,0),A}function j(B,H,C){var E=D(B,C),I=D(H,C);return+E===+I}var b=function B(H){switch(H){case 0:return"'domenica scorsa alle' p";default:return"'"+L[H]+" scorso alle' p"}},S=function B(H){return"'"+L[H]+" alle' p"},k=function B(H){switch(H){case 0:return"'domenica prossima alle' p";default:return"'"+L[H]+" prossimo alle' p"}},L=["domenica","luned\xEC","marted\xEC","mercoled\xEC","gioved\xEC","venerd\xEC","sabato"],f={lastWeek:function B(H,C,E){var I=H.getDay();if(j(H,C,E))return S(I);else return b(I)},yesterday:"'ieri alle' p",today:"'oggi alle' p",tomorrow:"'domani alle' p",nextWeek:function B(H,C,E){var I=H.getDay();if(j(H,C,E))return S(I);else return k(I)},other:"P"},_=function B(H,C,E,I){var U=f[H];if(typeof U==="function")return U(C,E,I);return U};function x(B){return function(H,C){var E=C!==null&&C!==void 0&&C.context?String(C.context):"standalone",I;if(E==="formatting"&&B.formattingValues){var U=B.defaultFormattingWidth||B.defaultWidth,X=C!==null&&C!==void 0&&C.width?String(C.width):U;I=B.formattingValues[X]||B.formattingValues[U]}else{var T=B.defaultWidth,q=C!==null&&C!==void 0&&C.width?String(C.width):B.defaultWidth;I=B.values[q]||B.values[T]}var Q=B.argumentCallback?B.argumentCallback(H):H;return I[Q]}}var c={narrow:["aC","dC"],abbreviated:["a.C.","d.C."],wide:["avanti Cristo","dopo Cristo"]},m={narrow:["1","2","3","4"],abbreviated:["T1","T2","T3","T4"],wide:["1\xBA trimestre","2\xBA trimestre","3\xBA trimestre","4\xBA trimestre"]},y={narrow:["G","F","M","A","M","G","L","A","S","O","N","D"],abbreviated:["gen","feb","mar","apr","mag","giu","lug","ago","set","ott","nov","dic"],wide:["gennaio","febbraio","marzo","aprile","maggio","giugno","luglio","agosto","settembre","ottobre","novembre","dicembre"]},u={narrow:["D","L","M","M","G","V","S"],short:["dom","lun","mar","mer","gio","ven","sab"],abbreviated:["dom","lun","mar","mer","gio","ven","sab"],wide:["domenica","luned\xEC","marted\xEC","mercoled\xEC","gioved\xEC","venerd\xEC","sabato"]},g={narrow:{am:"m.",pm:"p.",midnight:"mezzanotte",noon:"mezzogiorno",morning:"mattina",afternoon:"pomeriggio",evening:"sera",night:"notte"},abbreviated:{am:"AM",pm:"PM",midnight:"mezzanotte",noon:"mezzogiorno",morning:"mattina",afternoon:"pomeriggio",evening:"sera",night:"notte"},wide:{am:"AM",pm:"PM",midnight:"mezzanotte",noon:"mezzogiorno",morning:"mattina",afternoon:"pomeriggio",evening:"sera",night:"notte"}},p={narrow:{am:"m.",pm:"p.",midnight:"mezzanotte",noon:"mezzogiorno",morning:"di mattina",afternoon:"del pomeriggio",evening:"di sera",night:"di notte"},abbreviated:{am:"AM",pm:"PM",midnight:"mezzanotte",noon:"mezzogiorno",morning:"di mattina",afternoon:"del pomeriggio",evening:"di sera",night:"di notte"},wide:{am:"AM",pm:"PM",midnight:"mezzanotte",noon:"mezzogiorno",morning:"di mattina",afternoon:"del pomeriggio",evening:"di sera",night:"di notte"}},l=function B(H,C){var E=Number(H);return String(E)},d={ordinalNumber:l,era:x({values:c,defaultWidth:"wide"}),quarter:x({values:m,defaultWidth:"wide",argumentCallback:function B(H){return H-1}}),month:x({values:y,defaultWidth:"wide"}),day:x({values:u,defaultWidth:"wide"}),dayPeriod:x({values:g,defaultWidth:"wide",formattingValues:p,defaultFormattingWidth:"wide"})};function N(B){return function(H){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},E=C.width,I=E&&B.matchPatterns[E]||B.matchPatterns[B.defaultMatchWidth],U=H.match(I);if(!U)return null;var X=U[0],T=E&&B.parsePatterns[E]||B.parsePatterns[B.defaultParseWidth],q=Array.isArray(T)?n(T,function(z){return z.test(X)}):i(T,function(z){return z.test(X)}),Q;Q=B.valueCallback?B.valueCallback(q):q,Q=C.valueCallback?C.valueCallback(Q):Q;var A=H.slice(X.length);return{value:Q,rest:A}}}var i=function B(H,C){for(var E in H)if(Object.prototype.hasOwnProperty.call(H,E)&&C(H[E]))return E;return},n=function B(H,C){for(var E=0;E<H.length;E++)if(C(H[E]))return E;return};function r(B){return function(H){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},E=H.match(B.matchPattern);if(!E)return null;var I=E[0],U=H.match(B.parsePattern);if(!U)return null;var X=B.valueCallback?B.valueCallback(U[0]):U[0];X=C.valueCallback?C.valueCallback(X):X;var T=H.slice(I.length);return{value:X,rest:T}}}var s=/^(\d+)(º)?/i,o=/\d+/i,a={narrow:/^(aC|dC)/i,abbreviated:/^(a\.?\s?C\.?|a\.?\s?e\.?\s?v\.?|d\.?\s?C\.?|e\.?\s?v\.?)/i,wide:/^(avanti Cristo|avanti Era Volgare|dopo Cristo|Era Volgare)/i},e={any:[/^a/i,/^(d|e)/i]},t={narrow:/^[1234]/i,abbreviated:/^t[1234]/i,wide:/^[1234](º)? trimestre/i},HH={any:[/1/i,/2/i,/3/i,/4/i]},BH={narrow:/^[gfmalsond]/i,abbreviated:/^(gen|feb|mar|apr|mag|giu|lug|ago|set|ott|nov|dic)/i,wide:/^(gennaio|febbraio|marzo|aprile|maggio|giugno|luglio|agosto|settembre|ottobre|novembre|dicembre)/i},CH={narrow:[/^g/i,/^f/i,/^m/i,/^a/i,/^m/i,/^g/i,/^l/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ge/i,/^f/i,/^mar/i,/^ap/i,/^mag/i,/^gi/i,/^l/i,/^ag/i,/^s/i,/^o/i,/^n/i,/^d/i]},EH={narrow:/^[dlmgvs]/i,short:/^(do|lu|ma|me|gi|ve|sa)/i,abbreviated:/^(dom|lun|mar|mer|gio|ven|sab)/i,wide:/^(domenica|luned[i|ì]|marted[i|ì]|mercoled[i|ì]|gioved[i|ì]|venerd[i|ì]|sabato)/i},GH={narrow:[/^d/i,/^l/i,/^m/i,/^m/i,/^g/i,/^v/i,/^s/i],any:[/^d/i,/^l/i,/^ma/i,/^me/i,/^g/i,/^v/i,/^s/i]},IH={narrow:/^(a|m\.|p|mezzanotte|mezzogiorno|(di|del) (mattina|pomeriggio|sera|notte))/i,any:/^([ap]\.?\s?m\.?|mezzanotte|mezzogiorno|(di|del) (mattina|pomeriggio|sera|notte))/i},JH={any:{am:/^a/i,pm:/^p/i,midnight:/^mezza/i,noon:/^mezzo/i,morning:/mattina/i,afternoon:/pomeriggio/i,evening:/sera/i,night:/notte/i}},UH={ordinalNumber:r({matchPattern:s,parsePattern:o,valueCallback:function B(H){return parseInt(H,10)}}),era:N({matchPatterns:a,defaultMatchWidth:"wide",parsePatterns:e,defaultParseWidth:"any"}),quarter:N({matchPatterns:t,defaultMatchWidth:"wide",parsePatterns:HH,defaultParseWidth:"any",valueCallback:function B(H){return H+1}}),month:N({matchPatterns:BH,defaultMatchWidth:"wide",parsePatterns:CH,defaultParseWidth:"any"}),day:N({matchPatterns:EH,defaultMatchWidth:"wide",parsePatterns:GH,defaultParseWidth:"any"}),dayPeriod:N({matchPatterns:IH,defaultMatchWidth:"any",parsePatterns:JH,defaultParseWidth:"any"})},XH={code:"it",formatDistance:R,formatLong:F,formatRelative:_,localize:d,match:UH,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=M(M({},window.dateFns),{},{locale:M(M({},(J=window.dateFns)===null||J===void 0?void 0:J.locale),{},{it:XH})})})();

//# debugId=12BD94BC9D28CF0964756e2164756e21
