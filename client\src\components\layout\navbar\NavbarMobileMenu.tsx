import { Link } from 'wouter';
import { <PERSON>u, Shield } from 'lucide-react';
import { useLanguage } from '@/hooks/use-language';
import { Button } from '@/components/ui/button';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { cn, rtlMargin } from '@/lib/utils';

interface NavItem {
  href: string;
  label: string;
}

interface NavbarMobileMenuProps {
  navItems: NavItem[];
  currentLocation: string;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

export function NavbarMobileMenu({ 
  navItems, 
  currentLocation, 
  isOpen, 
  onOpenChange 
}: NavbarMobileMenuProps) {
  const { t, isRTL } = useLanguage();

  return (
    <Sheet open={isOpen} onOpenChange={onOpenChange}>
      <SheetTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="lg:hidden text-charcoal hover:text-gold transition-colors duration-300">
          <Menu className="h-6 w-6" />
        </Button>
      </SheetTrigger>
      <SheetContent side={isRTL ? 'left' : 'right'} className="w-80">
        <div className="flex flex-col space-y-4 mt-8">
          {navItems.map(item => (
            <Link
              key={item.href}
              href={item.href}
              onClick={() => onOpenChange(false)}
              className={`text-lg transition-colors duration-300 font-medium ${
                currentLocation === item.href ? 'text-gold' : 'text-charcoal hover:text-gold'
              }`}>
              {item.label}
            </Link>
          ))}
          <Link href="/admin" onClick={() => onOpenChange(false)}>
            <Button
              variant="default"
              className="w-full bg-charcoal text-white hover:bg-gold transition-all duration-300">
              <Shield className={cn('h-4 w-4', rtlMargin(isRTL, 'mr-2', 'ml-2'))} />
              {t('nav.admin')}
            </Button>
          </Link>
        </div>
      </SheetContent>
    </Sheet>
  );
}
