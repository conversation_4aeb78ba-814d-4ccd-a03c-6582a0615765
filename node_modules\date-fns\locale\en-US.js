"use strict";
exports.enUS = void 0;
var _index = require("./en-US/_lib/formatDistance.js");
var _index2 = require("./en-US/_lib/formatLong.js");
var _index3 = require("./en-US/_lib/formatRelative.js");
var _index4 = require("./en-US/_lib/localize.js");
var _index5 = require("./en-US/_lib/match.js");

/**
 * @category Locales
 * @summary English locale (United States).
 * @language English
 * @iso-639-2 eng
 * <AUTHOR> [@kossnocorp](https://github.com/kossnocorp)
 * <AUTHOR> [@leshakoss](https://github.com/leshakoss)
 */
const enUS = (exports.enUS = {
  code: "en-US",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 0 /* Sunday */,
    firstWeekContainsDate: 1,
  },
});
