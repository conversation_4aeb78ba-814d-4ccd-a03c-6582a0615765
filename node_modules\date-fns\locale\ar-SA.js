"use strict";
exports.arSA = void 0;
var _index = require("./ar-SA/_lib/formatDistance.js");
var _index2 = require("./ar-SA/_lib/formatLong.js");
var _index3 = require("./ar-SA/_lib/formatRelative.js");
var _index4 = require("./ar-SA/_lib/localize.js");
var _index5 = require("./ar-SA/_lib/match.js");

/**
 * @category Locales
 * @summary Arabic locale (Sauid Arabic).
 * @language Arabic
 * @iso-639-2 ara
 * <AUTHOR> [@dalwadani](https://github.com/dalwadani)
 */
const arSA = (exports.arSA = {
  code: "ar-SA",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 0 /* Sunday */,
    firstWeekContainsDate: 1,
  },
});
