var O=function(H){return O=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(G){return typeof G}:function(G){return G&&typeof Symbol=="function"&&G.constructor===Symbol&&G!==Symbol.prototype?"symbol":typeof G},O(H)},z=function(H,G){var J=Object.keys(H);if(Object.getOwnPropertySymbols){var Z=Object.getOwnPropertySymbols(H);G&&(Z=Z.filter(function(W){return Object.getOwnPropertyDescriptor(H,W).enumerable})),J.push.apply(J,Z)}return J},K=function(H){for(var G=1;G<arguments.length;G++){var J=arguments[G]!=null?arguments[G]:{};G%2?z(Object(J),!0).forEach(function(Z){C0(H,Z,J[Z])}):Object.getOwnPropertyDescriptors?Object.defineProperties(H,Object.getOwnPropertyDescriptors(J)):z(Object(J)).forEach(function(Z){Object.defineProperty(H,Z,Object.getOwnPropertyDescriptor(J,Z))})}return H},C0=function(H,G,J){if(G=X0(G),G in H)Object.defineProperty(H,G,{value:J,enumerable:!0,configurable:!0,writable:!0});else H[G]=J;return H},X0=function(H){var G=B0(H,"string");return O(G)=="symbol"?G:String(G)},B0=function(H,G){if(O(H)!="object"||!H)return H;var J=H[Symbol.toPrimitive];if(J!==void 0){var Z=J.call(H,G||"default");if(O(Z)!="object")return Z;throw new TypeError("@@toPrimitive must return a primitive value.")}return(G==="string"?String:Number)(H)};(function(H){var G=Object.defineProperty,J=function C(B,X){for(var E in X)G(B,E,{get:X[E],enumerable:!0,configurable:!0,set:function U(Y){return X[E]=function(){return Y}}})},Z={lessThanXSeconds:{one:"\u06A9\u06D5\u0645\u062A\u0631 \u0644\u06D5 \u06CC\u06D5\u06A9 \u0686\u0631\u06A9\u06D5",other:"\u06A9\u06D5\u0645\u062A\u0631 \u0644\u06D5 {{count}} \u0686\u0631\u06A9\u06D5"},xSeconds:{one:"1 \u0686\u0631\u06A9\u06D5",other:"{{count}} \u0686\u0631\u06A9\u06D5"},halfAMinute:"\u0646\u06CC\u0648 \u06A9\u0627\u062A\u0698\u0645\u06CE\u0631",lessThanXMinutes:{one:"\u06A9\u06D5\u0645\u062A\u0631 \u0644\u06D5 \u06CC\u06D5\u06A9 \u062E\u0648\u0644\u06D5\u06A9",other:"\u06A9\u06D5\u0645\u062A\u0631 \u0644\u06D5 {{count}} \u062E\u0648\u0644\u06D5\u06A9"},xMinutes:{one:"1 \u062E\u0648\u0644\u06D5\u06A9",other:"{{count}} \u062E\u0648\u0644\u06D5\u06A9"},aboutXHours:{one:"\u062F\u06D5\u0648\u0631\u0648\u0628\u06D5\u0631\u06CC 1 \u06A9\u0627\u062A\u0698\u0645\u06CE\u0631",other:"\u062F\u06D5\u0648\u0631\u0648\u0628\u06D5\u0631\u06CC {{count}} \u06A9\u0627\u062A\u0698\u0645\u06CE\u0631"},xHours:{one:"1 \u06A9\u0627\u062A\u0698\u0645\u06CE\u0631",other:"{{count}} \u06A9\u0627\u062A\u0698\u0645\u06CE\u0631"},xDays:{one:"1 \u0695\u06C6\u0698",other:"{{count}} \u0698\u06C6\u0698"},aboutXWeeks:{one:"\u062F\u06D5\u0648\u0631\u0648\u0628\u06D5\u0631\u06CC 1 \u0647\u06D5\u0641\u062A\u06D5",other:"\u062F\u0648\u0631\u0648\u0628\u06D5\u0631\u06CC {{count}} \u0647\u06D5\u0641\u062A\u06D5"},xWeeks:{one:"1 \u0647\u06D5\u0641\u062A\u06D5",other:"{{count}} \u0647\u06D5\u0641\u062A\u06D5"},aboutXMonths:{one:"\u062F\u0627\u0648\u0631\u0648\u0628\u06D5\u0631\u06CC 1 \u0645\u0627\u0646\u06AF",other:"\u062F\u06D5\u0648\u0631\u0648\u0628\u06D5\u0631\u06CC {{count}} \u0645\u0627\u0646\u06AF"},xMonths:{one:"1 \u0645\u0627\u0646\u06AF",other:"{{count}} \u0645\u0627\u0646\u06AF"},aboutXYears:{one:"\u062F\u06D5\u0648\u0631\u0648\u0628\u06D5\u0631\u06CC  1 \u0633\u0627\u06B5",other:"\u062F\u06D5\u0648\u0631\u0648\u0628\u06D5\u0631\u06CC {{count}} \u0633\u0627\u06B5"},xYears:{one:"1 \u0633\u0627\u06B5",other:"{{count}} \u0633\u0627\u06B5"},overXYears:{one:"\u0632\u06CC\u0627\u062A\u0631 \u0644\u06D5 \u0633\u0627\u06B5\u06CE\u06A9",other:"\u0632\u06CC\u0627\u062A\u0631 \u0644\u06D5 {{count}} \u0633\u0627\u06B5"},almostXYears:{one:"\u0628\u06D5\u0646\u0632\u06CC\u06A9\u06D5\u06CC\u06CC \u0633\u0627\u06B5\u06CE\u06A9  ",other:"\u0628\u06D5\u0646\u0632\u06CC\u06A9\u06D5\u06CC\u06CC {{count}} \u0633\u0627\u06B5"}},W=function C(B,X,E){var U,Y=Z[B];if(typeof Y==="string")U=Y;else if(X===1)U=Y.one;else U=Y.other.replace("{{count}}",X.toString());if(E!==null&&E!==void 0&&E.addSuffix)if(E.comparison&&E.comparison>0)return"\u0644\u06D5 \u0645\u0627\u0648\u06D5\u06CC "+U+"\u062F\u0627";else return U+"\u067E\u06CE\u0634 \u0626\u06CE\u0633\u062A\u0627";return U};function x(C){return function(){var B=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},X=B.width?String(B.width):C.defaultWidth,E=C.formats[X]||C.formats[C.defaultWidth];return E}}var $={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},M={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},S={full:"{{date}} '\u06A9\u0627\u062A\u0698\u0645\u06CE\u0631' {{time}}",long:"{{date}} '\u06A9\u0627\u062A\u0698\u0645\u06CE\u0631' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},L={date:x({formats:$,defaultWidth:"full"}),time:x({formats:M,defaultWidth:"full"}),dateTime:x({formats:S,defaultWidth:"full"})},R={lastWeek:"'\u0647\u06D5\u0641\u062A\u06D5\u06CC \u0695\u0627\u0628\u0631\u062F\u0648\u0648' eeee '\u06A9\u0627\u062A\u0698\u0645\u06CE\u0631' p",yesterday:"'\u062F\u0648\u06CE\u0646\u06CE \u06A9\u0627\u062A\u0698\u0645\u06CE\u0631' p",today:"'\u0626\u06D5\u0645\u0695\u06C6 \u06A9\u0627\u062A\u0698\u0645\u06CE\u0631' p",tomorrow:"'\u0628\u06D5\u06CC\u0627\u0646\u06CC \u06A9\u0627\u062A\u0698\u0645\u06CE\u0631' p",nextWeek:"eeee '\u06A9\u0627\u062A\u0698\u0645\u06CE\u0631' p",other:"P"},V=function C(B,X,E,U){return R[B]};function q(C){return function(B,X){var E=X!==null&&X!==void 0&&X.context?String(X.context):"standalone",U;if(E==="formatting"&&C.formattingValues){var Y=C.defaultFormattingWidth||C.defaultWidth,A=X!==null&&X!==void 0&&X.width?String(X.width):Y;U=C.formattingValues[A]||C.formattingValues[Y]}else{var I=C.defaultWidth,Q=X!==null&&X!==void 0&&X.width?String(X.width):C.defaultWidth;U=C.values[Q]||C.values[I]}var T=C.argumentCallback?C.argumentCallback(B):B;return U[T]}}var f={narrow:["\u067E","\u062F"],abbreviated:["\u067E-\u0632","\u062F-\u0632"],wide:["\u067E\u06CE\u0634 \u0632\u0627\u06CC\u0646","\u062F\u0648\u0627\u06CC \u0632\u0627\u06CC\u0646"]},j={narrow:["1","2","3","4"],abbreviated:["\u06861\u0645","\u06862\u0645","\u06863\u0645","\u06864\u0645"],wide:["\u0686\u0627\u0631\u06D5\u06AF\u06CC \u06CC\u06D5\u06A9\u06D5\u0645","\u0686\u0627\u0631\u06D5\u06AF\u06CC \u062F\u0648\u0648\u06D5\u0645","\u0686\u0627\u0631\u06D5\u06AF\u06CC \u0633\u06CE\u06CC\u06D5\u0645","\u0686\u0627\u0631\u06D5\u06AF\u06CC \u0686\u0648\u0627\u0631\u06D5\u0645"]},v={narrow:["\u06A9-\u062F","\u0634","\u0626\u0627","\u0646","\u0645","\u062D","\u062A","\u0626\u0627","\u0626\u06D5","\u062A\u0634-\u06CC","\u062A\u0634-\u062F","\u06A9-\u06CC"],abbreviated:["\u06A9\u0627\u0646-\u062F\u0648\u0648","\u0634\u0648\u0628","\u0626\u0627\u062F","\u0646\u06CC\u0633","\u0645\u0627\u06CC\u0633","\u062D\u0648\u0632","\u062A\u06D5\u0645","\u0626\u0627\u0628","\u0626\u06D5\u0644","\u062A\u0634-\u06CC\u06D5\u06A9","\u062A\u0634-\u062F\u0648\u0648","\u06A9\u0627\u0646-\u06CC\u06D5\u06A9"],wide:["\u06A9\u0627\u0646\u0648\u0648\u0646\u06CC \u062F\u0648\u0648\u06D5\u0645","\u0634\u0648\u0628\u0627\u062A","\u0626\u0627\u062F\u0627\u0631","\u0646\u06CC\u0633\u0627\u0646","\u0645\u0627\u06CC\u0633","\u062D\u0648\u0632\u06D5\u06CC\u0631\u0627\u0646","\u062A\u06D5\u0645\u0645\u0648\u0632","\u0626\u0627\u0628","\u0626\u06D5\u06CC\u0644\u0648\u0644","\u062A\u0634\u0631\u06CC\u0646\u06CC \u06CC\u06D5\u06A9\u06D5\u0645","\u062A\u0634\u0631\u06CC\u0646\u06CC \u062F\u0648\u0648\u06D5\u0645","\u06A9\u0627\u0646\u0648\u0648\u0646\u06CC \u06CC\u06D5\u06A9\u06D5\u0645"]},w={narrow:["\u06CC-\u0634","\u062F-\u0634","\u0633-\u0634","\u0686-\u0634","\u067E-\u0634","\u0647\u06D5","\u0634"],short:["\u06CC\u06D5-\u0634\u06D5","\u062F\u0648\u0648-\u0634\u06D5","\u0633\u06CE-\u0634\u06D5","\u0686\u0648-\u0634\u06D5","\u067E\u06CE-\u0634\u06D5","\u0647\u06D5\u06CC","\u0634\u06D5"],abbreviated:["\u06CC\u06D5\u06A9-\u0634\u06D5\u0645","\u062F\u0648\u0648-\u0634\u06D5\u0645","\u0633\u06CE-\u0634\u06D5\u0645","\u0686\u0648\u0627\u0631-\u0634\u06D5\u0645","\u067E\u06CE\u0646\u062C-\u0634\u06D5\u0645","\u0647\u06D5\u06CC\u0646\u06CC","\u0634\u06D5\u0645\u06D5"],wide:["\u06CC\u06D5\u06A9 \u0634\u06D5\u0645\u06D5","\u062F\u0648\u0648 \u0634\u06D5\u0645\u06D5","\u0633\u06CE \u0634\u06D5\u0645\u06D5","\u0686\u0648\u0627\u0631 \u0634\u06D5\u0645\u06D5","\u067E\u06CE\u0646\u062C \u0634\u06D5\u0645\u06D5","\u0647\u06D5\u06CC\u0646\u06CC","\u0634\u06D5\u0645\u06D5"]},F={narrow:{am:"\u067E",pm:"\u062F",midnight:"\u0646-\u0634",noon:"\u0646",morning:"\u0628\u06D5\u06CC\u0627\u0646\u06CC",afternoon:"\u062F\u0648\u0627\u06CC \u0646\u06CC\u0648\u06D5\u0695\u06C6",evening:"\u0626\u06CE\u0648\u0627\u0631\u06D5",night:"\u0634\u06D5\u0648"},abbreviated:{am:"\u067E-\u0646",pm:"\u062F-\u0646",midnight:"\u0646\u06CC\u0648\u06D5 \u0634\u06D5\u0648",noon:"\u0646\u06CC\u0648\u06D5\u0695\u06C6",morning:"\u0628\u06D5\u06CC\u0627\u0646\u06CC",afternoon:"\u062F\u0648\u0627\u06CC \u0646\u06CC\u0648\u06D5\u0695\u06C6",evening:"\u0626\u06CE\u0648\u0627\u0631\u06D5",night:"\u0634\u06D5\u0648"},wide:{am:"\u067E\u06CE\u0634 \u0646\u06CC\u0648\u06D5\u0695\u06C6",pm:"\u062F\u0648\u0627\u06CC \u0646\u06CC\u0648\u06D5\u0695\u06C6",midnight:"\u0646\u06CC\u0648\u06D5 \u0634\u06D5\u0648",noon:"\u0646\u06CC\u0648\u06D5\u0695\u06C6",morning:"\u0628\u06D5\u06CC\u0627\u0646\u06CC",afternoon:"\u062F\u0648\u0627\u06CC \u0646\u06CC\u0648\u06D5\u0695\u06C6",evening:"\u0626\u06CE\u0648\u0627\u0631\u06D5",night:"\u0634\u06D5\u0648"}},_={narrow:{am:"\u067E",pm:"\u062F",midnight:"\u0646-\u0634",noon:"\u0646",morning:"\u0644\u06D5 \u0628\u06D5\u06CC\u0627\u0646\u06CC\u062F\u0627",afternoon:"\u0644\u06D5 \u062F\u0648\u0627\u06CC \u0646\u06CC\u0648\u06D5\u0695\u06C6\u062F\u0627",evening:"\u0644\u06D5 \u0626\u06CE\u0648\u0627\u0631\u06D5\u062F\u0627",night:"\u0644\u06D5 \u0634\u06D5\u0648\u062F\u0627"},abbreviated:{am:"\u067E-\u0646",pm:"\u062F-\u0646",midnight:"\u0646\u06CC\u0648\u06D5 \u0634\u06D5\u0648",noon:"\u0646\u06CC\u0648\u06D5\u0695\u06C6",morning:"\u0644\u06D5 \u0628\u06D5\u06CC\u0627\u0646\u06CC\u062F\u0627",afternoon:"\u0644\u06D5 \u062F\u0648\u0627\u06CC \u0646\u06CC\u0648\u06D5\u0695\u06C6\u062F\u0627",evening:"\u0644\u06D5 \u0626\u06CE\u0648\u0627\u0631\u06D5\u062F\u0627",night:"\u0644\u06D5 \u0634\u06D5\u0648\u062F\u0627"},wide:{am:"\u067E\u06CE\u0634 \u0646\u06CC\u0648\u06D5\u0695\u06C6",pm:"\u062F\u0648\u0627\u06CC \u0646\u06CC\u0648\u06D5\u0695\u06C6",midnight:"\u0646\u06CC\u0648\u06D5 \u0634\u06D5\u0648",noon:"\u0646\u06CC\u0648\u06D5\u0695\u06C6",morning:"\u0644\u06D5 \u0628\u06D5\u06CC\u0627\u0646\u06CC\u062F\u0627",afternoon:"\u0644\u06D5 \u062F\u0648\u0627\u06CC \u0646\u06CC\u0648\u06D5\u0695\u06C6\u062F\u0627",evening:"\u0644\u06D5 \u0626\u06CE\u0648\u0627\u0631\u06D5\u062F\u0627",night:"\u0644\u06D5 \u0634\u06D5\u0648\u062F\u0627"}},P=function C(B,X){return String(B)},b={ordinalNumber:P,era:q({values:f,defaultWidth:"wide"}),quarter:q({values:j,defaultWidth:"wide",argumentCallback:function C(B){return B-1}}),month:q({values:v,defaultWidth:"wide"}),day:q({values:w,defaultWidth:"wide"}),dayPeriod:q({values:F,defaultWidth:"wide",formattingValues:_,defaultFormattingWidth:"wide"})};function D(C){return function(B){var X=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},E=X.width,U=E&&C.matchPatterns[E]||C.matchPatterns[C.defaultMatchWidth],Y=B.match(U);if(!Y)return null;var A=Y[0],I=E&&C.parsePatterns[E]||C.parsePatterns[C.defaultParseWidth],Q=Array.isArray(I)?k(I,function(N){return N.test(A)}):u(I,function(N){return N.test(A)}),T;T=C.valueCallback?C.valueCallback(Q):Q,T=X.valueCallback?X.valueCallback(T):T;var t=B.slice(A.length);return{value:T,rest:t}}}var u=function C(B,X){for(var E in B)if(Object.prototype.hasOwnProperty.call(B,E)&&X(B[E]))return E;return},k=function C(B,X){for(var E=0;E<B.length;E++)if(X(B[E]))return E;return};function h(C){return function(B){var X=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},E=B.match(C.matchPattern);if(!E)return null;var U=E[0],Y=B.match(C.parsePattern);if(!Y)return null;var A=C.valueCallback?C.valueCallback(Y[0]):Y[0];A=X.valueCallback?X.valueCallback(A):A;var I=B.slice(U.length);return{value:A,rest:I}}}var m=/^(\d+)(th|st|nd|rd)?/i,c=/\d+/i,y={narrow:/^(پ|د)/i,abbreviated:/^(پ-ز|د.ز)/i,wide:/^(پێش زاین| دوای زاین)/i},p={any:[/^د/g,/^پ/g]},d={narrow:/^[1234]/i,abbreviated:/^م[1234]چ/i,wide:/^(یەکەم|دووەم|سێیەم| چوارەم) (چارەگی)? quarter/i},g={wide:[/چارەگی یەکەم/,/چارەگی دووەم/,/چارەگی سيیەم/,/چارەگی چوارەم/],any:[/1/i,/2/i,/3/i,/4/i]},l={narrow:/^(ک-د|ش|ئا|ن|م|ح|ت|ئە|تش-ی|تش-د|ک-ی)/i,abbreviated:/^(کان-دوو|شوب|ئاد|نیس|مایس|حوز|تەم|ئاب|ئەل|تش-یەک|تش-دوو|کان-یەک)/i,wide:/^(کانوونی دووەم|شوبات|ئادار|نیسان|مایس|حوزەیران|تەمموز|ئاب|ئەیلول|تشرینی یەکەم|تشرینی دووەم|کانوونی یەکەم)/i},i={narrow:[/^ک-د/i,/^ش/i,/^ئا/i,/^ن/i,/^م/i,/^ح/i,/^ت/i,/^ئا/i,/^ئە/i,/^تش-ی/i,/^تش-د/i,/^ک-ی/i],any:[/^کان-دوو/i,/^شوب/i,/^ئاد/i,/^نیس/i,/^مایس/i,/^حوز/i,/^تەم/i,/^ئاب/i,/^ئەل/i,/^تش-یەک/i,/^تش-دوو/i,/^|کان-یەک/i]},n={narrow:/^(ش|ی|د|س|چ|پ|هە)/i,short:/^(یە-شە|دوو-شە|سێ-شە|چو-شە|پێ-شە|هە|شە)/i,abbreviated:/^(یەک-شەم|دوو-شەم|سێ-شەم|چوار-شەم|پێنخ-شەم|هەینی|شەمە)/i,wide:/^(یەک شەمە|دوو شەمە|سێ شەمە|چوار شەمە|پێنج شەمە|هەینی|شەمە)/i},s={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},o={narrow:/^(پ|د|ن-ش|ن| (بەیانی|دوای نیوەڕۆ|ئێوارە|شەو))/i,abbreviated:/^(پ-ن|د-ن|نیوە شەو|نیوەڕۆ|بەیانی|دوای نیوەڕۆ|ئێوارە|شەو)/,wide:/^(پێش نیوەڕۆ|دوای نیوەڕۆ|نیوەڕۆ|نیوە شەو|لەبەیانیدا|لەدواینیوەڕۆدا|لە ئێوارەدا|لە شەودا)/,any:/^(پ|د|بەیانی|نیوەڕۆ|ئێوارە|شەو)/},r={any:{am:/^د/i,pm:/^پ/i,midnight:/^ن-ش/i,noon:/^ن/i,morning:/بەیانی/i,afternoon:/دواینیوەڕۆ/i,evening:/ئێوارە/i,night:/شەو/i}},e={ordinalNumber:h({matchPattern:m,parsePattern:c,valueCallback:function C(B){return parseInt(B,10)}}),era:D({matchPatterns:y,defaultMatchWidth:"wide",parsePatterns:p,defaultParseWidth:"any"}),quarter:D({matchPatterns:d,defaultMatchWidth:"wide",parsePatterns:g,defaultParseWidth:"any",valueCallback:function C(B){return B+1}}),month:D({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any"}),day:D({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),dayPeriod:D({matchPatterns:o,defaultMatchWidth:"any",parsePatterns:r,defaultParseWidth:"any"})},a={code:"ckb",formatDistance:W,formatLong:L,formatRelative:V,localize:b,match:e,options:{weekStartsOn:0,firstWeekContainsDate:1}};window.dateFns=K(K({},window.dateFns),{},{locale:K(K({},(H=window.dateFns)===null||H===void 0?void 0:H.locale),{},{ckb:a})})})();

//# debugId=DEE0697FD7367A1264756e2164756e21
