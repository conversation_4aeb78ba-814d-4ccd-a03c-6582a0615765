@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(0, 0%, 100%);
  --foreground: hsl(24, 9.8%, 10%);
  --muted: hsl(37, 16%, 93%);
  --muted-foreground: hsl(25, 5.3%, 44.7%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(24, 9.8%, 10%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(24, 9.8%, 10%);
  --border: hsl(37, 16%, 90%);
  --input: hsl(37, 16%, 90%);
  --primary: hsl(42, 87%, 55%);
  --primary-foreground: hsl(0, 0%, 100%);
  --secondary: hsl(37, 16%, 93%);
  --secondary-foreground: hsl(24, 9.8%, 10%);
  --accent: hsl(37, 16%, 93%);
  --accent-foreground: hsl(24, 9.8%, 10%);
  --destructive: hsl(0, 84.2%, 60.2%);
  --destructive-foreground: hsl(60, 9.1%, 97.8%);
  --ring: hsl(24, 9.8%, 10%);
  --radius: 0.5rem;

  /* Luxury color palette */
  --charcoal: hsl(0, 0%, 17%);
  --warm-white: hsl(36, 33%, 97%);
  --beige: hsl(35, 24%, 87%);
  --gold: hsl(42, 87%, 55%);
  --bronze: hsl(33, 24%, 55%);
  --medium-gray: hsl(0, 0%, 42%);
}

.dark {
  --background: hsl(240, 10%, 3.9%);
  --foreground: hsl(0, 0%, 98%);
  --muted: hsl(240, 3.7%, 15.9%);
  --muted-foreground: hsl(240, 5%, 64.9%);
  --popover: hsl(240, 10%, 3.9%);
  --popover-foreground: hsl(0, 0%, 98%);
  --card: hsl(240, 10%, 3.9%);
  --card-foreground: hsl(0, 0%, 98%);
  --border: hsl(240, 3.7%, 15.9%);
  --input: hsl(240, 3.7%, 15.9%);
  --primary: hsl(42, 87%, 55%);
  --primary-foreground: hsl(0, 0%, 100%);
  --secondary: hsl(240, 3.7%, 15.9%);
  --secondary-foreground: hsl(0, 0%, 98%);
  --accent: hsl(240, 3.7%, 15.9%);
  --accent-foreground: hsl(0, 0%, 98%);
  --destructive: hsl(0, 62.8%, 30.6%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --ring: hsl(240, 4.9%, 83.9%);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  .font-playfair {
    font-family: 'Playfair Display', Georgia, serif;
  }
}

@layer components {
  .luxury-gradient {
    background: linear-gradient(135deg, var(--warm-white) 0%, var(--beige) 100%);
  }

  .gold-gradient {
    background: linear-gradient(135deg, var(--gold) 0%, var(--bronze) 100%);
  }

  .glass-effect {
    backdrop-filter: blur(10px);
    background: rgba(248, 246, 243, 0.9);
  }

  .elegant-shadow {
    box-shadow: 0 20px 60px rgba(44, 44, 44, 0.1);
  }

  .product-card {
    transition: all 0.3s ease-out;
  }

  .product-card:hover {
    transform: translateY(-8px);
  }

  .text-charcoal {
    color: var(--charcoal);
  }

  .text-medium-gray {
    color: var(--medium-gray);
  }

  .text-gold {
    color: var(--gold);
  }

  .text-bronze {
    color: var(--bronze);
  }

  .bg-charcoal {
    background-color: var(--charcoal);
  }

  .bg-warm-white {
    background-color: var(--warm-white);
  }

  .bg-beige {
    background-color: var(--beige);
  }

  .bg-gold {
    background-color: var(--gold);
  }

  .border-beige {
    border-color: var(--beige);
  }

  .border-gold {
    border-color: var(--gold);
  }

  .hover\:bg-gold:hover {
    background-color: var(--gold);
  }

  .hover\:text-gold:hover {
    color: var(--gold);
  }

  .hover\:border-gold:hover {
    border-color: var(--gold);
  }

  .focus\:ring-gold:focus {
    --tw-ring-color: var(--gold);
  }

  .focus\:border-gold:focus {
    border-color: var(--gold);
  }

  /* RTL Support */
  [dir='rtl'] {
    font-family: 'Noto Sans Arabic', 'Poppins', sans-serif;
  }

  [dir='rtl'] .font-playfair {
    font-family: 'Noto Sans Arabic', 'Playfair Display', serif;
  }

  /* RTL-specific spacing fixes */
  [dir='rtl'] .space-x-reverse > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 1;
  }

  /* RTL-aware flex layouts */
  [dir='rtl'] .rtl-flex-row {
    flex-direction: row-reverse;
  }

  [dir='ltr'] .rtl-flex-row {
    flex-direction: row;
  }

  /* RTL-aware text alignment */
  [dir='rtl'] .rtl-text-left {
    text-align: right;
  }

  [dir='ltr'] .rtl-text-left {
    text-align: left;
  }

  [dir='rtl'] .rtl-text-right {
    text-align: left;
  }

  [dir='ltr'] .rtl-text-right {
    text-align: right;
  }

  /* RTL-aware margins and padding */
  [dir='rtl'] .rtl-ml-auto {
    margin-right: auto;
    margin-left: unset;
  }

  [dir='ltr'] .rtl-ml-auto {
    margin-left: auto;
  }

  [dir='rtl'] .rtl-mr-auto {
    margin-left: auto;
    margin-right: unset;
  }

  [dir='ltr'] .rtl-mr-auto {
    margin-right: auto;
  }

  /* Prevent layout shift when scrollbar disappears */
  html {
    scrollbar-gutter: stable;
  }

  /* Prevent body scroll lock from affecting layout */
  body[data-scroll-locked] {
    padding-right: 0 !important;
    margin-right: 0 !important;
  }

  /* Ensure navbar doesn't shift when dropdown opens */
  nav[class*='fixed'] {
    padding-right: 0 !important;
    margin-right: 0 !important;
  }
}

@layer utilities {
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes slideUp {
    from {
      transform: translateY(20px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }
}
