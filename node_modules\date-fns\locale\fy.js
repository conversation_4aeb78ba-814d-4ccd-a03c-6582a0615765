"use strict";
exports.fy = void 0;
var _index = require("./fy/_lib/formatDistance.js");
var _index2 = require("./fy/_lib/formatLong.js");
var _index3 = require("./fy/_lib/formatRelative.js");
var _index4 = require("./fy/_lib/localize.js");
var _index5 = require("./fy/_lib/match.js");

/**
 * @category Locales
 * @summary Western Frisian locale (Netherlands).
 * @language West Frisian
 * @iso-639-2 fry
 * <AUTHOR> [@damon02](https://github.com/damon02)
 */
const fy = (exports.fy = {
  code: "fy",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
});
