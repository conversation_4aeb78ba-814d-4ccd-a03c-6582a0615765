import { useState } from 'react';
import { useLanguage } from '@/hooks/use-language';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Phone, Send, MessageSquare, Building, ChevronLeft, ChevronRight } from 'lucide-react';

export default function Contact() {
  const { t } = useLanguage();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: '',
    inquiryType: '',
  });
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  const stoneStoreImages = [
    {
      src: 'https://images.unsplash.com/photo-1565043666747-69f6646db940?auto=format&fit=crop&w=800&q=80',
      alt: 'Luxury marble showroom with elegant displays',
      title: 'Premium Marble Collection',
    },
    {
      src: 'https://images.unsplash.com/photo-1634712282287-14ed57b9cc89?auto=format&fit=crop&w=800&q=80',
      alt: 'Granite stone slabs in modern showroom',
      title: 'Granite Excellence',
    },
    {
      src: 'https://images.unsplash.com/photo-1600298881974-6be191ceeda1?auto=format&fit=crop&w=800&q=80',
      alt: 'Travertine tiles in sophisticated display',
      title: 'Travertine Elegance',
    },
    {
      src: 'https://images.unsplash.com/photo-1584622650111-993a426fbf0a?auto=format&fit=crop&w=800&q=80',
      alt: 'Ceramic tiles showcase in luxury setting',
      title: 'Ceramic Innovation',
    },
    {
      src: 'https://images.unsplash.com/photo-1634712282287-14ed57b9cc89?auto=format&fit=crop&w=800&q=80',
      alt: 'Stone showroom interior with premium materials',
      title: 'Showroom Experience',
    },
  ];

  const nextImage = () => {
    setCurrentImageIndex(prev => (prev + 1) % stoneStoreImages.length);
  };

  const prevImage = () => {
    setCurrentImageIndex(prev => (prev - 1 + stoneStoreImages.length) % stoneStoreImages.length);
  };

  const goToImage = (index: number) => {
    setCurrentImageIndex(index);
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission logic here
    console.log('Form submitted:', formData);
  };

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative min-h-[60vh] flex items-center justify-center pt-20 overflow-hidden">
        <div className="absolute inset-0 z-0">
          <div className="w-full h-full">
            <img
              src="https://images.unsplash.com/photo-1565043666747-69f6646db940?auto=format&fit=crop&w=1920&q=80"
              alt="Luxury stone showroom contact"
              className="w-full h-full object-cover opacity-20 transform scale-105"
              width={1920}
              height={1080}
              loading="eager"
              decoding="async"
            />
          </div>
          <div className="absolute inset-0 luxury-gradient opacity-95"></div>
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-6 lg:px-8">
          <div className="text-center">
            <h1 className="font-playfair text-5xl lg:text-7xl font-bold text-charcoal leading-tight mb-6">
              {t('contact.title') || 'Get in Touch'}
            </h1>
            <p className="text-2xl lg:text-3xl text-gold font-medium mb-8">
              {t('contact.subtitle') || "Let's Create Something Beautiful Together"}
            </p>
            <p className="text-xl text-medium-gray leading-relaxed max-w-4xl mx-auto">
              {t('contact.heroDescription') ||
                'Ready to transform your space with premium natural stones and ceramics? Our expert team is here to help you bring your vision to life.'}
            </p>
          </div>
        </div>
      </section>

      {/* Contact Form & Information Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16">
            {/* Contact Form */}
            <div>
              <div className="mb-8">
                <h2 className="font-playfair text-3xl lg:text-4xl font-bold text-charcoal mb-4">
                  {t('contact.formTitle') || 'Send Us a Message'}
                </h2>
                <p className="text-medium-gray text-lg">
                  {t('contact.formDescription') || "Fill out the form below and we'll get back to you within 24 hours."}
                </p>
              </div>

              <Card className="elegant-shadow border-0">
                <CardContent className="p-8">
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid md:grid-cols-2 gap-6">
                      <div>
                        <Label htmlFor="name" className="text-charcoal font-medium mb-2 block">
                          {t('contact.name') || 'Full Name'}
                        </Label>
                        <Input
                          id="name"
                          type="text"
                          value={formData.name}
                          onChange={e => handleInputChange('name', e.target.value)}
                          className="border-beige focus:border-gold focus:ring-gold"
                          placeholder={t('contact.namePlaceholder') || 'Enter your full name'}
                          required
                        />
                      </div>
                      <div>
                        <Label htmlFor="email" className="text-charcoal font-medium mb-2 block">
                          {t('contact.email') || 'Email Address'}
                        </Label>
                        <Input
                          id="email"
                          type="email"
                          value={formData.email}
                          onChange={e => handleInputChange('email', e.target.value)}
                          className="border-beige focus:border-gold focus:ring-gold"
                          placeholder={t('contact.emailPlaceholder') || 'Enter your email'}
                          required
                        />
                      </div>
                    </div>

                    <div className="grid md:grid-cols-2 gap-6">
                      <div>
                        <Label htmlFor="phone" className="text-charcoal font-medium mb-2 block">
                          {t('contact.phone') || 'Phone Number'}
                        </Label>
                        <Input
                          id="phone"
                          type="tel"
                          value={formData.phone}
                          onChange={e => handleInputChange('phone', e.target.value)}
                          className="border-beige focus:border-gold focus:ring-gold"
                          placeholder={t('contact.phonePlaceholder') || 'Enter your phone number'}
                        />
                      </div>
                      <div>
                        <Label htmlFor="inquiryType" className="text-charcoal font-medium mb-2 block">
                          {t('contact.inquiryType') || 'Inquiry Type'}
                        </Label>
                        <Select
                          value={formData.inquiryType}
                          onValueChange={value => handleInputChange('inquiryType', value)}>
                          <SelectTrigger className="border-beige focus:border-gold focus:ring-gold">
                            <SelectValue placeholder={t('contact.selectInquiryType') || 'Select inquiry type'} />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="quote">{t('contact.quoteRequest') || 'Quote Request'}</SelectItem>
                            <SelectItem value="consultation">
                              {t('contact.consultation') || 'Design Consultation'}
                            </SelectItem>
                            <SelectItem value="product">{t('contact.productInfo') || 'Product Information'}</SelectItem>
                            <SelectItem value="general">{t('contact.general') || 'General Inquiry'}</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="subject" className="text-charcoal font-medium mb-2 block">
                        {t('contact.subject') || 'Subject'}
                      </Label>
                      <Input
                        id="subject"
                        type="text"
                        value={formData.subject}
                        onChange={e => handleInputChange('subject', e.target.value)}
                        className="border-beige focus:border-gold focus:ring-gold"
                        placeholder={t('contact.subjectPlaceholder') || 'Enter subject'}
                        required
                      />
                    </div>

                    <div>
                      <Label htmlFor="message" className="text-charcoal font-medium mb-2 block">
                        {t('contact.message') || 'Message'}
                      </Label>
                      <Textarea
                        id="message"
                        value={formData.message}
                        onChange={e => handleInputChange('message', e.target.value)}
                        className="border-beige focus:border-gold focus:ring-gold min-h-[120px]"
                        placeholder={t('contact.messagePlaceholder') || 'Tell us about your project...'}
                        required
                      />
                    </div>

                    <Button
                      type="submit"
                      size="lg"
                      className="w-full gold-gradient text-white font-semibold hover:shadow-lg transform hover:scale-105 transition-all duration-300">
                      <Send className="mr-2 h-5 w-5" />
                      {t('contact.sendMessage') || 'Send Message'}
                    </Button>
                  </form>
                </CardContent>
              </Card>
            </div>

            {/* Image Slider */}
            <div>
              <div className="mb-8">
                <h2 className="font-playfair text-3xl lg:text-4xl font-bold text-charcoal mb-4">
                  {t('contact.showroomTitle') || 'Our Showroom'}
                </h2>
                <p className="text-medium-gray text-lg">
                  {t('contact.showroomDescription') ||
                    'Experience our premium collection in our state-of-the-art showroom.'}
                </p>
              </div>

              <Card className="elegant-shadow border-0 overflow-hidden">
                <CardContent className="p-0">
                  <div className="relative">
                    {/* Main Image */}
                    <div className="aspect-[4/3] relative overflow-hidden">
                      <img
                        src={stoneStoreImages[currentImageIndex].src}
                        alt={stoneStoreImages[currentImageIndex].alt}
                        className="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>

                      {/* Image Title Overlay */}
                      <div className="absolute bottom-4 left-4 right-4">
                        <h3 className="font-playfair text-xl font-semibold text-white">
                          {stoneStoreImages[currentImageIndex].title}
                        </h3>
                      </div>

                      {/* Navigation Arrows */}
                      <button
                        onClick={prevImage}
                        className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white p-2 rounded-full transition-all duration-300 hover:scale-110"
                        aria-label="Previous image">
                        <ChevronLeft className="h-6 w-6" />
                      </button>
                      <button
                        onClick={nextImage}
                        className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white p-2 rounded-full transition-all duration-300 hover:scale-110"
                        aria-label="Next image">
                        <ChevronRight className="h-6 w-6" />
                      </button>
                    </div>

                    {/* Thumbnail Navigation */}
                    <div className="p-4 bg-white">
                      <div className="flex gap-2 justify-center">
                        {stoneStoreImages.map((image, index) => (
                          <button
                            key={index}
                            onClick={() => goToImage(index)}
                            className={`w-16 h-12 rounded-lg overflow-hidden transition-all duration-300 ${
                              index === currentImageIndex
                                ? 'ring-2 ring-gold scale-110'
                                : 'hover:scale-105 opacity-70 hover:opacity-100'
                            }`}
                            aria-label={`Go to image ${index + 1}`}>
                            <img src={image.src} alt={image.alt} className="w-full h-full object-cover" />
                          </button>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Map Section */}
      <section className="py-20 luxury-gradient">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="font-playfair text-3xl lg:text-4xl font-bold text-charcoal mb-4">
              {t('contact.mapTitle') || 'Visit Our Showroom'}
            </h2>
            <p className="text-medium-gray text-lg max-w-2xl mx-auto">
              {t('contact.mapDescription') ||
                'Experience our premium collection in person at our state-of-the-art showroom.'}
            </p>
          </div>

          <Card className="elegant-shadow border-0 overflow-hidden">
            <CardContent className="p-0">
              <div className="aspect-video bg-gray-200 flex items-center justify-center">
                <div className="text-center">
                  <Building className="h-16 w-16 text-medium-gray mx-auto mb-4" />
                  <p className="text-medium-gray text-lg">
                    {t('contact.mapPlaceholder') || 'Interactive map will be displayed here'}
                  </p>
                  <p className="text-medium-gray text-sm mt-2">
                    {t('contact.mapNote') || 'Map integration can be added here'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-charcoal text-white">
        <div className="max-w-7xl mx-auto px-6 lg:px-8 text-center">
          <h2 className="font-playfair text-3xl lg:text-4xl font-bold mb-6">
            {t('contact.ctaTitle') || 'Ready to Start Your Project?'}
          </h2>
          <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
            {t('contact.ctaDescription') ||
              'Let our expert team help you choose the perfect materials for your space. Schedule a consultation today.'}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              size="lg"
              className="gold-gradient text-white font-semibold hover:shadow-lg transform hover:scale-105 transition-all duration-300">
              <Phone className="mr-2 h-5 w-5" />
              {t('contact.callNow') || 'Call Now'}
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="border-gold text-gold hover:bg-gold hover:text-white font-semibold transition-all duration-300">
              <MessageSquare className="mr-2 h-5 w-5" />
              {t('contact.whatsapp') || 'WhatsApp'}
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
