"use strict";
exports.arDZ = void 0;
var _index = require("./ar-DZ/_lib/formatDistance.js");
var _index2 = require("./ar-DZ/_lib/formatLong.js");
var _index3 = require("./ar-DZ/_lib/formatRelative.js");
var _index4 = require("./ar-DZ/_lib/localize.js");
var _index5 = require("./ar-DZ/_lib/match.js");

/**
 * @category Locales
 * @summary Arabic locale (Algerian Arabic).
 * @language Algerian Arabic
 * @iso-639-2 ara
 * <AUTHOR> [@badre429](https://github.com/badre429)
 * <AUTHOR> [@elshahat](https://github.com/elshahat)
 */
const arDZ = (exports.arDZ = {
  code: "ar-DZ",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 0 /* Sunday */,
    firstWeekContainsDate: 1,
  },
});
