import { Link } from 'wouter';
import { useLanguage } from '@/hooks/use-language';
import { cn, rtlSpace } from '@/lib/utils';

interface NavItem {
  href: string;
  label: string;
}

interface NavbarDesktopMenuProps {
  navItems: NavItem[];
  currentLocation: string;
}

export function NavbarDesktopMenu({ navItems, currentLocation }: NavbarDesktopMenuProps) {
  const { isRTL } = useLanguage();

  return (
    <div className={cn('hidden lg:flex items-center', rtlSpace(isRTL, 'space-x-8'))}>
      {navItems.map(item => (
        <Link
          key={item.href}
          href={item.href}
          className={`transition-colors duration-300 font-medium ${
            currentLocation === item.href ? 'text-gold' : 'text-charcoal hover:text-gold'
          }`}>
          {item.label}
        </Link>
      ))}
    </div>
  );
}
